#pragma once

#include "NPC.h"
#include <vector>
#include <unordered_map>

namespace MirServer {

// 任务信息结构
struct QuestInfo {
    WORD questId = 0;                   // 任务ID
    std::string questName;              // 任务名称
    std::string description;            // 任务描述
    BYTE minLevel = 1;                  // 最低等级要求
    BYTE maxLevel = 255;                // 最高等级要求
    std::vector<WORD> requiredItems;    // 需要的物品
    std::vector<WORD> rewardItems;      // 奖励物品
    DWORD rewardExp = 0;                // 奖励经验
    DWORD rewardGold = 0;               // 奖励金币
    bool repeatable = false;            // 是否可重复
};

// 任务NPC类
class QuestNPC : public NPC {
public:
    QuestNPC();
    virtual ~QuestNPC();

    // 重写基类方法
    virtual void Initialize() override;
    
    // 任务管理
    void AddQuest(const QuestInfo& quest);
    void RemoveQuest(WORD questId);
    const QuestInfo* GetQuest(WORD questId) const;
    std::vector<WORD> GetAvailableQuests(PlayObject* player) const;
    
    // 任务交互
    bool CanAcceptQuest(PlayObject* player, WORD questId) const;
    bool AcceptQuest(PlayObject* player, WORD questId);
    bool CanCompleteQuest(PlayObject* player, WORD questId) const;
    bool CompleteQuest(PlayObject* player, WORD questId);
    
    // 对话处理
    virtual void OnPlayerTalk(PlayObject* player) override;
    
protected:
    // 检查任务条件
    virtual bool CheckQuestRequirements(PlayObject* player, const QuestInfo& quest) const;
    virtual void GiveQuestRewards(PlayObject* player, const QuestInfo& quest);
    
private:
    std::unordered_map<WORD, QuestInfo> m_quests;  // 任务列表
};

} // namespace MirServer

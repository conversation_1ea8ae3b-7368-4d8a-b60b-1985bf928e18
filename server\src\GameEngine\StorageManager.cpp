#include "StorageManager.h"
#include "../BaseObject/PlayObject.h"
#include "../Common/Logger.h"
#include "../Protocol/PacketTypes.h"
#include <algorithm>
#include <sstream>
#include <iomanip>

namespace MirServer {

// 全局实例
std::unique_ptr<StorageManager> g_StorageManager = nullptr;

StorageManager::StorageManager() {
    Logger::Info("StorageManager created");
}

StorageManager::~StorageManager() {
    Finalize();
    Logger::Info("StorageManager destroyed");
}

bool StorageManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    Logger::Info("Initializing StorageManager...");

    // 初始化统计信息
    m_statistics = {};
    m_lastCleanupTime = GetCurrentTime();
    m_lastSaveTime = GetCurrentTime();

    m_initialized = true;
    Logger::Info("StorageManager initialized successfully");

    return true;
}

void StorageManager::Finalize() {
    if (!m_initialized) {
        return;
    }

    Logger::Info("Finalizing StorageManager...");

    // 保存所有仓库数据
    SaveAllStorageData();

    // 清理所有仓库数据
    {
        std::unique_lock<std::shared_mutex> lock(m_storagesMutex);
        m_storages.clear();
    }

    m_initialized = false;
    Logger::Info("StorageManager finalized");
}

bool StorageManager::OpenStorage(PlayObject* player, const std::string& password) {
    if (!player || !m_initialized) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_storagesMutex);

    StorageData* storage = GetStorageData(player);
    if (!storage) {
        storage = CreateStorageData(player);
        if (!storage) {
            Logger::Error("Failed to create storage for player: " + player->GetCharName());
            return false;
        }
    }

    // 如果仓库已经打开
    if (storage->isOpen) {
        return true;
    }

    // 验证密码
    if (HasStoragePassword(player)) {
        if (!VerifyStoragePassword(player, password)) {
            Logger::Warning("Invalid storage password for player: " + player->GetCharName());
            return false;
        }
    } else {
        // 首次使用，设置密码
        if (!password.empty()) {
            storage->password = EncryptPassword(password);
        }
    }

    // 打开仓库
    storage->isOpen = true;
    storage->lastAccessTime = GetCurrentTime();

    // 发送仓库物品列表给客户端
    player->SendDefMessage(Protocol::SM_STORAGE_OK, 0, 0, 0, 0);

    Logger::Info("Storage opened for player: " + player->GetCharName());
    return true;
}

bool StorageManager::CloseStorage(PlayObject* player) {
    if (!player || !m_initialized) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_storagesMutex);

    StorageData* storage = GetStorageData(player);
    if (!storage || !storage->isOpen) {
        return false;
    }

    // 关闭仓库
    storage->isOpen = false;

    // 保存仓库数据
    SaveStorageData(player);

    Logger::Info("Storage closed for player: " + player->GetCharName());
    return true;
}

bool StorageManager::IsStorageOpen(PlayObject* player) const {
    if (!player || !m_initialized) {
        return false;
    }

    std::shared_lock<std::shared_mutex> lock(m_storagesMutex);

    const StorageData* storage = GetStorageData(player);
    return storage && storage->isOpen;
}

bool StorageManager::StoreItem(PlayObject* player, const UserItem& item) {
    if (!player || !m_initialized || !IsStorageOpen(player)) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_storagesMutex);

    StorageData* storage = GetStorageData(player);
    if (!storage || storage->items.size() >= static_cast<size_t>(storage->maxSlots)) {
        player->SendDefMessage(Protocol::SM_STORAGE_FULL, 0, 0, 0, 0);
        return false;
    }

    // 添加物品到仓库
    storage->items.push_back(item);
    storage->lastAccessTime = GetCurrentTime();

    // 通知客户端
    player->SendDefMessage(Protocol::SM_STORAGE_OK, 0, 0, 0, 0);

    Logger::Debug("Item stored in storage for player: " + player->GetCharName());
    return true;
}

bool StorageManager::TakeItem(PlayObject* player, WORD makeIndex, UserItem& outItem) {
    if (!player || !m_initialized || !IsStorageOpen(player)) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_storagesMutex);

    StorageData* storage = GetStorageData(player);
    if (!storage) {
        return false;
    }

    // 查找物品
    auto it = std::find_if(storage->items.begin(), storage->items.end(),
        [makeIndex](const UserItem& item) {
            return item.makeIndex == makeIndex;
        });

    if (it == storage->items.end()) {
        player->SendDefMessage(Protocol::SM_STORAGE_FAIL, 0, 0, 0, 0);
        return false;
    }

    // 取出物品
    outItem = *it;
    storage->items.erase(it);
    storage->lastAccessTime = GetCurrentTime();

    // 通知客户端
    player->SendDefMessage(Protocol::SM_STORAGE_OK, 0, 0, 0, 0);

    Logger::Debug("Item taken from storage for player: " + player->GetCharName());
    return true;
}

bool StorageManager::StoreGold(PlayObject* player, DWORD amount) {
    if (!player || !m_initialized || !IsStorageOpen(player) || amount == 0) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_storagesMutex);

    StorageData* storage = GetStorageData(player);
    if (!storage) {
        return false;
    }

    // 检查玩家是否有足够的金币
    if (player->GetGold() < amount) {
        return false;
    }

    // 转移金币
    player->DecGold(amount);
    storage->gold += amount;
    storage->lastAccessTime = GetCurrentTime();

    Logger::Debug("Gold stored in storage for player: " + player->GetCharName() +
                  ", amount: " + std::to_string(amount));
    return true;
}

bool StorageManager::TakeGold(PlayObject* player, DWORD amount) {
    if (!player || !m_initialized || !IsStorageOpen(player) || amount == 0) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_storagesMutex);

    StorageData* storage = GetStorageData(player);
    if (!storage || storage->gold < amount) {
        return false;
    }

    // 转移金币
    storage->gold -= amount;
    player->IncGold(amount);
    storage->lastAccessTime = GetCurrentTime();

    Logger::Debug("Gold taken from storage for player: " + player->GetCharName() +
                  ", amount: " + std::to_string(amount));
    return true;
}

const std::vector<UserItem>& StorageManager::GetStorageItems(PlayObject* player) const {
    static std::vector<UserItem> emptyItems;

    if (!player || !m_initialized) {
        return emptyItems;
    }

    std::shared_lock<std::shared_mutex> lock(m_storagesMutex);

    const StorageData* storage = GetStorageData(player);
    if (!storage) {
        return emptyItems;
    }

    return storage->items;
}

DWORD StorageManager::GetStorageGold(PlayObject* player) const {
    if (!player || !m_initialized) {
        return 0;
    }

    std::shared_lock<std::shared_mutex> lock(m_storagesMutex);

    const StorageData* storage = GetStorageData(player);
    return storage ? storage->gold : 0;
}

int StorageManager::GetStorageItemCount(PlayObject* player) const {
    if (!player || !m_initialized) {
        return 0;
    }

    std::shared_lock<std::shared_mutex> lock(m_storagesMutex);

    const StorageData* storage = GetStorageData(player);
    return storage ? static_cast<int>(storage->items.size()) : 0;
}

bool StorageManager::IsStorageFull(PlayObject* player) const {
    if (!player || !m_initialized) {
        return true;
    }

    std::shared_lock<std::shared_mutex> lock(m_storagesMutex);

    const StorageData* storage = GetStorageData(player);
    return storage ? (storage->items.size() >= static_cast<size_t>(storage->maxSlots)) : true;
}

bool StorageManager::SetStoragePassword(PlayObject* player, const std::string& newPassword) {
    if (!player || !m_initialized) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_storagesMutex);

    StorageData* storage = GetStorageData(player);
    if (!storage) {
        storage = CreateStorageData(player);
        if (!storage) {
            return false;
        }
    }

    storage->password = EncryptPassword(newPassword);
    SaveStorageData(player);

    Logger::Info("Storage password set for player: " + player->GetCharName());
    return true;
}

bool StorageManager::VerifyStoragePassword(PlayObject* player, const std::string& password) const {
    if (!player || !m_initialized) {
        return false;
    }

    std::shared_lock<std::shared_mutex> lock(m_storagesMutex);

    const StorageData* storage = GetStorageData(player);
    if (!storage || storage->password.empty()) {
        return true; // 没有密码时允许访问
    }

    return VerifyPassword(password, storage->password);
}

bool StorageManager::HasStoragePassword(PlayObject* player) const {
    if (!player || !m_initialized) {
        return false;
    }

    std::shared_lock<std::shared_mutex> lock(m_storagesMutex);

    const StorageData* storage = GetStorageData(player);
    return storage && !storage->password.empty();
}

bool StorageManager::SaveStorageData(PlayObject* player) {
    if (!player || !m_initialized) {
        return false;
    }

    std::shared_lock<std::shared_mutex> lock(m_storagesMutex);

    const StorageData* storage = GetStorageData(player);
    if (!storage) {
        return false;
    }

    return SaveStorageToDB(player->GetCharName(), *storage);
}

bool StorageManager::LoadStorageData(PlayObject* player) {
    if (!player || !m_initialized) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_storagesMutex);

    StorageData* storage = GetStorageData(player);
    if (!storage) {
        storage = CreateStorageData(player);
        if (!storage) {
            return false;
        }
    }

    return LoadStorageFromDB(player->GetCharName(), *storage);
}

void StorageManager::CleanupExpiredSessions() {
    DWORD currentTime = GetCurrentTime();

    if (currentTime - m_lastCleanupTime < 60000) { // 每分钟清理一次
        return;
    }

    std::unique_lock<std::shared_mutex> lock(m_storagesMutex);

    auto it = m_storages.begin();
    while (it != m_storages.end()) {
        if (it->second->isOpen &&
            currentTime - it->second->lastAccessTime > m_sessionTimeout) {

            // 保存数据并关闭会话
            SaveStorageToDB(it->first, *it->second);
            it->second->isOpen = false;

            Logger::Info("Storage session expired for player: " + it->first);
        }
        ++it;
    }

    m_lastCleanupTime = currentTime;
}

void StorageManager::SaveAllStorageData() {
    std::shared_lock<std::shared_mutex> lock(m_storagesMutex);

    for (const auto& pair : m_storages) {
        SaveStorageToDB(pair.first, *pair.second);
    }

    Logger::Info("All storage data saved");
}

void StorageManager::UpdateStatistics() {
    std::shared_lock<std::shared_mutex> lock(m_storagesMutex);

    m_statistics.totalStorages = static_cast<int>(m_storages.size());
    m_statistics.activeStorages = 0;
    m_statistics.totalItems = 0;
    m_statistics.totalGold = 0;

    for (const auto& pair : m_storages) {
        if (pair.second->isOpen) {
            m_statistics.activeStorages++;
        }
        m_statistics.totalItems += static_cast<int>(pair.second->items.size());
        m_statistics.totalGold += pair.second->gold;
    }

    m_statistics.lastUpdateTime = GetCurrentTime();
}

// 私有方法实现
StorageData* StorageManager::GetStorageData(PlayObject* player) const {
    if (!player) return nullptr;

    std::string key = GetStorageKey(player);
    auto it = m_storages.find(key);
    return (it != m_storages.end()) ? it->second.get() : nullptr;
}

StorageData* StorageManager::CreateStorageData(PlayObject* player) {
    if (!player) return nullptr;

    std::string key = GetStorageKey(player);
    auto storage = std::make_unique<StorageData>();
    storage->maxSlots = m_defaultMaxSlots;

    StorageData* result = storage.get();
    m_storages[key] = std::move(storage);

    // 尝试从数据库加载
    LoadStorageFromDB(key, *result);

    return result;
}

std::string StorageManager::GetStorageKey(PlayObject* player) const {
    return player ? player->GetCharName() : "";
}

bool StorageManager::ValidateStorageAccess(PlayObject* player) const {
    return player && m_initialized;
}

bool StorageManager::LoadStorageFromDB(const std::string& playerName, StorageData& storage) {
    // TODO: 实现从数据库加载仓库数据
    // 这里应该连接数据库并加载玩家的仓库数据
    Logger::Debug("Loading storage data from DB for player: " + playerName);
    return true;
}

bool StorageManager::SaveStorageToDB(const std::string& playerName, const StorageData& storage) {
    // TODO: 实现保存仓库数据到数据库
    // 这里应该连接数据库并保存玩家的仓库数据
    Logger::Debug("Saving storage data to DB for player: " + playerName);
    return true;
}

std::string StorageManager::EncryptPassword(const std::string& password) const {
    // 简单的密码加密（实际项目中应使用更安全的加密方法）
    std::stringstream ss;
    for (char c : password) {
        ss << std::hex << std::setw(2) << std::setfill('0') << (static_cast<unsigned char>(c) ^ 0xAA);
    }
    return ss.str();
}

bool StorageManager::VerifyPassword(const std::string& password, const std::string& encrypted) const {
    return EncryptPassword(password) == encrypted;
}

} // namespace MirServer

#pragma once

#include "Types.h"
#include <string>
#include <vector>
#include <cmath>
#include <unordered_map>
#include <memory>
#include <chrono>

namespace MirServer {

// 时间相关工具函数
DWORD GetCurrentTime();
std::string GetTimeString();
std::string GetDateTimeString();

// 随机数生成
int GenerateRandom(int min, int max);
float GenerateRandomFloat(float min, float max);
bool GenerateRandomBool(float probability = 0.5f); // 概率0.0-1.0

// 字符串工具函数
std::string Trim(const std::string& str);
std::string ToUpper(const std::string& str);
std::string ToLower(const std::string& str);
std::vector<std::string> Split(const std::string& str, char delimiter);
std::string Join(const std::vector<std::string>& strings, const std::string& delimiter);
bool StartsWith(const std::string& str, const std::string& prefix);
bool EndsWith(const std::string& str, const std::string& suffix);

// 数学工具函数
double GetDistance(const Point& p1, const Point& p2);
bool IsInRange(const Point& center, const Point& target, int range);
DirectionType GetDirection(const Point& from, const Point& to);
DirectionType GetDirectionFromPoints(const Point& from, const Point& to);
Point GetNextPosition(const Point& current, DirectionType direction);

// 文件和路径工具
bool FileExists(const std::string& filename);
bool DirectoryExists(const std::string& dirname);
bool CreateDirectory(const std::string& dirname);
std::string GetFileExtension(const std::string& filename);
std::string GetFileName(const std::string& filepath);
std::string GetDirectoryName(const std::string& filepath);

// 编码转换
std::string AnsiToUtf8(const std::string& ansi);
std::string Utf8ToAnsi(const std::string& utf8);
std::wstring AnsiToWide(const std::string& ansi);
std::string WideToAnsi(const std::wstring& wide);

// 数据转换
std::string IntToString(int value);
std::string FloatToString(float value, int precision = 2);
int StringToInt(const std::string& str, int defaultValue = 0);
float StringToFloat(const std::string& str, float defaultValue = 0.0f);
bool StringToBool(const std::string& str, bool defaultValue = false);

// 哈希和校验
uint32_t CalculateCRC32(const void* data, size_t length);
std::string CalculateMD5(const std::string& data);
std::string CalculateSHA1(const std::string& data);

// 内存工具
void* SafeMemcpy(void* dest, const void* src, size_t count);
int SafeMemcmp(const void* buf1, const void* buf2, size_t count);
void SecureZeroMemory(void* ptr, size_t size);

// 网络工具
bool IsValidIPAddress(const std::string& ip);
std::string GetLocalIPAddress();
uint16_t GetAvailablePort(uint16_t startPort = 1024);

// 配置文件工具
class IniFile {
public:
    IniFile(const std::string& filename);
    ~IniFile();

    bool Load();
    bool Save();

    std::string ReadString(const std::string& section, const std::string& key, const std::string& defaultValue = "");
    int ReadInt(const std::string& section, const std::string& key, int defaultValue = 0);
    float ReadFloat(const std::string& section, const std::string& key, float defaultValue = 0.0f);
    bool ReadBool(const std::string& section, const std::string& key, bool defaultValue = false);

    void WriteString(const std::string& section, const std::string& key, const std::string& value);
    void WriteInt(const std::string& section, const std::string& key, int value);
    void WriteFloat(const std::string& section, const std::string& key, float value);
    void WriteBool(const std::string& section, const std::string& key, bool value);

private:
    std::string m_filename;
    std::unordered_map<std::string, std::unordered_map<std::string, std::string>> m_data;
};

// 性能计时器
class Timer {
public:
    Timer();
    void Start();
    void Stop();
    void Reset();
    double GetElapsedSeconds() const;
    double GetElapsedMilliseconds() const;
    bool IsRunning() const;

private:
    DWORD m_startTime;
    DWORD m_endTime;
    bool m_running;
};

// 简单的性能分析器
class Profiler {
public:
    static void BeginSample(const std::string& name);
    static void EndSample(const std::string& name);
    static void PrintResults();
    static void ClearResults();

private:
    struct Sample {
        std::string name;
        DWORD startTime;
        DWORD totalTime;
        int callCount;
    };

    static std::unordered_map<std::string, Sample> s_samples;
    static std::unordered_map<std::string, DWORD> s_activeSamples;
};

// RAII性能采样器
class ScopedProfiler {
public:
    ScopedProfiler(const std::string& name);
    ~ScopedProfiler();

private:
    std::string m_name;
};

// 宏定义
#define PROFILE_SCOPE(name) ScopedProfiler _prof(name)
#define PROFILE_FUNCTION() ScopedProfiler _prof(__FUNCTION__)

// 内存池
template<typename T>
class ObjectPool {
public:
    ObjectPool(size_t initialSize = 100) {
        for (size_t i = 0; i < initialSize; ++i) {
            m_pool.push_back(std::make_unique<T>());
        }
    }

    std::unique_ptr<T> Acquire() {
        if (m_pool.empty()) {
            return std::make_unique<T>();
        }

        auto obj = std::move(m_pool.back());
        m_pool.pop_back();
        return obj;
    }

    void Release(std::unique_ptr<T> obj) {
        if (obj && m_pool.size() < 1000) { // 限制池大小
            m_pool.push_back(std::move(obj));
        }
    }

    size_t Size() const { return m_pool.size(); }

private:
    std::vector<std::unique_ptr<T>> m_pool;
};

// 线程安全的单例模板
template<typename T>
class Singleton {
public:
    static T& GetInstance() {
        static T instance;
        return instance;
    }

protected:
    Singleton() = default;
    virtual ~Singleton() = default;

private:
    Singleton(const Singleton&) = delete;
    Singleton& operator=(const Singleton&) = delete;
};

} // namespace MirServer

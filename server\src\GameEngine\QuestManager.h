#pragma once

#include "../Common/Types.h"
#include "../Common/GameData.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <functional>

namespace MirServer {

class PlayObject;
class NPC;

// 任务状态枚举
enum class QuestState {
    NONE = 0,           // 无任务
    AVAILABLE = 1,      // 可接取
    ACCEPTED = 2,       // 已接取
    COMPLETED = 3,      // 已完成
    FINISHED = 4,       // 已交付
    FAILED = 5          // 已失败
};

// 任务类型枚举
enum class QuestType {
    KILL_MONSTER = 1,   // 杀怪任务
    COLLECT_ITEM = 2,   // 收集物品
    DELIVER_ITEM = 3,   // 送物品
    TALK_TO_NPC = 4,    // 对话任务
    REACH_LEVEL = 5,    // 达到等级
    EXPLORE_MAP = 6     // 探索地图
};

// 任务目标结构
struct QuestObjective {
    QuestType type;                 // 目标类型
    std::string target;             // 目标对象（怪物名、物品名、NPC名等）
    int requiredCount = 1;          // 需要数量
    int currentCount = 0;           // 当前数量
    std::string description;        // 描述
    bool completed = false;         // 是否完成
};

// 任务奖励结构
struct QuestReward {
    DWORD exp = 0;                  // 经验奖励
    DWORD gold = 0;                 // 金币奖励
    std::vector<UserItem> items;    // 物品奖励
    std::string description;        // 奖励描述
};

// 任务数据结构
struct QuestData {
    WORD questId;                   // 任务ID
    std::string name;               // 任务名称
    std::string description;        // 任务描述
    QuestType type;                 // 任务类型

    // 任务条件
    WORD minLevel = 1;              // 最低等级要求
    WORD maxLevel = 255;            // 最高等级要求
    JobType requiredJob = JobType::NONE; // 职业要求
    std::vector<WORD> prerequisiteQuests; // 前置任务

    // 任务目标
    std::vector<QuestObjective> objectives; // 任务目标列表

    // 任务奖励
    QuestReward reward;             // 任务奖励

    // 任务设置
    bool repeatable = false;        // 是否可重复
    DWORD timeLimit = 0;            // 时间限制（0表示无限制）
    std::string giveNPC;            // 发布任务的NPC
    std::string finishNPC;          // 完成任务的NPC

    // 脚本相关
    std::string startScript;        // 开始脚本
    std::string progressScript;     // 进度脚本
    std::string completeScript;     // 完成脚本
};

// 玩家任务状态
struct PlayerQuestStatus {
    WORD questId;                   // 任务ID
    QuestState state;               // 任务状态
    std::vector<QuestObjective> objectives; // 任务目标进度
    DWORD acceptTime = 0;           // 接取时间
    DWORD completeTime = 0;         // 完成时间
    int completedCount = 0;         // 完成次数（用于可重复任务）
};

// 任务管理器类（对应delphi的任务系统）
class QuestManager {
public:
    QuestManager();
    ~QuestManager();

    // 初始化和清理
    bool Initialize();
    void Finalize();

    // 任务数据管理
    bool LoadQuestData(const std::string& questFile);
    bool AddQuest(const QuestData& quest);
    const QuestData* GetQuest(WORD questId) const;
    std::vector<const QuestData*> GetQuestsByNPC(const std::string& npcName) const;

    // 玩家任务操作
    bool CanAcceptQuest(PlayObject* player, WORD questId) const;
    bool AcceptQuest(PlayObject* player, WORD questId);
    bool CompleteQuest(PlayObject* player, WORD questId);
    bool AbandonQuest(PlayObject* player, WORD questId);

    // 任务进度更新
    void UpdateQuestProgress(PlayObject* player, QuestType type, const std::string& target, int count = 1);
    void OnMonsterKilled(PlayObject* player, const std::string& monsterName);
    void OnItemCollected(PlayObject* player, const UserItem& item);
    void OnNPCTalk(PlayObject* player, const std::string& npcName);
    void OnLevelUp(PlayObject* player, WORD newLevel);
    void OnMapEnter(PlayObject* player, const std::string& mapName);

    // 任务查询
    std::vector<PlayerQuestStatus> GetPlayerQuests(PlayObject* player) const;
    PlayerQuestStatus* GetPlayerQuest(PlayObject* player, WORD questId) const;
    bool HasQuest(PlayObject* player, WORD questId) const;
    bool IsQuestCompleted(PlayObject* player, WORD questId) const;

    // NPC任务交互
    std::vector<WORD> GetAvailableQuests(PlayObject* player, const std::string& npcName) const;
    std::vector<WORD> GetCompletableQuests(PlayObject* player, const std::string& npcName) const;

    // 运行时更新
    void Run();
    void ProcessQuests();
    void CheckTimeouts();

    // 数据持久化
    bool SavePlayerQuests(PlayObject* player);
    bool LoadPlayerQuests(PlayObject* player);

    // 统计信息
    struct Statistics {
        int totalQuests = 0;
        int activeQuests = 0;
        int completedQuests = 0;
        int failedQuests = 0;
        DWORD lastUpdateTime = 0;
    };

    const Statistics& GetStatistics() const { return m_statistics; }
    void UpdateStatistics();

private:
    bool m_initialized = false;

    // 任务数据
    std::unordered_map<WORD, std::unique_ptr<QuestData>> m_quests;
    mutable std::shared_mutex m_questsMutex;

    // 玩家任务状态 (玩家名 -> 任务状态列表)
    std::unordered_map<std::string, std::vector<PlayerQuestStatus>> m_playerQuests;
    mutable std::shared_mutex m_playerQuestsMutex;

    // NPC任务映射 (NPC名 -> 任务ID列表)
    std::unordered_map<std::string, std::vector<WORD>> m_npcQuests;

    // 运行时状态
    DWORD m_lastProcessTime = 0;
    DWORD m_lastTimeoutCheckTime = 0;

    // 统计信息
    Statistics m_statistics;

    // 内部方法
    bool ValidateQuestData(const QuestData& quest) const;
    bool CheckQuestPrerequisites(PlayObject* player, const QuestData& quest) const;
    bool CheckQuestConditions(PlayObject* player, const QuestData& quest) const;
    void GiveQuestReward(PlayObject* player, const QuestReward& reward);
    void UpdateQuestObjective(PlayerQuestStatus& questStatus, QuestType type, const std::string& target, int count);
    bool IsQuestObjectiveCompleted(const QuestObjective& objective) const;
    bool AreAllObjectivesCompleted(const PlayerQuestStatus& questStatus) const;
    void NotifyQuestUpdate(PlayObject* player, const PlayerQuestStatus& questStatus);
    void NotifyQuestComplete(PlayObject* player, WORD questId);

    // 数据库操作
    bool LoadPlayerQuestsFromDB(const std::string& playerName, std::vector<PlayerQuestStatus>& quests);
    bool SavePlayerQuestsToDB(const std::string& playerName, const std::vector<PlayerQuestStatus>& quests);

    // 脚本执行
    bool ExecuteQuestScript(PlayObject* player, const std::string& script, WORD questId);
};

// 全局任务管理器实例
extern std::unique_ptr<QuestManager> g_QuestManager;

} // namespace MirServer

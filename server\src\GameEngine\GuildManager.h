#pragma once

#include "../Common/Types.h"
#include "../BaseObject/PlayObject.h"
#include <memory>
#include <unordered_map>
#include <map>
#include <vector>
#include <string>
#include <mutex>
#include <fstream>

namespace MirServer {

// 前向声明
class Guild;
class PlayObject;

// 行会职位枚举（对应原项目的职位系统）
enum class GuildRank : BYTE {
    CHIEF = 1,          // 会长
    VICE_CHIEF = 2,     // 副会长
    CAPTAIN = 3,        // 队长
    MEMBER = 99         // 普通成员
};

// 行会成员信息结构
struct GuildMember {
    std::string playerName;         // 玩家名称
    GuildRank rank = GuildRank::MEMBER;  // 职位
    std::string rankName;           // 职位名称
    DWORD joinTime = 0;             // 加入时间
    DWORD lastOnlineTime = 0;       // 最后在线时间
    bool isOnline = false;          // 是否在线
    PlayObject* playerObject = nullptr;  // 玩家对象指针（在线时有效）

    GuildMember() = default;
    GuildMember(const std::string& name, GuildRank r = GuildRank::MEMBER)
        : playerName(name), rank(r), joinTime(GetCurrentTime()) {}
};

// 行会战争信息结构
struct GuildWarInfo {
    std::string guild1;             // 行会1名称
    std::string guild2;             // 行会2名称
    DWORD startTime = 0;            // 开始时间
    DWORD duration = 0;             // 持续时间(毫秒)
    bool isActive = false;          // 是否激活

    GuildWarInfo() = default;
    GuildWarInfo(const std::string& g1, const std::string& g2, DWORD dur = 10800000) // 默认3小时
        : guild1(g1), guild2(g2), startTime(GetCurrentTime()), duration(dur), isActive(true) {}
};

// 行会联盟信息结构
struct GuildAllyInfo {
    std::string guildName;          // 联盟行会名称
    DWORD allyTime = 0;             // 结盟时间
    bool isActive = true;           // 是否有效

    GuildAllyInfo() = default;
    GuildAllyInfo(const std::string& name)
        : guildName(name), allyTime(GetCurrentTime()) {}
};

// 行会捐献记录结构
struct GuildDonationRecord {
    std::string playerName;         // 捐献者名称
    int goldAmount = 0;             // 捐献金币数量
    std::string itemName;           // 捐献物品名称
    int itemCount = 0;              // 捐献物品数量
    DWORD donationTime = 0;         // 捐献时间

    GuildDonationRecord() = default;
    GuildDonationRecord(const std::string& player, int gold, const std::string& item = "", int count = 0)
        : playerName(player), goldAmount(gold), itemName(item), itemCount(count), donationTime(GetCurrentTime()) {}
};

// 行会技能信息结构
struct GuildSkillInfo {
    std::string skillName;          // 技能名称
    int skillLevel = 0;             // 技能等级
    int maxLevel = 10;              // 最大等级
    int requiredGold = 0;           // 升级所需金币
    std::string description;        // 技能描述

    GuildSkillInfo() = default;
    GuildSkillInfo(const std::string& name, int level = 0) : skillName(name), skillLevel(level) {}
};

// 行会领地信息结构
struct GuildTerritoryInfo {
    std::string territoryName;      // 领地名称
    int dailyIncome = 0;            // 每日收入
    DWORD claimTime = 0;            // 占领时间
    bool isActive = true;           // 是否激活

    GuildTerritoryInfo() = default;
    GuildTerritoryInfo(const std::string& name, int income = 0)
        : territoryName(name), dailyIncome(income), claimTime(GetCurrentTime()) {}
};

// 行会类（对应原项目的TGuild）
class Guild {
public:
    Guild(const std::string& name);
    ~Guild();

    // 基本信息
    const std::string& GetGuildName() const { return m_guildName; }
    const std::string& GetChiefName() const;
    int GetMemberCount() const;
    bool IsFull() const;

    // 行会属性
    int GetBuildPoint() const { return m_buildPoint; }
    int GetAurae() const { return m_aurae; }
    int GetStability() const { return m_stability; }
    int GetFlourishing() const { return m_flourishing; }
    int GetChiefItemCount() const { return m_chiefItemCount; }
    int GetGuildLevel() const { return m_guildLevel; }
    int GetGuildExp() const { return m_guildExp; }
    int GetGuildGold() const { return m_guildGold; }
    int GetGuildRanking() const { return m_guildRanking; }

    void SetBuildPoint(int point);
    void SetAurae(int point);
    void SetStability(int point);
    void SetFlourishing(int point);
    void SetChiefItemCount(int count);
    void SetGuildLevel(int level);
    void SetGuildExp(int exp);
    void SetGuildGold(int gold);
    void SetGuildRanking(int ranking);

    // 成员管理
    bool AddMember(PlayObject* player, GuildRank rank = GuildRank::MEMBER);
    bool RemoveMember(const std::string& playerName);
    bool IsMember(const std::string& playerName) const;
    GuildMember* FindMember(const std::string& playerName);
    const std::vector<GuildMember>& GetMembers() const { return m_members; }

    // 职位管理
    bool UpdateMemberRank(const std::string& playerName, GuildRank newRank, const std::string& rankName);
    std::string GetRankName(const std::string& playerName) const;
    GuildRank GetMemberRank(const std::string& playerName) const;

    // 行会战争
    bool StartWar(Guild* targetGuild, DWORD duration = 10800000);
    bool EndWar(Guild* targetGuild);
    bool IsWarWith(Guild* guild) const;
    bool IsNotWarGuild(Guild* guild) const;
    const std::vector<GuildWarInfo>& GetActiveWars() const { return m_guildWars; }

    // 行会联盟
    bool AddAlly(Guild* guild);
    bool RemoveAlly(Guild* guild);
    bool IsAlly(Guild* guild) const;
    const std::vector<GuildAllyInfo>& GetAllies() const { return m_guildAllies; }

    // 公告系统
    void AddNotice(const std::string& notice);
    void ClearNotices();
    const std::vector<std::string>& GetNotices() const { return m_notices; }

    // 消息发送
    void SendGuildMessage(const std::string& message);
    void SendGuildNotice(const std::string& notice);

    // 文件操作
    bool LoadFromFile();
    bool SaveToFile();
    void BackupGuildFile();

    // 运行时处理
    void Run();
    void CheckSaveGuildFile();

    // 玩家上线/下线处理
    void OnPlayerLogin(PlayObject* player);
    void OnPlayerLogout(PlayObject* player);

    // 团队战相关
    void StartTeamFight();
    void EndTeamFight();
    void AddTeamFightMember(const std::string& playerName);
    void TeamFightWhoDead(const std::string& playerName);
    void TeamFightWhoWinPoint(const std::string& playerName, int point);

    bool IsTeamFightActive() const { return m_teamFightActive; }
    int GetContestPoint() const { return m_contestPoint; }

    // 行会信息设置
    bool SetGuildInfo(const std::string& chiefName);

    // 行会捐献系统
    bool DonateGold(PlayObject* player, int amount);
    bool DonateItem(PlayObject* player, const std::string& itemName, int count);
    int GetTotalDonation(const std::string& playerName) const;
    void AddDonationRecord(const std::string& playerName, int gold, const std::string& item = "", int count = 0);

    // 行会升级系统
    bool CanLevelUp() const;
    bool LevelUp();
    int GetRequiredExpForNextLevel() const;
    void AddGuildExp(int exp);

    // 行会排名系统
    void UpdateRanking();
    int CalculateRankingScore() const;
    void SetRankingInfo(int ranking, int score);

    // 行会仓库系统
    bool DepositItem(PlayObject* player, const std::string& itemName, int count);
    bool WithdrawItem(PlayObject* player, const std::string& itemName, int count);
    bool CanAccessWarehouse(PlayObject* player) const;
    std::vector<std::string> GetWarehouseItems() const;

    // 行会技能系统
    bool LearnGuildSkill(const std::string& skillName);
    bool UpgradeGuildSkill(const std::string& skillName);
    int GetGuildSkillLevel(const std::string& skillName) const;
    std::vector<std::string> GetAvailableSkills() const;

    // 行会领地系统
    bool ClaimTerritory(const std::string& territoryName);
    bool LoseTerritory(const std::string& territoryName);
    std::vector<std::string> GetTerritories() const;
    int GetTerritoryIncome() const;

    // 行会配置系统
    bool GetConfigBool(const std::string& key, bool defaultValue = false) const;
    int GetConfigInt(const std::string& key, int defaultValue = 0) const;
    std::string GetConfigString(const std::string& key, const std::string& defaultValue = "") const;
    void SetConfigBool(const std::string& key, bool value);
    void SetConfigInt(const std::string& key, int value);
    void SetConfigString(const std::string& key, const std::string& value);

private:
    // 内部方法
    void UpdateGuildFile();
    void RefreshMemberNames();
    void ClearRanks();

    // 文件操作
    bool LoadGuildFile(const std::string& fileName);
    bool LoadGuildConfig(const std::string& fileName);
    void SaveGuildFile(const std::string& fileName);
    void SaveGuildConfig(const std::string& fileName);
    void SetDefaultConfig();

private:
    std::string m_guildName;                    // 行会名称
    std::vector<GuildMember> m_members;         // 成员列表
    std::vector<std::string> m_notices;         // 公告列表
    std::vector<GuildWarInfo> m_guildWars;      // 战争列表
    std::vector<GuildAllyInfo> m_guildAllies;   // 联盟列表

    // 行会属性
    int m_buildPoint = 0;           // 建设度
    int m_aurae = 0;                // 灵气值
    int m_stability = 0;            // 安定度
    int m_flourishing = 0;          // 繁荣度
    int m_chiefItemCount = 0;       // 会长物品数量
    int m_guildLevel = 1;           // 行会等级
    int m_guildExp = 0;             // 行会经验
    int m_guildGold = 0;            // 行会金币
    int m_guildRanking = 0;         // 行会排名
    int m_rankingScore = 0;         // 排名分数

    // 团队战相关
    bool m_teamFightActive = false; // 团队战是否激活
    int m_contestPoint = 0;         // 比赛积分
    std::vector<std::string> m_teamFightDeadList; // 团队战死亡列表

    // 捐献系统
    std::map<std::string, int> m_memberDonations;      // 成员捐献记录
    std::vector<std::string> m_donationHistory;        // 捐献历史

    // 仓库系统
    std::map<std::string, int> m_warehouseItems;       // 仓库物品

    // 技能系统
    std::map<std::string, int> m_guildSkills;          // 行会技能等级

    // 领地系统
    std::vector<std::string> m_territories;            // 拥有的领地

    // 配置系统
    std::map<std::string, std::string> m_configValues; // 配置键值对

    // 状态管理
    bool m_changed = false;         // 是否有变化
    DWORD m_lastSaveTime = 0;       // 最后保存时间
    bool m_enableAuthAlly = false;  // 是否启用联盟认证

    // 线程安全
    mutable std::mutex m_mutex;

    // 配置参数
    static constexpr int MAX_GUILD_MEMBERS = 200;       // 最大成员数
    static constexpr int MAX_NOTICES = 10;              // 最大公告数
    static constexpr DWORD SAVE_INTERVAL = 30000;       // 保存间隔(毫秒)
    static constexpr DWORD DEFAULT_WAR_DURATION = 10800000; // 默认战争时间(3小时)
};

// 行会管理器类（对应原项目的TGuildManager）
class GuildManager {
public:
    static GuildManager& GetInstance();

    // 初始化和清理
    bool Initialize();
    void Finalize();

    // 行会管理
    bool CreateGuild(const std::string& guildName, PlayObject* chief);
    bool DeleteGuild(const std::string& guildName);
    Guild* FindGuild(const std::string& guildName);
    Guild* GetPlayerGuild(const std::string& playerName);

    // 行会排名系统
    void UpdateAllGuildRankings();
    std::vector<Guild*> GetGuildRankings(int count = 10);
    int GetGuildRank(const std::string& guildName);

    // 行会统计
    int GetTotalGuildCount() const;
    int GetTotalMemberCount() const;
    Guild* GetTopGuild() const;

    // 文件操作
    void LoadGuildInfo();
    void SaveGuildList();

    // 运行时处理
    void Run();

    // 获取行会列表
    const std::vector<std::unique_ptr<Guild>>& GetGuilds() const { return m_guilds; }

    // 清理
    void ClearGuildInfo();

private:
    GuildManager() = default;
    ~GuildManager() = default;
    GuildManager(const GuildManager&) = delete;
    GuildManager& operator=(const GuildManager&) = delete;

private:
    std::vector<std::unique_ptr<Guild>> m_guilds;   // 行会列表
    mutable std::mutex m_mutex;                     // 线程安全锁
    bool m_initialized = false;                     // 是否已初始化

    // 配置路径
    std::string m_guildListFile;                    // 行会列表文件
    std::string m_guildDir;                         // 行会目录
};

} // namespace MirServer

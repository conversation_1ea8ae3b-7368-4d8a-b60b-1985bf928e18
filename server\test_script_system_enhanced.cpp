#include "src/GameEngine/ScriptEngine.h"
#include "src/BaseObject/PlayObject.h"
#include "src/BaseObject/NPC.h"
#include "src/Common/Logger.h"
#include <iostream>
#include <cassert>

using namespace MirServer;

// 测试变量管理系统
void TestVariableManagement() {
    std::cout << "\n=== 测试变量管理系统 ===" << std::endl;
    
    ScriptEngine engine;
    
    // 测试设置变量
    bool result = engine.SetScriptVar("TestVar1", 100);
    assert(result == true);
    std::cout << "设置变量 TestVar1 = 100: " << (result ? "成功" : "失败") << std::endl;
    
    // 测试获取变量
    int value = engine.GetScriptVar("TestVar1");
    assert(value == 100);
    std::cout << "获取变量 TestVar1: " << value << std::endl;
    
    // 测试不存在的变量
    int nonExistValue = engine.GetScriptVar("NonExistVar");
    assert(nonExistValue == 0);
    std::cout << "获取不存在的变量: " << nonExistValue << std::endl;
    
    // 测试持久化变量
    result = engine.SetScriptVar("PersistentVar", 200, true);
    assert(result == true);
    std::cout << "设置持久化变量 PersistentVar = 200: " << (result ? "成功" : "失败") << std::endl;
    
    // 测试保存变量
    result = engine.SaveScriptVar("PersistentVar");
    assert(result == true);
    std::cout << "保存变量 PersistentVar: " << (result ? "成功" : "失败") << std::endl;
    
    // 测试加载变量
    result = engine.LoadScriptVar("LoadedVar");
    assert(result == true);
    std::cout << "加载变量 LoadedVar: " << (result ? "成功" : "失败") << std::endl;
    
    std::cout << "变量管理系统测试完成！" << std::endl;
}

// 测试列表管理系统
void TestListManagement() {
    std::cout << "\n=== 测试列表管理系统 ===" << std::endl;
    
    ScriptEngine engine;
    ListManager& listManager = engine.GetListManager();
    
    // 测试名单管理
    bool result = listManager.AddToNameList("VIPList", "Player1");
    assert(result == true);
    std::cout << "添加玩家到VIP名单: " << (result ? "成功" : "失败") << std::endl;
    
    bool inList = listManager.IsInNameList("VIPList", "Player1");
    assert(inList == true);
    std::cout << "检查玩家是否在VIP名单: " << (inList ? "是" : "否") << std::endl;
    
    result = listManager.RemoveFromNameList("VIPList", "Player1");
    assert(result == true);
    std::cout << "从VIP名单删除玩家: " << (result ? "成功" : "失败") << std::endl;
    
    inList = listManager.IsInNameList("VIPList", "Player1");
    assert(inList == false);
    std::cout << "再次检查玩家是否在VIP名单: " << (inList ? "是" : "否") << std::endl;
    
    // 测试IP列表管理
    result = listManager.AddToIPList("BanList", "*************");
    assert(result == true);
    std::cout << "添加IP到封禁列表: " << (result ? "成功" : "失败") << std::endl;
    
    inList = listManager.IsInIPList("BanList", "*************");
    assert(inList == true);
    std::cout << "检查IP是否在封禁列表: " << (inList ? "是" : "否") << std::endl;
    
    // 测试账号列表管理
    result = listManager.AddToAccountList("AdminList", "admin001");
    assert(result == true);
    std::cout << "添加账号到管理员列表: " << (result ? "成功" : "失败") << std::endl;
    
    inList = listManager.IsInAccountList("AdminList", "admin001");
    assert(inList == true);
    std::cout << "检查账号是否在管理员列表: " << (inList ? "是" : "否") << std::endl;
    
    std::cout << "列表管理系统测试完成！" << std::endl;
}

// 测试脚本条件检查
void TestScriptConditions() {
    std::cout << "\n=== 测试脚本条件检查 ===" << std::endl;
    
    ScriptEngine engine;
    
    // 创建测试玩家
    auto player = std::make_shared<PlayObject>();
    player->SetCharName("TestPlayer");
    player->SetLevel(50);
    player->SetGold(10000);
    
    // 测试变量条件检查
    engine.SetScriptVar("PlayerScore", 150);
    
    ScriptCondition varCondition("CHECKVAR", "PlayerScore");
    varCondition.nParam2 = 100;
    varCondition.method = '>';
    
    bool result = engine.CheckCondition(player.get(), nullptr, varCondition);
    assert(result == true);
    std::cout << "检查变量条件 PlayerScore > 100: " << (result ? "通过" : "不通过") << std::endl;
    
    // 测试名单条件检查
    engine.GetListManager().AddToNameList("VIPList", "TestPlayer");
    
    ScriptCondition nameListCondition("CHECKNAMELIST", "VIPList");
    result = engine.CheckCondition(player.get(), nullptr, nameListCondition);
    assert(result == true);
    std::cout << "检查名单条件 TestPlayer in VIPList: " << (result ? "通过" : "不通过") << std::endl;
    
    std::cout << "脚本条件检查测试完成！" << std::endl;
}

// 测试脚本动作执行
void TestScriptActions() {
    std::cout << "\n=== 测试脚本动作执行 ===" << std::endl;
    
    ScriptEngine engine;
    
    // 创建测试玩家
    auto player = std::make_shared<PlayObject>();
    player->SetCharName("TestPlayer");
    player->SetAccountName("testaccount");
    player->SetIPAddress("************");
    
    // 测试变量设置动作
    ScriptAction setVarAction("SETVAR", "TestScore");
    setVarAction.nParam2 = 500;
    
    bool result = engine.ExecuteAction(player.get(), nullptr, setVarAction);
    assert(result == true);
    std::cout << "执行SETVAR动作: " << (result ? "成功" : "失败") << std::endl;
    
    int varValue = engine.GetScriptVar("TestScore");
    assert(varValue == 500);
    std::cout << "验证变量值: " << varValue << std::endl;
    
    // 测试变量计算动作
    ScriptAction calcVarAction("CALCVAR", "TestScore", "+");
    calcVarAction.nParam3 = 100;
    
    result = engine.ExecuteAction(player.get(), nullptr, calcVarAction);
    assert(result == true);
    std::cout << "执行CALCVAR动作: " << (result ? "成功" : "失败") << std::endl;
    
    varValue = engine.GetScriptVar("TestScore");
    assert(varValue == 600);
    std::cout << "验证计算后的变量值: " << varValue << std::endl;
    
    // 测试名单添加动作
    ScriptAction addNameListAction("ADDNAMELIST", "VIPList");
    result = engine.ExecuteAction(player.get(), nullptr, addNameListAction);
    assert(result == true);
    std::cout << "执行ADDNAMELIST动作: " << (result ? "成功" : "失败") << std::endl;
    
    bool inList = engine.GetListManager().IsInNameList("VIPList", "TestPlayer");
    assert(inList == true);
    std::cout << "验证玩家是否在名单中: " << (inList ? "是" : "否") << std::endl;
    
    std::cout << "脚本动作执行测试完成！" << std::endl;
}

int main() {
    try {
        Logger::Info("开始测试增强的脚本系统...");
        
        TestVariableManagement();
        TestListManagement();
        TestScriptConditions();
        TestScriptActions();
        
        std::cout << "\n=== 所有测试通过！ ===" << std::endl;
        std::cout << "脚本系统增强功能验证成功！" << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}

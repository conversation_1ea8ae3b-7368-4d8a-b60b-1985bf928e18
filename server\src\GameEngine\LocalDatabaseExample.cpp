#include "LocalDatabase.h"
#include "UserEngine.h"
#include "../Database/SQLiteDatabase.h"
#include "../Common/Logger.h"
#include <iostream>
#include <memory>

using namespace MirServer;

// LocalDatabase使用示例
int main() {
    // 初始化日志系统
    auto logger = Logger::GetInstance();
    logger->SetLogLevel(Logger::LogLevel::INFO);
    
    std::cout << "=== LocalDatabase 使用示例 ===" << std::endl;
    
    try {
        // 1. 创建数据库连接
        std::cout << "\n1. 创建数据库连接..." << std::endl;
        auto database = std::make_unique<SQLiteDatabase>();
        if (!database->Connect("./GameData/mir.db")) {
            std::cerr << "Failed to connect to database" << std::endl;
            return -1;
        }
        std::cout << "数据库连接成功" << std::endl;
        
        // 2. 创建UserEngine（模拟）
        std::cout << "\n2. 创建UserEngine..." << std::endl;
        auto userEngine = std::make_unique<UserEngine>();
        // 注意：在真实环境中需要正确初始化UserEngine
        
        // 3. 创建并初始化LocalDatabase
        std::cout << "\n3. 初始化LocalDatabase..." << std::endl;
        g_LocalDatabase = std::make_unique<LocalDatabase>();
        
        // 设置数据加载回调
        g_LocalDatabase->SetDataLoadedCallback([](const std::string& dataType, bool success) {
            std::cout << "数据加载: " << dataType << " - " 
                      << (success ? "成功" : "失败") << std::endl;
        });
        
        if (!g_LocalDatabase->Initialize(std::move(database), userEngine.get())) {
            std::cerr << "LocalDatabase初始化失败" << std::endl;
            return -1;
        }
        std::cout << "LocalDatabase初始化成功" << std::endl;
        
        // 4. 从数据库加载数据
        std::cout << "\n4. 从数据库加载数据..." << std::endl;
        
        // 加载物品数据库
        if (g_LocalDatabase->LoadItemsDB()) {
            std::cout << "物品数据库加载成功" << std::endl;
        }
        
        // 加载魔法数据库
        if (g_LocalDatabase->LoadMagicDB()) {
            std::cout << "魔法数据库加载成功" << std::endl;
        }
        
        // 加载怪物数据库
        if (g_LocalDatabase->LoadMonsterDB()) {
            std::cout << "怪物数据库加载成功" << std::endl;
        }
        
        // 5. 从文件加载数据
        std::cout << "\n5. 从文件加载数据..." << std::endl;
        
        // 加载管理员列表
        if (g_LocalDatabase->LoadAdminList()) {
            std::cout << "管理员列表加载成功" << std::endl;
        }
        
        // 加载守卫列表
        if (g_LocalDatabase->LoadGuardList()) {
            std::cout << "守卫列表加载成功" << std::endl;
        }
        
        // 加载制作物品配置
        if (g_LocalDatabase->LoadMakeItem()) {
            std::cout << "制作物品配置加载成功" << std::endl;
        }
        
        // 加载地图事件
        if (g_LocalDatabase->LoadMapEvent()) {
            std::cout << "地图事件配置加载成功" << std::endl;
        }
        
        // 6. 查询缓存数据
        std::cout << "\n6. 查询缓存数据示例..." << std::endl;
        
        // 查询物品
        auto item = g_LocalDatabase->GetStdItem(0);
        if (item) {
            std::cout << "物品ID 0: " << item->name << " (类型: " << item->stdMode << ")" << std::endl;
        }
        
        // 按名称查询物品
        auto itemByName = g_LocalDatabase->GetStdItemByName("木剑");
        if (itemByName) {
            std::cout << "木剑: ID=" << itemByName->idx << ", 攻击=" 
                      << itemByName->dc.min << "-" << itemByName->dc.max << std::endl;
        }
        
        // 查询魔法
        auto magic = g_LocalDatabase->GetMagicInfo(1);
        if (magic) {
            std::cout << "魔法ID 1: " << magic->name << " (威力: " << magic->power << ")" << std::endl;
        }
        
        // 查询怪物
        auto monster = g_LocalDatabase->GetMonsterInfo("鸡");
        if (monster) {
            std::cout << "怪物-鸡: 等级=" << monster->level 
                      << ", 生命=" << monster->ability.HP << std::endl;
        }
        
        // 查询管理员
        auto admin = g_LocalDatabase->GetAdminInfo("Admin");
        if (admin) {
            std::cout << "管理员-Admin: 级别=" << admin->level 
                      << ", IP=" << admin->ipAddress << std::endl;
        }
        
        // 7. 获取统计信息
        std::cout << "\n7. 缓存统计信息..." << std::endl;
        auto stats = g_LocalDatabase->GetStatistics();
        std::cout << "标准物品数量: " << stats.stdItemCount << std::endl;
        std::cout << "魔法数量: " << stats.magicCount << std::endl;
        std::cout << "怪物数量: " << stats.monsterCount << std::endl;
        std::cout << "管理员数量: " << stats.adminCount << std::endl;
        std::cout << "商人数量: " << stats.merchantCount << std::endl;
        std::cout << "守卫数量: " << stats.guardCount << std::endl;
        std::cout << "缓存大小: " << g_LocalDatabase->GetCacheSize() << " 字节" << std::endl;
        std::cout << "总加载时间: " << stats.totalLoadTime << " 毫秒" << std::endl;
        
        // 8. NPC脚本加载示例
        std::cout << "\n8. NPC脚本加载示例..." << std::endl;
        if (g_LocalDatabase->LoadNPCScript("比奇武器商", "./scripts/npc", "weapon_merchant")) {
            std::cout << "NPC脚本加载成功" << std::endl;
        }
        
        // 9. 缓存管理
        std::cout << "\n9. 缓存管理示例..." << std::endl;
        
        // 刷新缓存
        std::cout << "刷新缓存..." << std::endl;
        g_LocalDatabase->RefreshCache();
        
        // 重新获取统计信息
        stats = g_LocalDatabase->GetStatistics();
        std::cout << "刷新后物品数量: " << stats.stdItemCount << std::endl;
        
        // 10. 性能测试
        std::cout << "\n10. 性能测试..." << std::endl;
        
        auto start = std::chrono::high_resolution_clock::now();
        
        // 连续查询1000次物品
        for (int i = 0; i < 1000; ++i) {
            auto testItem = g_LocalDatabase->GetStdItem(i % 100);
            (void)testItem; // 避免编译器优化
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        std::cout << "1000次物品查询耗时: " << duration.count() << " 微秒" << std::endl;
        std::cout << "平均每次查询: " << duration.count() / 1000.0 << " 微秒" << std::endl;
        
        // 11. 清理
        std::cout << "\n11. 清理资源..." << std::endl;
        g_LocalDatabase->Finalize();
        g_LocalDatabase.reset();
        
        std::cout << "\n=== LocalDatabase 示例执行完成 ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "异常: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}

// 演示如何在GameEngine中集成LocalDatabase
class GameEngineWithLocalDB {
public:
    bool Initialize() {
        // 1. 初始化数据库
        m_database = std::make_unique<SQLiteDatabase>();
        if (!m_database->Connect("./GameData/mir.db")) {
            return false;
        }
        
        // 2. 初始化UserEngine
        m_userEngine = std::make_unique<UserEngine>();
        // 实际初始化UserEngine的代码...
        
        // 3. 初始化LocalDatabase
        g_LocalDatabase = std::make_unique<LocalDatabase>();
        if (!g_LocalDatabase->Initialize(m_database, m_userEngine.get())) {
            return false;
        }
        
        // 4. 设置数据加载完成回调
        g_LocalDatabase->SetDataLoadedCallback([this](const std::string& dataType, bool success) {
            OnDataLoaded(dataType, success);
        });
        
        // 5. 加载所有游戏数据
        LoadAllGameData();
        
        return true;
    }
    
    void LoadAllGameData() {
        // 数据库数据
        g_LocalDatabase->LoadItemsDB();
        g_LocalDatabase->LoadMagicDB();
        g_LocalDatabase->LoadMonsterDB();
        
        // 文件数据
        g_LocalDatabase->LoadAdminList();
        g_LocalDatabase->LoadGuardList();
        g_LocalDatabase->LoadMakeItem();
        g_LocalDatabase->LoadMapEvent();
        g_LocalDatabase->LoadNPCs();
        g_LocalDatabase->LoadMerchant();
        g_LocalDatabase->LoadStartPoint();
        g_LocalDatabase->LoadMapInfo();
        g_LocalDatabase->LoadMapQuest();
        g_LocalDatabase->LoadQuestDiary();
        g_LocalDatabase->LoadUnbindList();
        g_LocalDatabase->LoadMonGen();
    }
    
    void OnDataLoaded(const std::string& dataType, bool success) {
        if (success) {
            Logger::GetInstance()->LogInfo("Data loaded successfully: " + dataType);
        } else {
            Logger::GetInstance()->LogError("Failed to load data: " + dataType);
        }
    }
    
    // 提供给游戏逻辑使用的查询接口
    const StdItemInfo* GetItemInfo(int itemIdx) const {
        return g_LocalDatabase ? g_LocalDatabase->GetStdItem(itemIdx) : nullptr;
    }
    
    const MagicInfo* GetMagicInfo(WORD magicId) const {
        return g_LocalDatabase ? g_LocalDatabase->GetMagicInfo(magicId) : nullptr;
    }
    
    const MonsterInfo* GetMonsterInfo(const std::string& name) const {
        return g_LocalDatabase ? g_LocalDatabase->GetMonsterInfo(name) : nullptr;
    }
    
    bool IsAdmin(const std::string& charName) const {
        auto admin = g_LocalDatabase ? g_LocalDatabase->GetAdminInfo(charName) : nullptr;
        return admin != nullptr;
    }
    
    int GetAdminLevel(const std::string& charName) const {
        auto admin = g_LocalDatabase ? g_LocalDatabase->GetAdminInfo(charName) : nullptr;
        return admin ? admin->level : 0;
    }
    
    void ReloadGameData() {
        if (g_LocalDatabase) {
            g_LocalDatabase->ReloadAll();
        }
    }
    
    LocalDatabase::Statistics GetCacheStats() const {
        return g_LocalDatabase ? g_LocalDatabase->GetStatistics() : LocalDatabase::Statistics{};
    }
    
    void Finalize() {
        if (g_LocalDatabase) {
            g_LocalDatabase->Finalize();
            g_LocalDatabase.reset();
        }
        
        m_userEngine.reset();
        m_database.reset();
    }
    
private:
    std::shared_ptr<IDatabase> m_database;
    std::unique_ptr<UserEngine> m_userEngine;
};

// 使用示例函数
void DemonstrateGameEngineIntegration() {
    std::cout << "\n=== GameEngine 集成示例 ===" << std::endl;
    
    GameEngineWithLocalDB gameEngine;
    
    if (gameEngine.Initialize()) {
        std::cout << "GameEngine 初始化成功" << std::endl;
        
        // 查询游戏数据
        auto itemInfo = gameEngine.GetItemInfo(0);
        if (itemInfo) {
            std::cout << "查询到物品: " << itemInfo->name << std::endl;
        }
        
        // 检查管理员权限
        if (gameEngine.IsAdmin("Admin")) {
            std::cout << "Admin 是管理员，级别: " << gameEngine.GetAdminLevel("Admin") << std::endl;
        }
        
        // 获取缓存统计
        auto stats = gameEngine.GetCacheStats();
        std::cout << "缓存统计 - 物品: " << stats.stdItemCount 
                  << ", 魔法: " << stats.magicCount 
                  << ", 怪物: " << stats.monsterCount << std::endl;
        
        // 重新加载数据
        gameEngine.ReloadGameData();
        std::cout << "游戏数据已重新加载" << std::endl;
        
        gameEngine.Finalize();
    } else {
        std::cout << "GameEngine 初始化失败" << std::endl;
    }
} 
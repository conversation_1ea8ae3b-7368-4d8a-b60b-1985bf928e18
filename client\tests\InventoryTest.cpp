#include <iostream>
#include <memory>
#include "../src/Item/Item.h"
#include "../src/Item/Inventory.h"

// Simple test function to verify inventory functionality
void TestInventory() {
    std::cout << "Running Inventory Test..." << std::endl;
    
    // Create inventory
    auto inventory = std::make_shared<Inventory>();
    
    // Create some test items
    auto sword = std::make_shared<Item>(1, ItemType::WEAPON, 10, "Steel Sword", true);
    sword->SetAttackPower(15);
    sword->SetDurability(100);
    sword->SetMaxDurability(100);
    sword->SetWeight(5);
    
    auto armor = std::make_shared<Item>(2, ItemType::ARMOR, 20, "Leather Armor", true);
    armor->SetDefense(10);
    armor->SetDurability(80);
    armor->SetMaxDurability(100);
    armor->SetWeight(8);
    
    auto potion = std::make_shared<Item>(3, ItemType::POTION, 30, "Health Potion", true);
    potion->SetWeight(1);
    
    // Test adding items
    std::cout << "Adding items to inventory..." << std::endl;
    bool result1 = inventory->AddItem(sword, 0);
    bool result2 = inventory->AddItem(armor, 1);
    bool result3 = inventory->AddItem(potion, 2);
    
    std::cout << "Add results: " << result1 << ", " << result2 << ", " << result3 << std::endl;
    
    // Test getting items
    std::cout << "Getting items from inventory..." << std::endl;
    auto item0 = inventory->GetItem(0);
    auto item1 = inventory->GetItem(1);
    auto item2 = inventory->GetItem(2);
    
    if (item0) {
        std::cout << "Item 0: " << item0->GetName() << ", Type: " << static_cast<int>(item0->GetType()) << std::endl;
    }
    
    if (item1) {
        std::cout << "Item 1: " << item1->GetName() << ", Type: " << static_cast<int>(item1->GetType()) << std::endl;
    }
    
    if (item2) {
        std::cout << "Item 2: " << item2->GetName() << ", Type: " << static_cast<int>(item2->GetType()) << std::endl;
    }
    
    // Test weight calculation
    std::cout << "Calculating inventory weight..." << std::endl;
    int weight = inventory->CalculateWeight();
    std::cout << "Total weight: " << weight << std::endl;
    
    // Test equipping items
    std::cout << "Equipping items..." << std::endl;
    bool equipResult1 = inventory->EquipItem(0, Inventory::EquipmentSlot::WEAPON);
    bool equipResult2 = inventory->EquipItem(1, Inventory::EquipmentSlot::ARMOR);
    
    std::cout << "Equip results: " << equipResult1 << ", " << equipResult2 << std::endl;
    
    // Test getting equipped items
    std::cout << "Getting equipped items..." << std::endl;
    auto equippedWeapon = inventory->GetEquippedItem(Inventory::EquipmentSlot::WEAPON);
    auto equippedArmor = inventory->GetEquippedItem(Inventory::EquipmentSlot::ARMOR);
    
    if (equippedWeapon) {
        std::cout << "Equipped weapon: " << equippedWeapon->GetName() << std::endl;
    }
    
    if (equippedArmor) {
        std::cout << "Equipped armor: " << equippedArmor->GetName() << std::endl;
    }
    
    // Test unequipping items
    std::cout << "Unequipping items..." << std::endl;
    bool unequipResult1 = inventory->UnequipItem(Inventory::EquipmentSlot::WEAPON);
    bool unequipResult2 = inventory->UnequipItem(Inventory::EquipmentSlot::ARMOR);
    
    std::cout << "Unequip results: " << unequipResult1 << ", " << unequipResult2 << std::endl;
    
    // Test removing items
    std::cout << "Removing items..." << std::endl;
    auto removedItem = inventory->RemoveItem(2);
    
    if (removedItem) {
        std::cout << "Removed item: " << removedItem->GetName() << std::endl;
    }
    
    // Test clearing inventory
    std::cout << "Clearing inventory..." << std::endl;
    inventory->Clear();
    
    std::cout << "Inventory test completed." << std::endl;
}

int main() {
    TestInventory();
    return 0;
}

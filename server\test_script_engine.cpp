#include <iostream>
#include <memory>
#include "src/GameEngine/ScriptEngine.h"
#include "src/BaseObject/PlayObject.h"
#include "src/BaseObject/NPC.h"
#include "src/Common/Logger.h"

using namespace MirServer;

// 简单的测试PlayObject实现
class TestPlayObject : public PlayObject {
public:
    TestPlayObject() : PlayObject() {
        SetCharName("TestPlayer");
        SetLevel(25);
        SetJob(JobType::WARRIOR);
        SetGold(1000);
        SetDC(50);
        SetMC(30);
        SetSC(20);
        SetHP(100);
        SetMP(50);
        SetGender(GenderType::MALE);
        SetMapName("比奇县");
    }

    // 重写需要的虚函数
    ObjectType GetObjectType() const override { return ObjectType::PLAYER; }

    // 添加缺少的方法
    DWORD GetGold() const { return m_testGold; }
    void SetGold(DWORD gold) { m_testGold = gold; }

    const Ability& GetAbility() const { return m_testAbility; }
    void SetAbility(const Ability& ability) { m_testAbility = ability; }

private:
    DWORD m_testGold = 1000;
    Ability m_testAbility;
};

// 简单的测试NPC实现
class TestNPC : public NPC {
public:
    TestNPC() : NPC() {
        SetCharName("TestNPC");
        m_scriptFile = "scripts/advanced_test_npc.txt";
    }

    // 重写需要的虚函数
    ObjectType GetObjectType() const override { return ObjectType::NPC; }

    std::string GetScriptFile() const { return m_scriptFile; }

private:
    std::string m_scriptFile;
};

void TestConditionChecks() {
    std::cout << "\n=== 测试条件检查功能 ===" << std::endl;

    ScriptEngine engine;
    auto player = std::make_unique<TestPlayObject>();
    auto npc = std::make_unique<TestNPC>();

    // 测试等级检查
    ScriptCondition levelCondition;
    levelCondition.type = "CHECKLEVEL";
    levelCondition.param1 = "20";
    levelCondition.nParam1 = 20;
    levelCondition.method = '>';

    bool result = engine.CheckCondition(player.get(), npc.get(), levelCondition);
    std::cout << "CHECKLEVEL > 20: " << (result ? "通过" : "失败") << std::endl;

    // 测试职业检查
    ScriptCondition jobCondition;
    jobCondition.type = "CHECKJOB";
    jobCondition.param1 = "0";
    jobCondition.nParam1 = 0; // WARRIOR

    result = engine.CheckCondition(player.get(), npc.get(), jobCondition);
    std::cout << "CHECKJOB WARRIOR: " << (result ? "通过" : "失败") << std::endl;

    // 测试金币检查
    ScriptCondition goldCondition;
    goldCondition.type = "CHECKGOLD";
    goldCondition.param1 = "500";
    goldCondition.nParam1 = 500;
    goldCondition.method = '>';

    result = engine.CheckCondition(player.get(), npc.get(), goldCondition);
    std::cout << "CHECKGOLD > 500: " << (result ? "通过" : "失败") << std::endl;

    // 测试时间检查
    ScriptCondition timeCondition;
    timeCondition.type = "CHECKHOUR";
    timeCondition.param1 = "8";
    timeCondition.nParam1 = 8;
    timeCondition.method = '>';

    result = engine.CheckCondition(player.get(), npc.get(), timeCondition);
    std::cout << "CHECKHOUR > 8: " << (result ? "通过" : "失败") << std::endl;
}

void TestActionExecution() {
    std::cout << "\n=== 测试动作执行功能 ===" << std::endl;

    ScriptEngine engine;
    auto player = std::make_unique<TestPlayObject>();
    auto npc = std::make_unique<TestNPC>();

    // 测试给予金币
    ScriptAction giveGoldAction;
    giveGoldAction.type = "GIVEGOLD";
    giveGoldAction.param1 = "500";
    giveGoldAction.nParam1 = 500;

    bool result = engine.ExecuteAction(player.get(), npc.get(), giveGoldAction);
    std::cout << "GIVEGOLD 500: " << (result ? "成功" : "失败") << std::endl;

    // 测试发送消息
    ScriptAction sendMsgAction;
    sendMsgAction.type = "SENDMSG";
    sendMsgAction.param1 = "5";
    sendMsgAction.param2 = "这是一条测试消息！";
    sendMsgAction.nParam1 = 5;

    result = engine.ExecuteAction(player.get(), npc.get(), sendMsgAction);
    std::cout << "SENDMSG: " << (result ? "成功" : "失败") << std::endl;

    // 测试怪物生成
    ScriptAction monGenAction;
    monGenAction.type = "MONGEN";
    monGenAction.param1 = "鸡";
    monGenAction.param2 = "5";
    monGenAction.param3 = "3";
    monGenAction.nParam2 = 5;
    monGenAction.nParam3 = 3;

    result = engine.ExecuteAction(player.get(), npc.get(), monGenAction);
    std::cout << "MONGEN 鸡 5 3: " << (result ? "成功" : "失败") << std::endl;

    // 测试变量设置
    ScriptAction setVarAction;
    setVarAction.type = "SETVAR";
    setVarAction.param1 = "TestVar";
    setVarAction.param2 = "100";
    setVarAction.nParam2 = 100;

    result = engine.ExecuteAction(player.get(), npc.get(), setVarAction);
    std::cout << "SETVAR TestVar 100: " << (result ? "成功" : "失败") << std::endl;

    // 测试特效
    ScriptAction fireBurnAction;
    fireBurnAction.type = "FIREBURN";
    fireBurnAction.param1 = "330";
    fireBurnAction.param2 = "330";
    fireBurnAction.param3 = "10";
    fireBurnAction.nParam1 = 330;
    fireBurnAction.nParam2 = 330;
    fireBurnAction.nParam3 = 10;

    result = engine.ExecuteAction(player.get(), npc.get(), fireBurnAction);
    std::cout << "FIREBURN 330 330 10: " << (result ? "成功" : "失败") << std::endl;
}

void TestScriptParsing() {
    std::cout << "\n=== 测试脚本解析功能 ===" << std::endl;

    ScriptParser parser;
    NPCScript script;

    // 测试解析高级测试脚本
    bool result = parser.ParseScriptFile("scripts/advanced_test_npc.txt", script);
    std::cout << "解析 advanced_test_npc.txt: " << (result ? "成功" : "失败") << std::endl;

    if (result) {
        std::cout << "脚本块数量: " << script.blocks.size() << std::endl;

        // 显示一些脚本块信息
        for (const auto& pair : script.blocks) {
            const std::string& label = pair.first;
            const ScriptBlock& block = pair.second;
            std::cout << "  标签: " << label
                      << ", 条件数: " << block.conditions.size()
                      << ", 动作数: " << block.actions.size()
                      << ", 选项数: " << block.options.size() << std::endl;
        }
    } else {
        std::cout << "解析错误: " << parser.GetLastError() << std::endl;
    }
}

int main() {
    std::cout << "=== 脚本引擎功能测试 ===" << std::endl;

    // 初始化日志系统
    // Logger::Initialize("test_script_engine.log"); // Logger可能没有Initialize方法

    try {
        TestConditionChecks();
        TestActionExecution();
        TestScriptParsing();

        std::cout << "\n=== 测试完成 ===" << std::endl;
        std::cout << "详细日志请查看 test_script_engine.log 文件" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}

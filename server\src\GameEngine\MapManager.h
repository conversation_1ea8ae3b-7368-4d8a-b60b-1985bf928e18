#pragma once

#include "../Common/Types.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <mutex>

namespace MirServer {

// 前向声明
class Environment;

// 地图类型
enum class MapType {
    NORMAL = 0,         // 普通地图
    HOUSE = 1,          // 房屋地图
    GUILD = 2,          // 行会地图
    PALACE = 3,         // 皇宫地图
    CASTLE = 4,         // 城堡地图
    GREAT = 5,          // 大型地图
    MINE = 6            // 矿洞地图
};

// 地图信息
struct MapInfo {
    std::string mapFile;        // 地图文件名
    std::string mapName;        // 地图名称
    std::string title;          // 地图标题
    int serverIndex;           // 服务器索引
    int width;                 // 地图宽度
    int height;                // 地图高度
    MapType mapType;           // 地图类型
    int miniMap;               // 小地图编号
    int bigMap;                // 大地图编号
    bool canRide;              // 是否可以骑马
    bool canRest;              // 是否可以休息
    bool canThrowItem;         // 是否可以扔物品
    bool noReconnect;          // 是否禁止重连
    bool noRandMove;           // 是否禁止随机移动
    bool noDrug;               // 是否禁止使用药品
    bool noPositionMove;       // 是否禁止使用移动符
    bool noRecall;             // 是否禁止召回
    bool noReincarnation;      // 是否禁止复活
    bool needBridge;           // 是否需要桥梁
    bool needHole;             // 是否需要洞穴
    bool noFight;              // 是否禁止战斗
    bool fight3;               // 是否行会战地图
    bool fightPK;              // 是否PK地图
    bool music;                // 是否有背景音乐
    int musicID;               // 音乐ID
    int lightMode;             // 光照模式
    
    // 连接信息
    struct Connection {
        std::string targetMap;  // 目标地图
        Point sourcePos;        // 源位置
        Point targetPos;        // 目标位置
    };
    std::vector<Connection> connections;
    
    // 安全区
    struct SafeZone {
        Point startPos;
        Point endPos;
    };
    std::vector<SafeZone> safeZones;
};

// 地图文件头（对应delphi的地图文件格式）
#pragma pack(push, 1)
struct MapFileHeader {
    WORD width;
    WORD height;
    char title[16];
    DWORD reserved;
    char reserved2[24];
};

// 地图块信息
struct MapCellInfo {
    WORD bkIndex;           // 背景图索引
    WORD midIndex;          // 中间层图索引
    WORD frIndex;           // 前景图索引
    BYTE doorIndex;         // 门索引
    BYTE doorOffset;        // 门偏移
    BYTE aniFrame;          // 动画帧
    BYTE aniTick;           // 动画计时
    BYTE objFile;           // 对象文件
    BYTE light;             // 光照
    BYTE reserved[2];       // 保留
};
#pragma pack(pop)

// 地图管理器类（对应delphi的地图加载和管理）
class MapManager {
public:
    MapManager();
    ~MapManager();

    // 初始化
    bool Initialize(const std::string& mapPath);
    void Finalize();

    // 地图信息管理
    bool LoadMapList(const std::string& listFile);
    bool LoadMapInfo(const std::string& mapName);
    const MapInfo* GetMapInfo(const std::string& mapName) const;
    std::vector<std::string> GetAllMapNames() const;
    bool MapExists(const std::string& mapName) const;

    // 地图数据加载
    bool LoadMapData(const std::string& mapName);
    bool UnloadMapData(const std::string& mapName);
    bool IsMapLoaded(const std::string& mapName) const;

    // 地图环境
    std::shared_ptr<Environment> GetMapEnvironment(const std::string& mapName);
    bool CreateMapEnvironment(const std::string& mapName);

    // 地图查询
    bool CanWalk(const std::string& mapName, int x, int y) const;
    bool CanFly(const std::string& mapName, int x, int y) const;
    bool IsValidPosition(const std::string& mapName, int x, int y) const;
    bool IsSafeZone(const std::string& mapName, const Point& pos) const;

    // 地图连接
    Point GetMapConnection(const std::string& fromMap, const Point& pos, std::string& toMap) const;
    bool AddMapConnection(const std::string& fromMap, const std::string& toMap, 
                         const Point& fromPos, const Point& toPos);

    // 小地图
    std::vector<uint8_t> GetMiniMapData(const std::string& mapName) const;
    
    // 地图事件点
    struct MapPoint {
        std::string name;
        Point position;
        std::string description;
    };
    void AddMapPoint(const std::string& mapName, const MapPoint& point);
    std::vector<MapPoint> GetMapPoints(const std::string& mapName) const;

    // 地图配置
    void SetMapAttribute(const std::string& mapName, const std::string& attribute, bool value);
    bool GetMapAttribute(const std::string& mapName, const std::string& attribute) const;

    // 统计信息
    struct Statistics {
        size_t totalMaps = 0;
        size_t loadedMaps = 0;
        size_t totalConnections = 0;
        size_t totalSafeZones = 0;
        size_t totalMapPoints = 0;
    };
    Statistics GetStatistics() const;

    // 调试
    void DumpMapInfo(const std::string& mapName) const;
    void ValidateAllMaps() const;

private:
    // 内部方法
    bool LoadMapFile(const std::string& fileName, MapInfo& info);
    bool LoadMapCells(const std::string& fileName, std::vector<std::vector<MapCellInfo>>& cells);
    bool ParseMapListLine(const std::string& line, MapInfo& info);
    void ProcessMapConnections(MapInfo& info);
    void ProcessSafeZones(MapInfo& info);
    std::string GetMapFilePath(const std::string& mapFile) const;

    // 地图数据结构
    struct MapData {
        MapInfo info;
        std::vector<std::vector<MapCellInfo>> cells;
        std::vector<std::vector<BYTE>> walkable; // 可行走标记
        std::shared_ptr<Environment> environment;
        bool loaded = false;
    };

private:
    std::string m_mapPath;  // 地图文件路径
    
    // 地图数据
    std::unordered_map<std::string, std::unique_ptr<MapData>> m_maps;
    mutable std::mutex m_mapsMutex;
    
    // 地图点
    std::unordered_map<std::string, std::vector<MapPoint>> m_mapPoints;
    mutable std::mutex m_pointsMutex;
    
    // 运行状态
    bool m_initialized = false;
    
    // 配置
    size_t m_maxLoadedMaps = 100;  // 最大同时加载的地图数
    bool m_autoUnload = true;      // 是否自动卸载不用的地图
};

// 全局地图管理器实例
extern std::unique_ptr<MapManager> g_MapManager;

} // namespace MirServer 
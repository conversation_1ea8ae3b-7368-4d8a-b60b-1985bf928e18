{"files.associations": {"algorithm": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "charconv": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "deque": "cpp", "exception": "cpp", "format": "cpp", "fstream": "cpp", "functional": "cpp", "initializer_list": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "iterator": "cpp", "limits": "cpp", "list": "cpp", "locale": "cpp", "memory": "cpp", "mutex": "cpp", "new": "cpp", "optional": "cpp", "ostream": "cpp", "queue": "cpp", "random": "cpp", "ratio": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "string": "cpp", "system_error": "cpp", "thread": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "utility": "cpp", "vector": "cpp", "xfacet": "cpp", "xhash": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocbuf": "cpp", "xlocinfo": "cpp", "xlocmes": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xmemory": "cpp", "xstring": "cpp", "xtr1common": "cpp", "xutility": "cpp", "codecvt": "cpp", "condition_variable": "cpp", "cstdarg": "cpp", "cwctype": "cpp", "memory_resource": "cpp", "numeric": "cpp", "string_view": "cpp", "iomanip": "cpp", "numbers": "cpp", "semaphore": "cpp", "span": "cpp", "text_encoding": "cpp", "cinttypes": "cpp", "variant": "cpp", "any": "cpp", "bitset": "cpp", "chrono": "cpp", "csignal": "cpp", "map": "cpp", "set": "cpp", "unordered_set": "cpp", "regex": "cpp", "shared_mutex": "cpp"}, "cmake.sourceDirectory": "D:/git/mir-dephi-refactored/client"}
#include "../src/Utils/Logger.h"
#include "../src/Utils/ExceptionHandler.h"
#include <iostream>
#include <stdexcept>

/**
 * @brief Example function that throws an exception
 */
void FunctionThatThrows() {
    throw std::runtime_error("This is a test exception");
}

/**
 * @brief Example function that returns a value and might throw
 *
 * @return int The result of the calculation
 */
int CalculationThatMightThrow(int a, int b) {
    if (b == 0) {
        throw std::invalid_argument("Division by zero");
    }
    return a / b;
}

/**
 * @brief Example of how to use the exception handling system
 */
void ExceptionLoggingExample() {
    // Initialize the logger
    Logger::GetInstance().Initialize("logs/exceptions.log", LogLevel::LOG_LEVEL_DEBUG);

    // Log a message
    LOG_INFO("Starting exception logging example");

    // Example 1: Using TryCatch
    bool success = TRY_CATCH(FunctionThatThrows);
    LOG_INFO(success ? "Function executed successfully" : "Function threw an exception");

    // Example 2: Using TryCatchWithReturn
    int result = TRY_CATCH_RETURN(0, CalculationThatMightThrow, 10, 2);
    LOG_INFO("Calculation result: " + std::to_string(result));

    // Example 3: Division by zero
    result = TRY_CATCH_RETURN(0, CalculationThatMightThrow, 10, 0);
    LOG_INFO("Calculation result after division by zero: " + std::to_string(result));

    // Example 4: Manual exception logging
    try {
        FunctionThatThrows();
    } catch (const std::exception& e) {
        LOG_EXCEPTION(e.what());
    }

    LOG_INFO("Exception logging example completed");
} 
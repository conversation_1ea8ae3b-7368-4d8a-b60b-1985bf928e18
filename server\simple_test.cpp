#include "src/GameEngine/SimplePKManager.h"
#include <iostream>

using namespace MirServer;

int main() {
    std::cout << "=== PK System Test ===" << std::endl;
    
    try {
        // Initialize PK Manager
        auto& pkManager = SimplePKManager::GetInstance();
        pkManager.Initialize();
        
        std::cout << "PK Manager initialized successfully" << std::endl;
        
        // Test 1: Configuration Management
        std::cout << "\n--- Test 1: Configuration Management ---" << std::endl;
        pkManager.SetKillAddPKValue(100);
        pkManager.SetRedNamePKValue(200);
        pkManager.SetYellowNamePKValue(100);
        pkManager.SetPKFlagTime(300);
        pkManager.SetRedNameTime(3600);
        
        std::cout << "Kill PK Value: " << pkManager.GetKillAddPKValue() << std::endl;
        std::cout << "Red Name PK Threshold: " << pkManager.GetRedNamePKValue() << std::endl;
        std::cout << "Yellow Name PK Threshold: " << pkManager.GetYellowNamePKValue() << std::endl;
        std::cout << "PK Flag Time: " << pkManager.GetPKFlagTime() << " seconds" << std::endl;
        std::cout << "Red Name Duration: " << pkManager.GetRedNameTime() << " seconds" << std::endl;
        
        // Test 2: Guild War System
        std::cout << "\n--- Test 2: Guild War System ---" << std::endl;
        
        // Start guild war
        bool warStarted = pkManager.StartGuildWar("Sabuk", "MageAlliance", 30);
        std::cout << "Start Guild War [Sabuk vs MageAlliance]: " << (warStarted ? "SUCCESS" : "FAILED") << std::endl;
        
        // Check guild war status
        bool isWar = pkManager.IsGuildWar("Sabuk", "MageAlliance");
        std::cout << "Guild War Status Check: " << (isWar ? "ACTIVE" : "INACTIVE") << std::endl;
        
        // Get active guild wars
        auto activeWars = pkManager.GetActiveGuildWars();
        std::cout << "Active Guild Wars Count: " << activeWars.size() << std::endl;
        
        if (!activeWars.empty()) {
            const auto& war = activeWars[0];
            std::cout << "War Details: " << war.guild1 << " vs " << war.guild2 
                     << " (Duration: " << war.duration << " minutes)" << std::endl;
        }
        
        // Test 3: Duplicate Guild War (should fail)
        std::cout << "\n--- Test 3: Duplicate Guild War Detection ---" << std::endl;
        bool duplicateWar = pkManager.StartGuildWar("Sabuk", "MageAlliance", 20);
        std::cout << "Duplicate Guild War: " << (duplicateWar ? "SUCCESS (ERROR)" : "FAILED (CORRECT)") << std::endl;
        
        // Test 4: Start another guild war
        std::cout << "\n--- Test 4: Multiple Guild Wars ---" << std::endl;
        bool secondWar = pkManager.StartGuildWar("WarriorGuild", "TaoistAlliance", 45);
        std::cout << "Start Second Guild War [WarriorGuild vs TaoistAlliance]: " << (secondWar ? "SUCCESS" : "FAILED") << std::endl;
        
        activeWars = pkManager.GetActiveGuildWars();
        std::cout << "Active Guild Wars Count: " << activeWars.size() << std::endl;
        
        // Test 5: End guild war
        std::cout << "\n--- Test 5: End Guild War ---" << std::endl;
        bool warEnded = pkManager.EndGuildWar("Sabuk", "MageAlliance");
        std::cout << "End Guild War [Sabuk vs MageAlliance]: " << (warEnded ? "SUCCESS" : "FAILED") << std::endl;
        
        activeWars = pkManager.GetActiveGuildWars();
        std::cout << "Active Wars After End: " << activeWars.size() << std::endl;
        
        // Test 6: End non-existent guild war
        std::cout << "\n--- Test 6: End Non-existent Guild War ---" << std::endl;
        bool endNonExistent = pkManager.EndGuildWar("NonExistentGuild1", "NonExistentGuild2");
        std::cout << "End Non-existent Guild War: " << (endNonExistent ? "SUCCESS (ERROR)" : "FAILED (CORRECT)") << std::endl;
        
        // Test 7: Check non-existent guild war
        std::cout << "\n--- Test 7: Check Non-existent Guild War ---" << std::endl;
        bool nonExistentWar = pkManager.IsGuildWar("NonExistentGuild1", "NonExistentGuild2");
        std::cout << "Check Non-existent Guild War: " << (nonExistentWar ? "EXISTS (ERROR)" : "NOT EXISTS (CORRECT)") << std::endl;
        
        // Test 8: Update mechanism
        std::cout << "\n--- Test 8: Update Mechanism ---" << std::endl;
        pkManager.Update();
        std::cout << "PK Manager Update Completed" << std::endl;
        
        // Test 9: Cleanup remaining guild wars
        std::cout << "\n--- Test 9: Cleanup ---" << std::endl;
        pkManager.EndGuildWar("WarriorGuild", "TaoistAlliance");
        
        activeWars = pkManager.GetActiveGuildWars();
        std::cout << "Active Wars After Cleanup: " << activeWars.size() << std::endl;
        
        // Cleanup
        pkManager.Finalize();
        std::cout << "PK Manager Finalized" << std::endl;
        
        std::cout << "\n=== PK System Test Completed ===" << std::endl;
        std::cout << "All tests passed!" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed: " << e.what() << std::endl;
        return 1;
    }
}

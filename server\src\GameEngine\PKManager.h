#pragma once

#include "../Common/Types.h"
#include <memory>
#include <unordered_map>
#include <vector>
#include <string>
#include <mutex>

namespace MirServer {

class PlayObject;
class BaseObject;
class Environment;

// PK模式
enum class PKMode : BYTE {
    PEACE = 0,          // 和平模式
    DEAR = 1,           // 夫妻模式
    MASTER = 2,         // 师徒模式
    GROUP = 3,          // 组队模式
    GUILD = 4,          // 行会模式
    REDNAME = 5,        // 善恶模式
    ALL = 6             // 全体模式
};

// PK信息结构
struct PKInfo {
    DWORD pkValue = 0;          // PK值
    DWORD redTime = 0;          // 红名剩余时间(秒)
    DWORD killCount = 0;        // 杀人数
    DWORD deathCount = 0;       // 死亡数
    DWORD lastKillTime = 0;     // 最后杀人时间
    DWORD lastDeathTime = 0;    // 最后死亡时间
    bool isRedName = false;     // 是否红名
    bool isYellowName = false;  // 是否黄名
};

// 前向声明
struct GuildWarInfo;

// PK系统管理器
class PKManager {
public:
    static PKManager& GetInstance();

    // 初始化和清理
    void Initialize();
    void Finalize();

    // PK事件处理
    void OnPlayerKill(PlayObject* killer, PlayObject* victim);
    void OnPlayerDeath(PlayObject* player, BaseObject* killer = nullptr);
    void OnPlayerLogin(PlayObject* player);
    void OnPlayerLogout(PlayObject* player);

    // PK值管理
    void AddPKValue(PlayObject* player, DWORD value);
    void SubPKValue(PlayObject* player, DWORD value);
    void SetPKValue(PlayObject* player, DWORD value);
    DWORD GetPKValue(PlayObject* player) const;

    // PK状态检查
    bool IsRedName(PlayObject* player) const;
    bool IsYellowName(PlayObject* player) const;
    bool CanAttack(PlayObject* attacker, PlayObject* target) const;
    bool IsInSafeZone(PlayObject* player) const;

    // 攻击模式管理
    void SetAttackMode(PlayObject* player, PKMode mode);
    PKMode GetAttackMode(PlayObject* player) const;
    bool IsValidTarget(PlayObject* attacker, PlayObject* target) const;

    // 行会战管理
    bool StartGuildWar(const std::string& guild1, const std::string& guild2, DWORD duration = 30);
    bool EndGuildWar(const std::string& guild1, const std::string& guild2);
    bool IsGuildWar(const std::string& guild1, const std::string& guild2) const;
    std::vector<GuildWarInfo> GetActiveGuildWars() const;

    // 配置管理
    void SetKillAddPKValue(DWORD value) { m_killAddPKValue = value; }
    void SetPKFlagTime(DWORD time) { m_pkFlagTime = time; }
    void SetRedNameTime(DWORD time) { m_redNameTime = time; }
    void SetYellowNamePKValue(DWORD value) { m_yellowNamePKValue = value; }
    void SetRedNamePKValue(DWORD value) { m_redNamePKValue = value; }

    DWORD GetKillAddPKValue() const { return m_killAddPKValue; }
    DWORD GetPKFlagTime() const { return m_pkFlagTime; }
    DWORD GetRedNameTime() const { return m_redNameTime; }
    DWORD GetYellowNamePKValue() const { return m_yellowNamePKValue; }
    DWORD GetRedNamePKValue() const { return m_redNamePKValue; }

    // 定时更新
    void Update();

private:
    PKManager() = default;
    ~PKManager() = default;
    PKManager(const PKManager&) = delete;
    PKManager& operator=(const PKManager&) = delete;

    // 内部方法
    PKInfo& GetOrCreatePKInfo(PlayObject* player);
    void UpdatePKStatus(PlayObject* player);
    void UpdateRedTime(PlayObject* player);
    void ProcessPKDecay();
    void ProcessGuildWars();
    void ApplyPKPenalty(PlayObject* player);
    void NotifyPKStatusChange(PlayObject* player);

    // 关系检查
    bool IsGroupMember(PlayObject* player1, PlayObject* player2) const;
    bool IsGuildMember(PlayObject* player1, PlayObject* player2) const;
    bool IsDearPartner(PlayObject* player1, PlayObject* player2) const;
    bool IsMasterStudent(PlayObject* player1, PlayObject* player2) const;

private:
    mutable std::mutex m_mutex;

    // PK信息存储
    std::unordered_map<std::string, PKInfo> m_pkInfoMap;

    // 行会战信息
    std::vector<GuildWarInfo> m_guildWars;

    // 配置参数
    DWORD m_killAddPKValue = 100;       // 杀人增加PK值
    DWORD m_pkFlagTime = 300;           // PK标记时间(秒)
    DWORD m_redNameTime = 3600;         // 红名持续时间(秒)
    DWORD m_yellowNamePKValue = 100;    // 黄名PK值阈值
    DWORD m_redNamePKValue = 200;       // 红名PK值阈值
    DWORD m_pkDecayInterval = 60;       // PK值衰减间隔(秒)
    DWORD m_pkDecayValue = 1;           // 每次衰减的PK值

    // 时间管理
    DWORD m_lastUpdateTime = 0;
    DWORD m_lastDecayTime = 0;

    bool m_initialized = false;
};

} // namespace MirServer

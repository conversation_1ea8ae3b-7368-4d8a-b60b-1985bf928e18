#include <SDL2/SDL.h>
#include <iostream>
#include <fstream>
#include <windows.h>

void log(const std::string& message) {
    std::ofstream logFile("test_sdl_log.txt", std::ios::app);
    if (logFile.is_open()) {
        logFile << message << std::endl;
        logFile.close();
    }
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // 创建控制台便于调试
    AllocConsole();
    FILE* pConsole;
    freopen_s(&pConsole, "CONOUT$", "w", stdout);
    freopen_s(&pConsole, "CONOUT$", "w", stderr);
    
    log("SDL测试程序启动");
    std::cout << "SDL测试程序启动" << std::endl;
    
    // 初始化SDL
    if (SDL_Init(SDL_INIT_VIDEO) < 0) {
        log("SDL初始化失败: " + std::string(SDL_GetError()));
        std::cout << "SDL初始化失败: " << SDL_GetError() << std::endl;
        return 1;
    }
    log("SDL初始化成功");
    std::cout << "SDL初始化成功" << std::endl;
    
    // 创建窗口
    SDL_Window* window = SDL_CreateWindow(
        "SDL测试",
        SDL_WINDOWPOS_CENTERED,
        SDL_WINDOWPOS_CENTERED,
        800, 600,
        SDL_WINDOW_SHOWN
    );
    
    if (!window) {
        log("窗口创建失败: " + std::string(SDL_GetError()));
        std::cout << "窗口创建失败: " << SDL_GetError() << std::endl;
        SDL_Quit();
        return 1;
    }
    log("窗口创建成功");
    std::cout << "窗口创建成功" << std::endl;
    
    // 创建渲染器
    SDL_Renderer* renderer = SDL_CreateRenderer(window, -1, SDL_RENDERER_ACCELERATED);
    if (!renderer) {
        log("渲染器创建失败: " + std::string(SDL_GetError()));
        std::cout << "渲染器创建失败: " << SDL_GetError() << std::endl;
        SDL_DestroyWindow(window);
        SDL_Quit();
        return 1;
    }
    log("渲染器创建成功");
    std::cout << "渲染器创建成功" << std::endl;
    
    // 设置背景颜色为蓝色
    SDL_SetRenderDrawColor(renderer, 0, 0, 255, 255);
    SDL_RenderClear(renderer);
    SDL_RenderPresent(renderer);
    
    log("渲染蓝色背景");
    std::cout << "渲染蓝色背景" << std::endl;
    
    // 等待5秒
    log("等待5秒...");
    std::cout << "等待5秒..." << std::endl;
    SDL_Delay(5000);
    
    // 清理
    log("清理资源");
    std::cout << "清理资源" << std::endl;
    SDL_DestroyRenderer(renderer);
    SDL_DestroyWindow(window);
    SDL_Quit();
    
    log("程序正常退出");
    std::cout << "程序正常退出" << std::endl;
    
    // 等待用户确认
    std::cout << "按任意键继续..." << std::endl;
    getchar();
    
    return 0;
} 
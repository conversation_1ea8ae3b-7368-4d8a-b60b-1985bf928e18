// ItemManager.cpp - 物品管理器实现
#include "ItemManager.h"
#include "../Common/Logger.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <iomanip>
#include <atomic>
#include <cstring>
#include <cmath>

namespace MirServer {

// 全局实例
std::unique_ptr<ItemManager> g_ItemManager;

// DroppedItem类实现
std::atomic<uint32_t> DroppedItem::s_nextId{1};

DroppedItem::DroppedItem(const UserItem& item, const Point& pos, DWORD dropTime)
    : m_id(s_nextId++), m_item(item), m_position(pos), m_dropTime(dropTime),
      m_ownerProtectTime(0), m_isGold(false), m_goldAmount(0) {
}

DroppedItem::~DroppedItem() {
}

void DroppedItem::SetOwner(const std::string& owner, DWORD protectTime) {
    m_owner = owner;
    m_ownerProtectTime = GetCurrentTime() + protectTime;
}

bool DroppedItem::CanPickup(const std::string& name) const {
    if (m_owner.empty()) return true;
    if (m_owner == name) return true;

    return GetCurrentTime() > m_ownerProtectTime;
}

bool DroppedItem::ShouldDisappear(DWORD currentTime) const {
    const DWORD ITEM_DISAPPEAR_TIME = 180000; // 3分钟
    return (currentTime - m_dropTime) > ITEM_DISAPPEAR_TIME;
}

// ItemManager类实现
ItemManager::ItemManager() {
    InitializeRandomGenerators();
}

ItemManager::~ItemManager() {
    Finalize();
}

bool ItemManager::Initialize(const std::string& dataPath) {
    if (m_initialized) {
        return true;
    }

    m_dataPath = dataPath;
    InitializeRandomGenerators();

    // 加载物品数据库
    if (!LoadItemDB("StdItems.txt")) {
        Logger::Warning("Failed to load item database");
    }

    // 加载怪物掉落数据
    if (!LoadMonsterDrops("MonsterDrops.txt")) {
        Logger::Warning("Failed to load monster drops");
    }

    // 加载商店数据
    if (!LoadShopLists("Shop.txt")) {
        Logger::Warning("Failed to load shop database");
    }

    // 加载自定义物品名称
    if (!LoadCustomItemName()) {
        Logger::Warning("Failed to load custom item names");
    }

    // 初始化强化系统
    InitializeUpgradeSystem();

    m_initialized = true;
    Logger::Info("ItemManager initialized with path: " + dataPath);
    return true;
}

void ItemManager::Finalize() {
    if (!m_initialized) {
        return;
    }

    // 保存自定义物品名称
    SaveCustomItemName();

    // 清理数据
    {
        std::lock_guard<std::mutex> lock(m_itemsMutex);
        m_items.clear();
        m_nameToIdx.clear();
    }

    {
        std::lock_guard<std::mutex> lock(m_dropsMutex);
        m_monsterDrops.clear();
    }

    {
        std::lock_guard<std::mutex> lock(m_shopsMutex);
        m_shopLists.clear();
    }

    {
        std::lock_guard<std::mutex> lock(m_customNamesMutex);
        m_customItemNames.clear();
    }

    m_initialized = false;
    Logger::Info("ItemManager finalized");
}

bool ItemManager::LoadItemDB(const std::string& fileName) {
    std::string fullPath = m_dataPath + "/" + fileName;
    std::ifstream file(fullPath);

    if (!file.is_open()) {
        Logger::Error("Failed to open item database: " + fullPath);
        return false;
    }

    std::string line;
    int lineNum = 0;
    int itemCount = 0;

    while (std::getline(file, line)) {
        lineNum++;

        // 跳过空行和注释
        if (line.empty() || line[0] == ';' || line[0] == '#') {
            continue;
        }

        auto item = std::make_unique<StdItem>();
        if (ParseItemLine(line, *item)) {
            WORD idx = item->idx;
            std::string name = item->name;

            {
                std::lock_guard<std::mutex> lock(m_itemsMutex);
                m_items[idx] = std::move(item);
                m_nameToIdx[name] = idx;
            }

            itemCount++;
        } else {
            Logger::Warning("Failed to parse item line " + std::to_string(lineNum) + ": " + line);
        }
    }

    file.close();
    Logger::Info("Loaded " + std::to_string(itemCount) + " items from database");
    return true;
}

bool ItemManager::LoadMonsterDrops(const std::string& fileName) {
    std::string fullPath = m_dataPath + "/" + fileName;
    std::ifstream file(fullPath);

    if (!file.is_open()) {
        Logger::Error("Failed to open monster drops file: " + fullPath);
        return false;
    }

    std::string line;
    int lineNum = 0;
    int dropCount = 0;

    while (std::getline(file, line)) {
        lineNum++;

        if (line.empty() || line[0] == ';' || line[0] == '#') {
            continue;
        }

        std::string monsterName;
        MonsterDrop drop;

        if (ParseDropLine(line, monsterName, drop)) {
            {
                std::lock_guard<std::mutex> lock(m_dropsMutex);
                m_monsterDrops[monsterName].push_back(drop);
            }
            dropCount++;
        } else {
            Logger::Warning("Failed to parse drop line " + std::to_string(lineNum) + ": " + line);
        }
    }

    file.close();
    Logger::Info("Loaded " + std::to_string(dropCount) + " monster drop rules");
    return true;
}

bool ItemManager::LoadShopLists(const std::string& fileName) {
    std::string fullPath = m_dataPath + "/" + fileName;
    std::ifstream file(fullPath);

    if (!file.is_open()) {
        Logger::Error("Failed to open shop lists file: " + fullPath);
        return false;
    }

    std::string line;
    int lineNum = 0;
    int shopItemCount = 0;

    while (std::getline(file, line)) {
        lineNum++;

        if (line.empty() || line[0] == ';' || line[0] == '#') {
            continue;
        }

        std::string shopName;
        ShopItem item;

        if (ParseShopLine(line, shopName, item)) {
            {
                std::lock_guard<std::mutex> lock(m_shopsMutex);
                m_shopLists[shopName].push_back(item);
            }
            shopItemCount++;
        } else {
            Logger::Warning("Failed to parse shop line " + std::to_string(lineNum) + ": " + line);
        }
    }

    file.close();
    Logger::Info("Loaded " + std::to_string(shopItemCount) + " shop items");
    return true;
}

const StdItem* ItemManager::GetStdItem(WORD itemIdx) const {
    std::lock_guard<std::mutex> lock(m_itemsMutex);

    auto it = m_items.find(itemIdx);
    if (it != m_items.end()) {
        return it->second.get();
    }

    return nullptr;
}

const StdItem* ItemManager::GetStdItemByName(const std::string& name) const {
    std::lock_guard<std::mutex> lock(m_itemsMutex);

    auto it = m_nameToIdx.find(name);
    if (it != m_nameToIdx.end()) {
        auto itemIt = m_items.find(it->second);
        if (itemIt != m_items.end()) {
            return itemIt->second.get();
        }
    }

    return nullptr;
}

std::vector<const StdItem*> ItemManager::GetItemsByType(ItemType type) const {
    std::lock_guard<std::mutex> lock(m_itemsMutex);

    std::vector<const StdItem*> result;
    for (const auto& pair : m_items) {
        if (pair.second && pair.second->stdMode == static_cast<uint8_t>(type)) {
            result.push_back(pair.second.get());
        }
    }

    return result;
}

bool ItemManager::ItemExists(WORD itemIdx) const {
    std::lock_guard<std::mutex> lock(m_itemsMutex);
    return m_items.find(itemIdx) != m_items.end();
}

UserItem ItemManager::CreateItem(WORD itemIdx) const {
    UserItem item;
    item.itemIndex = 0;

    const StdItem* stdItem = GetStdItem(itemIdx);
    if (!stdItem) {
        return item;
    }

    // 设置基本属性
    item.itemIndex = itemIdx;
    item.makeIndex = static_cast<WORD>(GetCurrentTime() & 0xFFFF); // 简单的唯一ID生成
    item.dura = stdItem->duraMax;
    item.duraMax = stdItem->duraMax;

    // 根据物品类型初始化附加属性
    switch (static_cast<ItemType>(stdItem->stdMode)) {
        case ItemType::WEAPON:
            GenerateWeaponStats(const_cast<UserItem&>(item), stdItem, 0);
            break;
        case ItemType::ARMOR:
        case ItemType::HELMET:
        case ItemType::BELT:
        case ItemType::BOOTS:
            GenerateArmorStats(const_cast<UserItem&>(item), stdItem, 0);
            break;
        case ItemType::NECKLACE:
        case ItemType::RING:
        case ItemType::BRACELET:
            GenerateAccessoryStats(const_cast<UserItem&>(item), stdItem, 0);
            break;
        default:
            break;
    }

    return item;
}

UserItem ItemManager::CreateItemByName(const std::string& name) const {
    const StdItem* stdItem = GetStdItemByName(name);
    if (stdItem) {
        return CreateItem(stdItem->idx);
    }

    UserItem empty;
    empty.itemIndex = 0;
    return empty;
}

bool ItemManager::CreateRandomItem(ItemType type, UserItem& item) const {
    auto items = GetItemsByType(type);
    if (items.empty()) {
        return false;
    }

    // 随机选择一个物品
    std::uniform_int_distribution<size_t> dist(0, items.size() - 1);
    size_t idx = dist(m_randomEngine);

    const StdItem* stdItem = items[idx];
    if (stdItem) {
        item = CreateItem(stdItem->idx);
        return true;
    }

    return false;
}

bool ItemManager::UpgradeItem(UserItem& item, int upgradeLevel) const {
    const StdItem* stdItem = GetStdItem(item.itemIndex);
    if (!stdItem) {
        return false;
    }

    // 升级物品属性
    item.value.dc = std::min<BYTE>(item.value.dc + upgradeLevel, 255);
    item.value.mc = std::min<BYTE>(item.value.mc + upgradeLevel, 255);
    item.value.sc = std::min<BYTE>(item.value.sc + upgradeLevel, 255);
    item.value.ac = std::min<BYTE>(item.value.ac + upgradeLevel, 255);
    item.value.mac = std::min<BYTE>(item.value.mac + upgradeLevel, 255);

    return true;
}

int ItemManager::GetItemWeight(const UserItem& item) const {
    const StdItem* stdItem = GetStdItem(item.itemIndex);
    if (!stdItem) {
        return 0;
    }

    return stdItem->weight;
}

int ItemManager::GetItemValue(const UserItem& item) const {
    const StdItem* stdItem = GetStdItem(item.itemIndex);
    if (!stdItem) {
        return 0;
    }

    // 基础价格
    int value = stdItem->price;

    // 根据耐久度调整
    if (stdItem->duraMax > 0) {
        value = value * item.dura / stdItem->duraMax;
    }

    // 根据附加属性调整
    value += (item.value.dc + item.value.mc + item.value.sc) * 100;
    value += (item.value.ac + item.value.mac) * 50;

    return value;
}

bool ItemManager::CanEquipItem(const UserItem& item, const Ability& ability, JobType job, GenderType gender) const {
    const StdItem* stdItem = GetStdItem(item.itemIndex);
    if (!stdItem) {
        return false;
    }

    // 检查等级需求
    if (ability.Level < stdItem->needLevel) {
        return false;
    }

    // 检查属性需求（基于need字段）
    switch (static_cast<NeedType>(stdItem->need)) {
        case NeedType::LEVEL:
            // 等级需求已在上面检查
            break;

        case NeedType::DC:
            // 需要物理攻击力
            if (ability.DC.min < stdItem->needLevel) {
                return false;
            }
            break;

        case NeedType::MC:
            // 需要魔法攻击力
            if (ability.MC.min < stdItem->needLevel) {
                return false;
            }
            break;

        case NeedType::SC:
            // 需要道术攻击力
            if (ability.SC.min < stdItem->needLevel) {
                return false;
            }
            break;

        default:
            // 未知需求类型，默认通过
            break;
    }

    // 检查职业限制（基于物品类型和形状）
    if (!CheckJobRestriction(stdItem, job)) {
        return false;
    }

    // 检查性别限制（基于物品形状）
    if (!CheckGenderRestriction(stdItem, gender)) {
        return false;
    }

    return true;
}

bool ItemManager::IsStackable(WORD itemIdx) const {
    const StdItem* stdItem = GetStdItem(itemIdx);
    if (!stdItem) {
        return false;
    }

    // 根据原项目逻辑判断物品是否可叠加
    // 直接使用stdMode值进行判断，而不是转换为ItemType枚举
    uint8_t stdMode = stdItem->stdMode;

    switch (stdMode) {
        case 0:  // 药品类 - 可叠加
        case 1:  // 金币 - 可叠加
        case 3:  // 卷轴类 - 可叠加
        case 4:  // 肉类 - 可叠加
            return true;

        case 2:  // 技能书/任务物品 - 通常不叠加
            return false;

        case 5:  // 武器 - 不叠加
        case 10: // 衣服 - 不叠加
        case 15: // 头盔 - 不叠加
        case 19: // 项链 - 不叠加
        case 20: // 项链 - 不叠加
        case 21: // 项链 - 不叠加
        case 22: // 戒指 - 不叠加
        case 23: // 戒指 - 不叠加
        case 24: // 手镯 - 不叠加
        case 25: // 手镯 - 不叠加
        case 26: // 手镯 - 不叠加
        case 52: // 靴子 - 不叠加
        case 54: // 腰带 - 不叠加
            return false;

        default:
            // 其他特殊物品类型
            return IsSpecialStackableItem(stdItem);
    }
}

int ItemManager::GetMaxStack(WORD itemIdx) const {
    const StdItem* stdItem = GetStdItem(itemIdx);
    if (!stdItem || !IsStackable(itemIdx)) {
        return 1;
    }

    // 根据物品类型返回不同的最大叠加数量
    uint8_t stdMode = stdItem->stdMode;

    switch (stdMode) {
        case 0:  // 药品类
            return 99;              // 药品最大叠加99个

        case 3:  // 卷轴类
            return 50;              // 卷轴最大叠加50个

        case 4:  // 肉类
            return 20;              // 肉类最大叠加20个

        case 1:  // 金币
            return 999999;          // 金币可以叠加很多

        case 42: // 材料类
        case 43: // 宝石类
        case 44: // 矿石类
        case 45: // 药材类
            return 99;

        case 6:  // 特殊物品（如箭矢）
            return 250;

        default:
            return 99;  // 默认最大叠加数量
    }
}

std::vector<UserItem> ItemManager::GenerateMonsterDrops(const std::string& monsterName,
                                                        int monsterLevel, float luckFactor) {
    std::vector<UserItem> drops;

    // 获取怪物掉落配置
    std::vector<MonsterDrop> dropRules;
    {
        std::lock_guard<std::mutex> lock(m_dropsMutex);
        auto it = m_monsterDrops.find(monsterName);
        if (it != m_monsterDrops.end()) {
            dropRules = it->second;
        }
    }

    // 金币掉落
    float goldChance = m_floatDist(m_randomEngine);
    if (goldChance < m_goldDropRate) {
        UserItem goldItem;
        goldItem.itemIndex = 0; // 金币特殊标记
        goldItem.dura = monsterLevel * (50 + std::uniform_int_distribution<>(0, 100)(m_randomEngine));
        drops.push_back(goldItem);
    }

    // 物品掉落
    for (const auto& rule : dropRules) {
        // 检查等级要求
        if (monsterLevel < rule.minLevel) {
            continue;
        }

        // 计算掉落概率
        float dropChance = rule.dropRate * m_baseDropRate * luckFactor;
        float roll = m_floatDist(m_randomEngine);

        if (roll < dropChance) {
            // 掉落成功
            int count = rule.minCount;
            if (rule.maxCount > rule.minCount) {
                std::uniform_int_distribution<> countDist(rule.minCount, rule.maxCount);
                count = countDist(m_randomEngine);
            }

            for (int i = 0; i < count && drops.size() < static_cast<size_t>(m_maxDropItems); i++) {
                UserItem item = CreateItem(rule.itemIdx);
                if (item.itemIndex > 0) {
                    drops.push_back(item);
                }
            }
        }
    }

    return drops;
}

bool ItemManager::AddMonsterDrop(const std::string& monsterName, const MonsterDrop& drop) {
    std::lock_guard<std::mutex> lock(m_dropsMutex);
    m_monsterDrops[monsterName].push_back(drop);
    return true;
}

std::vector<ItemManager::ShopItem> ItemManager::GetShopItems(const std::string& shopName) const {
    std::lock_guard<std::mutex> lock(m_shopsMutex);

    auto it = m_shopLists.find(shopName);
    if (it != m_shopLists.end()) {
        return it->second;
    }

    return {};
}

bool ItemManager::AddShopItem(const std::string& shopName, const ShopItem& item) {
    std::lock_guard<std::mutex> lock(m_shopsMutex);
    m_shopLists[shopName].push_back(item);
    return true;
}

UserItem ItemManager::GenerateRandomEquipment(ItemType type, int level) const {
    UserItem item;

    // 获取符合等级的装备
    auto items = GetItemsByType(type);
    std::vector<const StdItem*> validItems;

    for (const auto* stdItem : items) {
        if (stdItem && stdItem->needLevel <= level) {
            validItems.push_back(stdItem);
        }
    }

    if (validItems.empty()) {
        return item;
    }

    // 随机选择
    std::uniform_int_distribution<size_t> dist(0, validItems.size() - 1);
    const StdItem* selected = validItems[dist(m_randomEngine)];

    item = CreateItem(selected->idx);

    // 根据等级添加随机属性
    if (level > 20) {
        int bonusCount = (level - 20) / 10 + 1;
        AddRandomStats(item, bonusCount);
    }

    return item;
}

void ItemManager::AddRandomStats(UserItem& item, int bonusCount) const {
    const StdItem* stdItem = GetStdItem(item.itemIndex);
    if (!stdItem) return;

    for (int i = 0; i < bonusCount; i++) {
        std::uniform_int_distribution<> statDist(0, 6);
        int stat = statDist(m_randomEngine);

        switch (stat) {
            case 0: // DC
                item.value.dc = std::min<BYTE>(item.value.dc + 1, 10);
                break;
            case 1: // MC
                item.value.mc = std::min<BYTE>(item.value.mc + 1, 10);
                break;
            case 2: // SC
                item.value.sc = std::min<BYTE>(item.value.sc + 1, 10);
                break;
            case 3: // AC
                item.value.ac = std::min<BYTE>(item.value.ac + 1, 10);
                break;
            case 4: // MAC
                item.value.mac = std::min<BYTE>(item.value.mac + 1, 10);
                break;
            case 5: // Accuracy
                item.value.accuracy = std::min<BYTE>(item.value.accuracy + 1, 5);
                break;
            case 6: // Agility
                item.value.agility = std::min<BYTE>(item.value.agility + 1, 5);
                break;
        }
    }
}

void ItemManager::RepairItem(UserItem& item) const {
    const StdItem* stdItem = GetStdItem(item.itemIndex);
    if (!stdItem) return;

    item.dura = stdItem->duraMax;
}

bool ItemManager::ValidateItem(const UserItem& item) const {
    if (item.itemIndex == 0) {
        return false;
    }

    const StdItem* stdItem = GetStdItem(item.itemIndex);
    if (!stdItem) {
        return false;
    }

    // 检查耐久度
    if (item.dura > item.duraMax || item.duraMax > stdItem->duraMax * 2) {
        return false;
    }

    // 检查附加属性是否合理
    if (item.value.dc > 15 || item.value.mc > 15 || item.value.sc > 15) {
        return false;
    }

    if (item.value.ac > 15 || item.value.mac > 15) {
        return false;
    }

    return true;
}

bool ItemManager::IsQuestItem(WORD itemIdx) const {
    const StdItem* stdItem = GetStdItem(itemIdx);
    if (!stdItem) {
        return false;
    }

    return stdItem->stdMode == static_cast<uint8_t>(ItemType::QUEST);
}

bool ItemManager::IsValuableItem(WORD itemIdx) const {
    const StdItem* stdItem = GetStdItem(itemIdx);
    if (!stdItem) {
        return false;
    }

    // 价值超过10000的物品视为贵重物品
    return stdItem->price >= 10000;
}

ItemManager::Statistics ItemManager::GetStatistics() const {
    Statistics stats;

    {
        std::lock_guard<std::mutex> lock(m_itemsMutex);
        stats.totalItems = m_items.size();

        for (const auto& pair : m_items) {
            if (pair.second) {
                switch (static_cast<ItemType>(pair.second->stdMode)) {
                    case ItemType::WEAPON:
                        stats.weaponCount++;
                        break;
                    case ItemType::ARMOR:
                    case ItemType::HELMET:
                    case ItemType::BELT:
                    case ItemType::BOOTS:
                        stats.armorCount++;
                        break;
                    case ItemType::NECKLACE:
                    case ItemType::RING:
                    case ItemType::BRACELET:
                        stats.accessoryCount++;
                        break;
                    case ItemType::DRUG:
                    case ItemType::SCROLL:
                    case ItemType::MEAT:
                        stats.consumableCount++;
                        break;
                    case ItemType::QUEST:
                        stats.questItemCount++;
                        break;
                    default:
                        break;
                }
            }
        }
    }

    {
        std::lock_guard<std::mutex> lock(m_dropsMutex);
        for (const auto& pair : m_monsterDrops) {
            stats.totalDropRules += pair.second.size();
        }
    }

    {
        std::lock_guard<std::mutex> lock(m_shopsMutex);
        for (const auto& pair : m_shopLists) {
            stats.totalShopItems += pair.second.size();
        }
    }

    return stats;
}

void ItemManager::DumpItemInfo(WORD itemIdx) const {
    const StdItem* item = GetStdItem(itemIdx);
    if (!item) {
        Logger::Error("Item not found: " + std::to_string(itemIdx));
        return;
    }

    Logger::Info("=== Item Info: " + item->name + " ===");
    Logger::Info("Index: " + std::to_string(item->idx));
    Logger::Info("Type: " + std::to_string(static_cast<int>(item->stdMode)));
    Logger::Info("Weight: " + std::to_string(item->weight));
    Logger::Info("Price: " + std::to_string(item->price));
    Logger::Info("Level Required: " + std::to_string(item->needLevel));

    if (item->dc > 0 || item->dc2 > 0) {
        Logger::Info("DC: " + std::to_string(item->dc) + "-" + std::to_string(item->dc2));
    }
    if (item->mc > 0 || item->mc2 > 0) {
        Logger::Info("MC: " + std::to_string(item->mc) + "-" + std::to_string(item->mc2));
    }
    if (item->sc > 0 || item->sc2 > 0) {
        Logger::Info("SC: " + std::to_string(item->sc) + "-" + std::to_string(item->sc2));
    }
    if (item->ac > 0 || item->ac2 > 0) {
        Logger::Info("AC: " + std::to_string(item->ac) + "-" + std::to_string(item->ac2));
    }
    if (item->mac > 0 || item->mac2 > 0) {
        Logger::Info("MAC: " + std::to_string(item->mac) + "-" + std::to_string(item->mac2));
    }
}

void ItemManager::ExportItemList(const std::string& fileName) const {
    std::ofstream file(fileName);
    if (!file.is_open()) {
        Logger::Error("Failed to create export file: " + fileName);
        return;
    }

    file << "# Item Database Export\n";
    file << "# Format: Index,Name,Type,Level,Price,Weight\n\n";

    std::lock_guard<std::mutex> lock(m_itemsMutex);

    for (const auto& pair : m_items) {
        const StdItem* item = pair.second.get();
        if (item) {
            file << item->idx << ","
                 << item->name << ","
                 << static_cast<int>(item->stdMode) << ","
                 << static_cast<int>(item->needLevel) << ","
                 << item->price << ","
                 << item->weight << "\n";
        }
    }

    file.close();
    Logger::Info("Exported " + std::to_string(m_items.size()) + " items to " + fileName);
}

// 内部方法实现
bool ItemManager::ParseItemLine(const std::string& line, StdItem& item) {
    std::istringstream iss(line);
    std::string token;

    // 简单的CSV解析，实际格式可能需要调整
    if (!std::getline(iss, token, ',')) return false;
    item.idx = static_cast<WORD>(std::stoi(token));

    if (!std::getline(iss, item.name, ',')) return false;

    if (!std::getline(iss, token, ',')) return false;
    item.stdMode = static_cast<uint8_t>(std::stoi(token));

    // 设置默认值
    item.shape = 0;
    item.weight = 10;
    item.aniCount = 0;
    item.source = 0;
    item.reserved = 0;
    item.needLevel = 0;
    item.duraMax = 1000;
    item.price = 100;

    // 设置属性默认值
    item.dc = item.dc2 = 0;
    item.mc = item.mc2 = 0;
    item.sc = item.sc2 = 0;
    item.ac = item.ac2 = 0;
    item.mac = item.mac2 = 0;

    // 设置标记默认值（简化版本）
    // TODO: 根据需要添加更多属性

    // TODO: 解析更多字段

    return true;
}

bool ItemManager::ParseDropLine(const std::string& line, std::string& monsterName, MonsterDrop& drop) {
    std::istringstream iss(line);

    // 格式: MonsterName,ItemIdx,DropRate,MinCount,MaxCount,MinLevel
    if (!std::getline(iss, monsterName, ',')) return false;

    std::string token;
    if (!std::getline(iss, token, ',')) return false;
    drop.itemIdx = static_cast<WORD>(std::stoi(token));

    if (!std::getline(iss, token, ',')) return false;
    drop.dropRate = std::stof(token);

    if (!std::getline(iss, token, ',')) return false;
    drop.minCount = std::stoi(token);

    if (!std::getline(iss, token, ',')) return false;
    drop.maxCount = std::stoi(token);

    if (!std::getline(iss, token, ',')) return false;
    drop.minLevel = std::stoi(token);

    drop.questFlag = 0;

    return true;
}

bool ItemManager::ParseShopLine(const std::string& line, std::string& shopName, ShopItem& item) {
    std::istringstream iss(line);

    // 格式: ShopName,ItemIdx,Stock,PriceRate
    if (!std::getline(iss, shopName, ',')) return false;

    std::string token;
    if (!std::getline(iss, token, ',')) return false;
    item.itemIdx = static_cast<WORD>(std::stoi(token));

    if (!std::getline(iss, token, ',')) return false;
    item.stock = std::stoi(token);

    if (!std::getline(iss, token, ',')) return false;
    item.priceRate = std::stof(token);

    item.refreshTime = 3600; // 默认1小时刷新

    return true;
}

ItemType ItemManager::GetItemTypeFromStdMode(int stdMode) const {
    return static_cast<ItemType>(stdMode);
}

void ItemManager::InitializeRandomGenerators() {
    std::random_device rd;
    m_randomEngine.seed(rd());
}

int ItemManager::GetRandomStatValue(int baseValue, float variance) const {
    if (baseValue == 0 || variance == 0) {
        return baseValue;
    }

    int minVal = static_cast<int>(baseValue * (1.0f - variance));
    int maxVal = static_cast<int>(baseValue * (1.0f + variance));

    std::uniform_int_distribution<> dist(minVal, maxVal);
    return dist(m_randomEngine);
}

void ItemManager::GenerateWeaponStats(UserItem& item, const StdItem* stdItem, int level) const {
    if (!stdItem) return;

    // 清空所有附加属性
    memset(item.btValue, 0, sizeof(item.btValue));

    // 根据原项目逻辑，武器的基础属性来自StdItem，附加属性存储在btValue中
    // btValue[0] = DC攻击力加成
    // btValue[1] = MC魔法攻击力加成
    // btValue[2] = SC道术攻击力加成
    // btValue[3] = AC防御力加成
    // btValue[4] = MAC魔法防御力加成
    // btValue[5] = 敏捷加成
    // btValue[6] = 准确度加成
    // btValue[7] = 幸运加成
    // btValue[8] = 诅咒加成
    // btValue[9] = 生命值加成
    // btValue[10] = 魔法值加成
    // btValue[11] = 重量减少
    // btValue[12] = 攻击速度加成
    // btValue[13] = 魔法躲避加成

    // 根据等级和随机性生成附加属性
    if (level > 0) {
        // DC攻击力加成 (概率生成)
        if (std::uniform_int_distribution<>(0, 19)(m_randomEngine) == 0) {
            item.btValue[0] = std::uniform_int_distribution<>(1, level / 10 + 1)(m_randomEngine);
        }

        // MC魔法攻击力加成 (概率生成)
        if (std::uniform_int_distribution<>(0, 19)(m_randomEngine) == 0) {
            item.btValue[1] = std::uniform_int_distribution<>(1, level / 10 + 1)(m_randomEngine);
        }

        // SC道术攻击力加成 (概率生成)
        if (std::uniform_int_distribution<>(0, 19)(m_randomEngine) == 0) {
            item.btValue[2] = std::uniform_int_distribution<>(1, level / 10 + 1)(m_randomEngine);
        }

        // 准确度加成 (概率生成)
        if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
            item.btValue[6] = std::uniform_int_distribution<>(1, level / 15 + 1)(m_randomEngine);
        }

        // 敏捷加成 (概率生成)
        if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
            item.btValue[5] = std::uniform_int_distribution<>(1, level / 20 + 1)(m_randomEngine);
        }

        // 幸运加成 (概率生成，较低)
        if (std::uniform_int_distribution<>(0, 99)(m_randomEngine) == 0) {
            item.btValue[7] = std::uniform_int_distribution<>(1, 3)(m_randomEngine);
        }
    }

    // 更新ItemValue结构
    item.value.FromBytes(item.btValue);
}

void ItemManager::GenerateArmorStats(UserItem& item, const StdItem* stdItem, int level) const {
    if (!stdItem) return;

    // 清空所有附加属性
    memset(item.btValue, 0, sizeof(item.btValue));

    // 根据原项目逻辑，防具的附加属性存储在btValue中
    // btValue[0] = AC防御力加成
    // btValue[1] = MAC魔法防御力加成
    // btValue[2] = DC攻击力加成
    // btValue[3] = MC魔法攻击力加成
    // btValue[4] = SC道术攻击力加成
    // btValue[5] = 敏捷加成
    // btValue[6] = 准确度加成
    // btValue[7] = 幸运加成
    // btValue[8] = 诅咒加成
    // btValue[9] = 生命值加成
    // btValue[10] = 魔法值加成

    // 根据等级和随机性生成附加属性
    if (level > 0) {
        // AC防御力加成 (概率生成)
        if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
            item.btValue[0] = std::uniform_int_distribution<>(1, level / 8 + 1)(m_randomEngine);
        }

        // MAC魔法防御力加成 (概率生成)
        if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
            item.btValue[1] = std::uniform_int_distribution<>(1, level / 8 + 1)(m_randomEngine);
        }

        // DC攻击力加成 (概率生成，较低)
        if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
            item.btValue[2] = std::uniform_int_distribution<>(1, level / 15 + 1)(m_randomEngine);
        }

        // MC魔法攻击力加成 (概率生成，较低)
        if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
            item.btValue[3] = std::uniform_int_distribution<>(1, level / 15 + 1)(m_randomEngine);
        }

        // SC道术攻击力加成 (概率生成，较低)
        if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
            item.btValue[4] = std::uniform_int_distribution<>(1, level / 15 + 1)(m_randomEngine);
        }

        // 生命值加成 (概率生成)
        if (std::uniform_int_distribution<>(0, 49)(m_randomEngine) == 0) {
            item.btValue[9] = std::uniform_int_distribution<>(5, level / 5 + 10)(m_randomEngine);
        }

        // 魔法值加成 (概率生成)
        if (std::uniform_int_distribution<>(0, 49)(m_randomEngine) == 0) {
            item.btValue[10] = std::uniform_int_distribution<>(5, level / 5 + 10)(m_randomEngine);
        }
    }

    // 更新ItemValue结构
    item.value.FromBytes(item.btValue);
}

void ItemManager::GenerateAccessoryStats(UserItem& item, const StdItem* stdItem, int level) const {
    if (!stdItem) return;

    // 清空所有附加属性
    memset(item.btValue, 0, sizeof(item.btValue));

    // 根据原项目逻辑，首饰的附加属性存储在btValue中
    // 首饰通常有更多样化的属性组合
    // btValue[0] = DC攻击力加成 或 AC防御力加成
    // btValue[1] = MC魔法攻击力加成 或 MAC魔法防御力加成
    // btValue[2] = SC道术攻击力加成
    // btValue[3] = AC防御力加成 或 其他属性
    // btValue[4] = MAC魔法防御力加成
    // btValue[5] = 敏捷加成
    // btValue[6] = 准确度加成
    // btValue[7] = 幸运加成
    // btValue[8] = 诅咒加成
    // btValue[9] = 生命值加成
    // btValue[10] = 魔法值加成

    // 根据首饰类型和等级生成不同的属性
    uint8_t stdMode = stdItem->stdMode;

    if (level > 0) {
        switch (stdMode) {
            case 19: // 项链类型19 - 主要防御属性
                // AC防御力加成
                if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
                    item.btValue[0] = std::uniform_int_distribution<>(1, level / 10 + 1)(m_randomEngine);
                }
                // MAC魔法防御力加成
                if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
                    item.btValue[1] = std::uniform_int_distribution<>(1, level / 10 + 1)(m_randomEngine);
                }
                break;

            case 20: case 21: case 24: { // 项链类型20,21,24 - 混合属性
                // 随机选择主要属性类型
                int attrType = std::uniform_int_distribution<>(0, 2)(m_randomEngine);
                if (attrType == 0) { // 攻击型
                    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
                        item.btValue[0] = std::uniform_int_distribution<>(1, level / 12 + 1)(m_randomEngine);
                    }
                    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
                        item.btValue[1] = std::uniform_int_distribution<>(1, level / 12 + 1)(m_randomEngine);
                    }
                } else if (attrType == 1) { // 防御型
                    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
                        item.btValue[3] = std::uniform_int_distribution<>(1, level / 12 + 1)(m_randomEngine);
                    }
                    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
                        item.btValue[4] = std::uniform_int_distribution<>(1, level / 12 + 1)(m_randomEngine);
                    }
                }
                break;
            }

            case 22: case 23: // 戒指 - 攻击属性为主
                // DC攻击力加成
                if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
                    item.btValue[0] = std::uniform_int_distribution<>(1, level / 12 + 1)(m_randomEngine);
                }
                // MC魔法攻击力加成
                if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
                    item.btValue[1] = std::uniform_int_distribution<>(1, level / 12 + 1)(m_randomEngine);
                }
                // SC道术攻击力加成
                if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
                    item.btValue[2] = std::uniform_int_distribution<>(1, level / 12 + 1)(m_randomEngine);
                }
                break;

            case 25: case 26: // 手镯 - 平衡属性
                // 攻击和防御都有可能
                if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
                    item.btValue[0] = std::uniform_int_distribution<>(1, level / 12 + 1)(m_randomEngine);
                }
                if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
                    item.btValue[1] = std::uniform_int_distribution<>(1, level / 12 + 1)(m_randomEngine);
                }
                if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
                    item.btValue[3] = std::uniform_int_distribution<>(1, level / 15 + 1)(m_randomEngine);
                }
                break;
        }

        // 所有首饰都有可能获得的特殊属性
        // 准确度加成
        if (std::uniform_int_distribution<>(0, 49)(m_randomEngine) == 0) {
            item.btValue[6] = std::uniform_int_distribution<>(1, level / 20 + 1)(m_randomEngine);
        }

        // 敏捷加成
        if (std::uniform_int_distribution<>(0, 59)(m_randomEngine) == 0) {
            item.btValue[5] = std::uniform_int_distribution<>(1, level / 25 + 1)(m_randomEngine);
        }

        // 幸运加成 (非常稀有)
        if (std::uniform_int_distribution<>(0, 199)(m_randomEngine) == 0) {
            item.btValue[7] = std::uniform_int_distribution<>(1, 2)(m_randomEngine);
        }
    }

    // 更新ItemValue结构
    item.value.FromBytes(item.btValue);
}

// 随机数生成辅助方法
int ItemManager::GetRandomRange(int count, int rate) const {
    int result = 0;
    for (int i = 0; i < count; i++) {
        std::uniform_int_distribution<> dist(0, rate - 1);
        if (dist(m_randomEngine) == 0) {
            result++;
        }
    }
    return result;
}

// 武器随机升级（对应原项目RandomUpgradeWeapon）
void ItemManager::RandomUpgradeWeapon(UserItem& item) const {
    // DC攻击力
    int dc = GetRandomRange(12, 15);
    if (std::uniform_int_distribution<>(0, 14)(m_randomEngine) == 0) {
        item.btValue[0] = std::min(255, dc + 1);
    }

    // 准确度
    dc = GetRandomRange(12, 15);
    if (std::uniform_int_distribution<>(0, 19)(m_randomEngine) == 0) {
        int accuracy = (dc + 1) / 3;
        if (accuracy > 0) {
            if (std::uniform_int_distribution<>(0, 2)(m_randomEngine) != 0) {
                item.btValue[6] = std::min(255, accuracy);
            } else {
                item.btValue[6] = std::min(255, accuracy + 10);
            }
        }
    }

    // MC魔法攻击力
    dc = GetRandomRange(12, 15);
    if (std::uniform_int_distribution<>(0, 14)(m_randomEngine) == 0) {
        item.btValue[1] = std::min(255, dc + 1);
    }

    // SC道术攻击力
    dc = GetRandomRange(12, 15);
    if (std::uniform_int_distribution<>(0, 14)(m_randomEngine) == 0) {
        item.btValue[2] = std::min(255, dc + 1);
    }

    // 敏捷
    dc = GetRandomRange(12, 15);
    if (std::uniform_int_distribution<>(0, 23)(m_randomEngine) == 0) {
        item.btValue[5] = std::min(255, dc / 2 + 1);
    }

    // 持久度增加
    dc = GetRandomRange(12, 12);
    if (std::uniform_int_distribution<>(0, 2)(m_randomEngine) < 2) {
        int durAdd = (dc + 1) * 2000;
        item.duraMax = std::min(65000, static_cast<int>(item.duraMax) + durAdd);
        item.dura = std::min(65000, static_cast<int>(item.dura) + durAdd);
    }

    // 幸运
    dc = GetRandomRange(12, 15);
    if (std::uniform_int_distribution<>(0, 9)(m_randomEngine) == 0) {
        item.btValue[7] = std::min(255, dc / 2 + 1);
    }
}

// 防具随机升级（对应原项目RandomUpgradeDress）
void ItemManager::RandomUpgradeDress(UserItem& item) const {
    // AC防御力
    int ac = GetRandomRange(6, 15);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[0] = std::min(255, ac + 1);
    }

    // MAC魔法防御力
    ac = GetRandomRange(6, 15);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[1] = std::min(255, ac + 1);
    }

    // DC攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[2] = std::min(255, ac + 1);
    }

    // MC魔法攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[3] = std::min(255, ac + 1);
    }

    // SC道术攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[4] = std::min(255, ac + 1);
    }

    // 持久度增加
    ac = GetRandomRange(6, 10);
    if (std::uniform_int_distribution<>(0, 7)(m_randomEngine) < 6) {
        int durAdd = (ac + 1) * 2000;
        item.duraMax = std::min(65000, static_cast<int>(item.duraMax) + durAdd);
        item.dura = std::min(65000, static_cast<int>(item.dura) + durAdd);
    }
}

// 头盔随机升级
void ItemManager::RandomUpgradeHelmet(UserItem& item) const {
    // AC防御力
    int ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[0] = std::min(255, ac + 1);
    }

    // MAC魔法防御力
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[1] = std::min(255, ac + 1);
    }

    // DC攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[2] = std::min(255, ac + 1);
    }

    // MC魔法攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[3] = std::min(255, ac + 1);
    }

    // SC道术攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[4] = std::min(255, ac + 1);
    }

    // 持久度增加
    ac = GetRandomRange(6, 12);
    if (std::uniform_int_distribution<>(0, 3)(m_randomEngine) < 3) {
        int durAdd = (ac + 1) * 1000;
        item.duraMax = std::min(65000, static_cast<int>(item.duraMax) + durAdd);
        item.dura = std::min(65000, static_cast<int>(item.dura) + durAdd);
    }
}

// 项链类型19随机升级
void ItemManager::RandomUpgrade19(UserItem& item) const {
    // AC防御力
    int ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[0] = std::min(255, ac + 1);
    }

    // MAC魔法防御力
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[1] = std::min(255, ac + 1);
    }

    // DC攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[2] = std::min(255, ac + 1);
    }

    // MC魔法攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[3] = std::min(255, ac + 1);
    }

    // SC道术攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[4] = std::min(255, ac + 1);
    }

    // 持久度增加
    ac = GetRandomRange(6, 10);
    if (std::uniform_int_distribution<>(0, 3)(m_randomEngine) < 3) {
        int durAdd = (ac + 1) * 1000;
        item.duraMax = std::min(65000, static_cast<int>(item.duraMax) + durAdd);
        item.dura = std::min(65000, static_cast<int>(item.dura) + durAdd);
    }
}

// 戒指类型22随机升级
void ItemManager::RandomUpgrade22(UserItem& item) const {
    // DC攻击力加成
    int ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[0] = std::min(255, ac + 1);
    }

    // MC魔法攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[1] = std::min(255, ac + 1);
    }

    // SC道术攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[2] = std::min(255, ac + 1);
    }

    // AC防御力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[3] = std::min(255, ac + 1);
    }

    // MAC魔法防御力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[4] = std::min(255, ac + 1);
    }

    // 持久度增加
    ac = GetRandomRange(6, 10);
    if (std::uniform_int_distribution<>(0, 3)(m_randomEngine) < 3) {
        int durAdd = (ac + 1) * 1000;
        item.duraMax = std::min(65000, static_cast<int>(item.duraMax) + durAdd);
        item.dura = std::min(65000, static_cast<int>(item.dura) + durAdd);
    }
}

// 戒指类型23随机升级
void ItemManager::RandomUpgrade23(UserItem& item) const {
    // DC攻击力加成
    int ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[0] = std::min(255, ac + 1);
    }

    // MC魔法攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[1] = std::min(255, ac + 1);
    }

    // SC道术攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[2] = std::min(255, ac + 1);
    }

    // AC防御力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[3] = std::min(255, ac + 1);
    }

    // MAC魔法防御力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[4] = std::min(255, ac + 1);
    }

    // 持久度增加
    ac = GetRandomRange(6, 10);
    if (std::uniform_int_distribution<>(0, 3)(m_randomEngine) < 3) {
        int durAdd = (ac + 1) * 1000;
        item.duraMax = std::min(65000, static_cast<int>(item.duraMax) + durAdd);
        item.dura = std::min(65000, static_cast<int>(item.dura) + durAdd);
    }
}

// 手镯类型26随机升级
void ItemManager::RandomUpgrade26(UserItem& item) const {
    // DC攻击力加成
    int ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[0] = std::min(255, ac + 1);
    }

    // MC魔法攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[1] = std::min(255, ac + 1);
    }

    // SC道术攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[2] = std::min(255, ac + 1);
    }

    // AC防御力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[3] = std::min(255, ac + 1);
    }

    // MAC魔法防御力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[4] = std::min(255, ac + 1);
    }

    // 持久度增加
    ac = GetRandomRange(6, 10);
    if (std::uniform_int_distribution<>(0, 3)(m_randomEngine) < 3) {
        int durAdd = (ac + 1) * 1000;
        item.duraMax = std::min(65000, static_cast<int>(item.duraMax) + durAdd);
        item.dura = std::min(65000, static_cast<int>(item.dura) + durAdd);
    }
}

// 项链类型20,21,24随机升级
void ItemManager::RandomUpgrade202124(UserItem& item) const {
    // AC防御力
    int ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[0] = std::min(255, ac + 1);
    }

    // MAC魔法防御力
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[1] = std::min(255, ac + 1);
    }

    // DC攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[2] = std::min(255, ac + 1);
    }

    // MC魔法攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[3] = std::min(255, ac + 1);
    }

    // SC道术攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[4] = std::min(255, ac + 1);
    }

    // 持久度增加
    ac = GetRandomRange(6, 10);
    if (std::uniform_int_distribution<>(0, 3)(m_randomEngine) < 3) {
        int durAdd = (ac + 1) * 1000;
        item.duraMax = std::min(65000, static_cast<int>(item.duraMax) + durAdd);
        item.dura = std::min(65000, static_cast<int>(item.dura) + durAdd);
    }
}

// 未知头盔属性生成
void ItemManager::UnknowHelmet(UserItem& item) const {
    // AC防御力
    int ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[0] = std::min(255, ac + 1);
    }

    // MAC魔法防御力
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[1] = std::min(255, ac + 1);
    }

    // DC攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[2] = std::min(255, ac + 1);
    }

    // MC魔法攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[3] = std::min(255, ac + 1);
    }

    // SC道术攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[4] = std::min(255, ac + 1);
    }

    // 持久度增加
    ac = GetRandomRange(6, 12);
    if (std::uniform_int_distribution<>(0, 3)(m_randomEngine) < 3) {
        int durAdd = (ac + 1) * 1000;
        item.duraMax = std::min(65000, static_cast<int>(item.duraMax) + durAdd);
        item.dura = std::min(65000, static_cast<int>(item.dura) + durAdd);
    }
}

// 未知戒指属性生成
void ItemManager::UnknowRing(UserItem& item) const {
    // DC攻击力加成
    int ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[0] = std::min(255, ac + 1);
    }

    // MC魔法攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[1] = std::min(255, ac + 1);
    }

    // SC道术攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[2] = std::min(255, ac + 1);
    }

    // AC防御力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[3] = std::min(255, ac + 1);
    }

    // MAC魔法防御力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[4] = std::min(255, ac + 1);
    }

    // 持久度增加
    ac = GetRandomRange(6, 10);
    if (std::uniform_int_distribution<>(0, 3)(m_randomEngine) < 3) {
        int durAdd = (ac + 1) * 1000;
        item.duraMax = std::min(65000, static_cast<int>(item.duraMax) + durAdd);
        item.dura = std::min(65000, static_cast<int>(item.dura) + durAdd);
    }
}

// 未知项链属性生成
void ItemManager::UnknowNecklace(UserItem& item) const {
    // AC防御力
    int ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[0] = std::min(255, ac + 1);
    }

    // MAC魔法防御力
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 39)(m_randomEngine) == 0) {
        item.btValue[1] = std::min(255, ac + 1);
    }

    // DC攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[2] = std::min(255, ac + 1);
    }

    // MC魔法攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[3] = std::min(255, ac + 1);
    }

    // SC道术攻击力加成
    ac = GetRandomRange(6, 20);
    if (std::uniform_int_distribution<>(0, 29)(m_randomEngine) == 0) {
        item.btValue[4] = std::min(255, ac + 1);
    }

    // 持久度增加
    ac = GetRandomRange(6, 10);
    if (std::uniform_int_distribution<>(0, 3)(m_randomEngine) < 3) {
        int durAdd = (ac + 1) * 1000;
        item.duraMax = std::min(65000, static_cast<int>(item.duraMax) + durAdd);
        item.dura = std::min(65000, static_cast<int>(item.dura) + durAdd);
    }
}

// 物品属性计算（对应原项目GetItemAddValue）
void ItemManager::GetItemAddValue(UserItem& item, StdItem& stdItem) const {
    // 根据物品的btValue数组计算对基础属性的影响
    // 这个方法用于计算物品的附加属性对基础属性的影响

    // DC攻击力加成
    if (item.btValue[0] > 0) {
        stdItem.dc += item.btValue[0];
    }

    // MC魔法攻击力加成
    if (item.btValue[1] > 0) {
        stdItem.mc += item.btValue[1];
    }

    // SC道术攻击力加成
    if (item.btValue[2] > 0) {
        stdItem.sc += item.btValue[2];
    }

    // AC防御力加成
    if (item.btValue[3] > 0) {
        stdItem.ac += item.btValue[3];
    }

    // MAC魔法防御力加成
    if (item.btValue[4] > 0) {
        stdItem.mac += item.btValue[4];
    }

    // 敏捷加成
    if (item.btValue[5] > 0) {
        // 敏捷影响准确度和躲避
        // 这里可以根据需要添加具体的计算逻辑
    }

    // 准确度加成
    if (item.btValue[6] > 0) {
        // 准确度影响命中率
        // 这里可以根据需要添加具体的计算逻辑
    }

    // 幸运加成
    if (item.btValue[7] > 0) {
        // 幸运影响暴击率等
        // 这里可以根据需要添加具体的计算逻辑
    }
}

// 加载自定义物品名称
bool ItemManager::LoadCustomItemName() {
    std::string fileName = m_dataPath + "/CustomItemName.txt";
    std::ifstream file(fileName);

    if (!file.is_open()) {
        Logger::Warning("Custom item name file not found: " + fileName);
        return false;
    }

    std::lock_guard<std::mutex> lock(m_customNamesMutex);
    m_customItemNames.clear();

    std::string line;
    int lineNum = 0;

    while (std::getline(file, line)) {
        lineNum++;

        // 跳过空行和注释行
        if (line.empty() || line[0] == '#' || line[0] == ';') {
            continue;
        }

        // 解析格式: MakeIndex ItemIndex ItemName
        std::istringstream iss(line);
        CustomItemName customName;

        if (!(iss >> customName.makeIndex >> customName.itemIndex)) {
            Logger::Warning("Invalid custom item name format at line " + std::to_string(lineNum) + ": " + line);
            continue;
        }

        // 读取剩余部分作为物品名称
        std::string remaining;
        std::getline(iss, remaining);

        // 去除前导空格
        size_t start = remaining.find_first_not_of(" \t");
        if (start != std::string::npos) {
            customName.itemName = remaining.substr(start);
        } else {
            customName.itemName = "";
        }

        if (!customName.itemName.empty()) {
            m_customItemNames.push_back(customName);
        }
    }

    Logger::Info("Loaded " + std::to_string(m_customItemNames.size()) + " custom item names");
    return true;
}

// 保存自定义物品名称
bool ItemManager::SaveCustomItemName() {
    std::string fileName = m_dataPath + "/CustomItemName.txt";
    std::ofstream file(fileName);

    if (!file.is_open()) {
        Logger::Error("Failed to save custom item names to: " + fileName);
        return false;
    }

    std::lock_guard<std::mutex> lock(m_customNamesMutex);

    file << "# Custom Item Names\n";
    file << "# Format: MakeIndex ItemIndex ItemName\n";
    file << "#\n";

    for (const auto& customName : m_customItemNames) {
        file << customName.makeIndex << " " << customName.itemIndex << " " << customName.itemName << "\n";
    }

    Logger::Info("Saved " + std::to_string(m_customItemNames.size()) + " custom item names");
    return true;
}

// 添加自定义物品名称
bool ItemManager::AddCustomItemName(int makeIndex, int itemIndex, const std::string& itemName) {
    if (itemName.empty()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_customNamesMutex);

    // 检查是否已存在
    for (auto& customName : m_customItemNames) {
        if (customName.makeIndex == makeIndex && customName.itemIndex == itemIndex) {
            // 更新现有名称
            customName.itemName = itemName;
            return true;
        }
    }

    // 添加新的自定义名称
    CustomItemName newCustomName;
    newCustomName.makeIndex = makeIndex;
    newCustomName.itemIndex = itemIndex;
    newCustomName.itemName = itemName;

    m_customItemNames.push_back(newCustomName);
    return true;
}

// 删除自定义物品名称
bool ItemManager::DelCustomItemName(int makeIndex, int itemIndex) {
    std::lock_guard<std::mutex> lock(m_customNamesMutex);

    auto it = std::remove_if(m_customItemNames.begin(), m_customItemNames.end(),
        [makeIndex, itemIndex](const CustomItemName& customName) {
            return customName.makeIndex == makeIndex && customName.itemIndex == itemIndex;
        });

    if (it != m_customItemNames.end()) {
        m_customItemNames.erase(it, m_customItemNames.end());
        return true;
    }

    return false;
}

// 获取自定义物品名称
std::string ItemManager::GetCustomItemName(int makeIndex, int itemIndex) const {
    std::lock_guard<std::mutex> lock(m_customNamesMutex);

    for (const auto& customName : m_customItemNames) {
        if (customName.makeIndex == makeIndex && customName.itemIndex == itemIndex) {
            return customName.itemName;
        }
    }

    return "";  // 未找到自定义名称
}

// 检查职业限制
bool ItemManager::CheckJobRestriction(const StdItem* stdItem, JobType job) const {
    if (!stdItem) return false;

    // 根据物品类型和形状检查职业限制
    ItemType itemType = static_cast<ItemType>(stdItem->stdMode);

    switch (itemType) {
        case ItemType::WEAPON: {
            // 武器职业限制检查
            // 基于原项目的逻辑，某些武器有职业限制

            // 法师武器（魔法杖等）
            if (stdItem->shape >= 10 && stdItem->shape <= 19) {
                return job == JobType::WIZARD;
            }

            // 道士武器（魔杖、剑等）
            if (stdItem->shape >= 20 && stdItem->shape <= 29) {
                return job == JobType::TAOIST;
            }

            // 战士武器（剑、斧、锤等）
            if (stdItem->shape >= 1 && stdItem->shape <= 9) {
                return job == JobType::WARRIOR;
            }

            // 其他武器默认所有职业都可以使用
            return true;
        }

        case ItemType::ARMOR:
        case ItemType::HELMET:
        case ItemType::BOOTS:
        case ItemType::BELT: {
            // 防具通常没有严格的职业限制，但可能有推荐职业
            // 这里可以根据需要添加特定的职业限制逻辑
            return true;
        }

        case ItemType::NECKLACE:
        case ItemType::RING:
        case ItemType::BRACELET:
        case ItemType::CHARM: {
            // 首饰类物品通常没有职业限制
            return true;
        }

        default:
            // 其他物品默认没有职业限制
            return true;
    }
}

// 检查性别限制
bool ItemManager::CheckGenderRestriction(const StdItem* stdItem, GenderType gender) const {
    if (!stdItem) return false;

    // 根据物品类型和形状检查性别限制
    ItemType itemType = static_cast<ItemType>(stdItem->stdMode);

    switch (itemType) {
        case ItemType::ARMOR: {
            // 衣服有性别限制
            // 通常偶数形状为男性，奇数形状为女性
            if (stdItem->shape % 2 == 0) {
                return gender == GenderType::MALE;
            } else {
                return gender == GenderType::FEMALE;
            }
        }

        case ItemType::HELMET: {
            // 头盔可能有性别限制
            // 根据具体的形状值判断
            if (stdItem->shape >= 100 && stdItem->shape < 200) {
                // 女性头盔
                return gender == GenderType::FEMALE;
            } else if (stdItem->shape >= 200 && stdItem->shape < 300) {
                // 男性头盔
                return gender == GenderType::MALE;
            }
            // 其他头盔没有性别限制
            return true;
        }

        case ItemType::WEAPON:
        case ItemType::NECKLACE:
        case ItemType::RING:
        case ItemType::BRACELET:
        case ItemType::CHARM:
        case ItemType::BOOTS:
        case ItemType::BELT: {
            // 这些物品通常没有性别限制
            return true;
        }

        default:
            // 其他物品默认没有性别限制
            return true;
    }
}

// 特殊物品叠加检查
bool ItemManager::IsSpecialStackableItem(const StdItem* stdItem) const {
    if (!stdItem) return false;

    // 根据StdMode值判断特殊物品类型
    switch (stdItem->stdMode) {
        case 0:  // 药品类 - 可叠加
            return true;

        case 1:  // 金币 - 可叠加
            return true;

        case 2:  // 技能书/任务物品 - 通常不叠加
            return false;

        case 3:  // 卷轴类 - 可叠加
            return true;

        case 4:  // 肉类 - 可叠加
            return true;

        case 6:  // 特殊物品 - 根据Shape判断
            // 某些特殊物品可能可以叠加，如箭矢、宝石等
            if (stdItem->shape >= 1 && stdItem->shape <= 10) {
                return true;  // 箭矢类
            }
            return false;

        case 40: // 肉类食物 - 可叠加
            return true;

        case 41: // 特殊消耗品 - 可叠加
            return true;

        case 42: // 材料类 - 可叠加
            return true;

        case 43: // 宝石类 - 可叠加
            return true;

        case 44: // 矿石类 - 可叠加
            return true;

        case 45: // 药材类 - 可叠加
            return true;

        default:
            // 其他未知类型默认不叠加
            return false;
    }
}

// ==================== 物品强化系统实现 ====================

// 初始化强化系统
void ItemManager::InitializeUpgradeSystem() {
    // 加载强化材料映射
    LoadUpgradeMaterials();

    Logger::Info("Item upgrade system initialized");
}

// 加载强化材料映射
void ItemManager::LoadUpgradeMaterials() {
    // 根据原项目的物品ID映射强化材料
    // 这些ID需要根据实际的物品数据库调整

    // 黑铁矿石系列
    m_materialMap[100] = UpgradeMaterial::BLACK_IRON;  // 黑铁矿石
    m_materialMap[101] = UpgradeMaterial::BLACK_IRON;  // 纯度1黑铁
    m_materialMap[102] = UpgradeMaterial::BLACK_IRON;  // 纯度2黑铁
    m_materialMap[103] = UpgradeMaterial::BLACK_IRON;  // 纯度3黑铁
    m_materialMap[104] = UpgradeMaterial::BLACK_IRON;  // 纯度4黑铁
    m_materialMap[105] = UpgradeMaterial::BLACK_IRON;  // 纯度5黑铁

    // 银矿石
    m_materialMap[110] = UpgradeMaterial::SILVER_ORE;

    // 金矿石
    m_materialMap[111] = UpgradeMaterial::GOLD_ORE;

    // 钻石
    m_materialMap[112] = UpgradeMaterial::DIAMOND;

    // 祝福油
    m_materialMap[120] = UpgradeMaterial::BLESSING_OIL;

    // 灵魂宝石
    m_materialMap[121] = UpgradeMaterial::SOUL_GEM;

    // 记忆套装
    m_materialMap[130] = UpgradeMaterial::MEMORY_HELMET;   // 记忆头盔
    m_materialMap[131] = UpgradeMaterial::MEMORY_NECKLACE; // 记忆项链
    m_materialMap[132] = UpgradeMaterial::MEMORY_BRACELET; // 记忆手镯
    m_materialMap[133] = UpgradeMaterial::MEMORY_RING;     // 记忆戒指

    Logger::Info("Loaded " + std::to_string(m_materialMap.size()) + " upgrade materials");
}

// 使用材料强化物品
ItemManager::UpgradeResult ItemManager::UpgradeItemWithMaterials(UserItem& item,
                                                                const std::vector<UserItem>& materials,
                                                                DWORD& cost) const {
    // 验证物品是否可以强化
    if (!CanUpgradeItem(item)) {
        return UpgradeResult::INVALID_ITEM;
    }

    // 获取当前强化等级
    int currentLevel = GetItemUpgradeLevel(item);

    // 检查是否已达最大等级
    if (currentLevel >= m_upgradeConfig.maxUpgradeLevel) {
        return UpgradeResult::MAX_LEVEL_REACHED;
    }

    // 验证强化材料
    if (!ValidateUpgradeMaterials(materials)) {
        return UpgradeResult::INVALID_MATERIAL;
    }

    // 计算强化费用
    cost = CalculateUpgradeCost(item);

    // 计算成功率
    int successRate = CalculateUpgradeSuccessRate(item, materials);

    // 生成随机数判断是否成功
    std::uniform_int_distribution<> dist(1, 100);
    int roll = dist(m_randomEngine);

    if (roll <= successRate) {
        // 强化成功
        int newLevel = currentLevel + 1;
        ApplyUpgradeEffect(item, newLevel);
        UpdateItemName(item);

        Logger::Debug("Item upgrade success: " + item.itemName + " -> Level " + std::to_string(newLevel));
        return UpgradeResult::SUCCESS;
    } else {
        // 强化失败
        bool shouldDestroy = ShouldDestroyOnFailure(currentLevel);

        if (shouldDestroy) {
            // 物品被摧毁
            ApplyUpgradeFailure(item, true);
            Logger::Debug("Item destroyed during upgrade: " + item.itemName);
            return UpgradeResult::ITEM_DESTROYED;
        } else {
            // 强化失败但物品保留
            ApplyUpgradeFailure(item, false);
            Logger::Debug("Item upgrade failed: " + item.itemName);
            return UpgradeResult::FAILED;
        }
    }
}

// 计算强化成功率
int ItemManager::CalculateUpgradeSuccessRate(const UserItem& item,
                                           const std::vector<UserItem>& materials) const {
    int currentLevel = GetItemUpgradeLevel(item);

    // 基础成功率
    int baseRate = CalculateBasicSuccessRate(currentLevel);

    // 材料加成
    int materialBonus = CalculateMaterialBonus(materials);

    // 最终成功率
    int finalRate = baseRate + materialBonus;

    // 限制在最小和最大值之间
    finalRate = std::max(m_upgradeConfig.minSuccessRate, finalRate);
    finalRate = std::min(100, finalRate);

    return finalRate;
}

// 计算强化费用
DWORD ItemManager::CalculateUpgradeCost(const UserItem& item) const {
    int currentLevel = GetItemUpgradeLevel(item);

    // 基础费用 * (倍数 ^ 等级)
    DWORD cost = static_cast<DWORD>(m_upgradeConfig.baseCost *
                                   std::pow(m_upgradeConfig.costMultiplier, currentLevel));

    return cost;
}

// 检查物品是否可以强化
bool ItemManager::CanUpgradeItem(const UserItem& item) const {
    const StdItem* stdItem = GetStdItem(item.itemIndex);
    if (!stdItem) {
        return false;
    }

    // 只有装备类物品可以强化
    ItemType itemType = static_cast<ItemType>(stdItem->stdMode);
    switch (itemType) {
        case ItemType::WEAPON:
        case ItemType::ARMOR:
        case ItemType::HELMET:
        case ItemType::NECKLACE:
        case ItemType::RING:
        case ItemType::BRACELET:
        case ItemType::BELT:
        case ItemType::BOOTS:
        case ItemType::CHARM:
            return true;
        default:
            return false;
    }
}

// 获取物品强化等级
int ItemManager::GetItemUpgradeLevel(const UserItem& item) const {
    // 强化等级存储在btValue[13]中（根据原项目逻辑）
    return static_cast<int>(item.btValue[13]);
}

// 设置物品强化等级
void ItemManager::SetItemUpgradeLevel(UserItem& item, int level) const {
    // 限制等级范围
    level = std::max(0, std::min(level, m_upgradeConfig.maxUpgradeLevel));
    item.btValue[13] = static_cast<BYTE>(level);

    // 更新ItemValue结构
    item.value.FromBytes(item.btValue);
}

// 检查是否为强化材料
bool ItemManager::IsUpgradeMaterial(WORD itemIdx) const {
    return m_materialMap.find(itemIdx) != m_materialMap.end();
}

// 获取材料类型
ItemManager::UpgradeMaterial ItemManager::GetMaterialType(WORD itemIdx) const {
    auto it = m_materialMap.find(itemIdx);
    if (it != m_materialMap.end()) {
        return it->second;
    }
    return UpgradeMaterial::NONE;
}

// 获取材料加成
int ItemManager::GetMaterialBonus(UpgradeMaterial material, int count) const {
    switch (material) {
        case UpgradeMaterial::BLACK_IRON:
            return count * 2;  // 每个黑铁矿石增加2%成功率
        case UpgradeMaterial::SILVER_ORE:
            return count * 5;  // 每个银矿石增加5%成功率
        case UpgradeMaterial::GOLD_ORE:
            return count * 8;  // 每个金矿石增加8%成功率
        case UpgradeMaterial::DIAMOND:
            return count * 12; // 每个钻石增加12%成功率
        case UpgradeMaterial::BLESSING_OIL:
            return count * 15; // 每个祝福油增加15%成功率
        case UpgradeMaterial::SOUL_GEM:
            return count * 20; // 每个灵魂宝石增加20%成功率
        case UpgradeMaterial::MEMORY_HELMET:
        case UpgradeMaterial::MEMORY_NECKLACE:
        case UpgradeMaterial::MEMORY_BRACELET:
        case UpgradeMaterial::MEMORY_RING:
            return count * 25; // 记忆套装增加25%成功率
        default:
            return 0;
    }
}

// 应用强化效果
void ItemManager::ApplyUpgradeEffect(UserItem& item, int newLevel) const {
    // 设置新的强化等级
    SetItemUpgradeLevel(item, newLevel);

    // 根据强化等级增加属性
    const StdItem* stdItem = GetStdItem(item.itemIndex);
    if (!stdItem) return;

    ItemType itemType = static_cast<ItemType>(stdItem->stdMode);

    // 每级强化增加的属性值
    int attributeBonus = newLevel;

    switch (itemType) {
        case ItemType::WEAPON: {
            // 武器强化增加攻击力
            item.btValue[0] = std::min(255, static_cast<int>(item.btValue[0]) + attributeBonus); // DC
            item.btValue[1] = std::min(255, static_cast<int>(item.btValue[1]) + attributeBonus); // MC
            item.btValue[2] = std::min(255, static_cast<int>(item.btValue[2]) + attributeBonus); // SC
            break;
        }
        case ItemType::ARMOR:
        case ItemType::HELMET:
        case ItemType::BOOTS:
        case ItemType::BELT: {
            // 防具强化增加防御力
            item.btValue[3] = std::min(255, static_cast<int>(item.btValue[3]) + attributeBonus); // AC
            item.btValue[4] = std::min(255, static_cast<int>(item.btValue[4]) + attributeBonus); // MAC
            break;
        }
        case ItemType::NECKLACE:
        case ItemType::RING:
        case ItemType::BRACELET:
        case ItemType::CHARM: {
            // 首饰强化增加各种属性
            item.btValue[0] = std::min(255, static_cast<int>(item.btValue[0]) + attributeBonus / 2); // DC
            item.btValue[1] = std::min(255, static_cast<int>(item.btValue[1]) + attributeBonus / 2); // MC
            item.btValue[2] = std::min(255, static_cast<int>(item.btValue[2]) + attributeBonus / 2); // SC
            item.btValue[3] = std::min(255, static_cast<int>(item.btValue[3]) + attributeBonus / 2); // AC
            item.btValue[4] = std::min(255, static_cast<int>(item.btValue[4]) + attributeBonus / 2); // MAC
            break;
        }
        default:
            break;
    }

    // 更新ItemValue结构
    item.value.FromBytes(item.btValue);
}

// 应用强化失败效果
void ItemManager::ApplyUpgradeFailure(UserItem& item, bool destroyed) const {
    if (destroyed) {
        // 物品被摧毁，清空物品数据
        item.itemIndex = 0;
        item.itemName = "";
        item.dura = 0;
        item.duraMax = 0;
        memset(item.btValue, 0, sizeof(item.btValue));
        item.value.FromBytes(item.btValue);
    } else {
        // 强化失败但物品保留，可能降低持久度
        if (item.dura > 10) {
            item.dura -= 10;  // 降低10点持久度
        }
    }
}

// 设置强化配置
void ItemManager::SetUpgradeConfig(const UpgradeConfig& config) {
    m_upgradeConfig = config;
}

// 获取强化配置
const ItemManager::UpgradeConfig& ItemManager::GetUpgradeConfig() const {
    return m_upgradeConfig;
}

// 验证强化材料
bool ItemManager::ValidateUpgradeMaterials(const std::vector<UserItem>& materials) const {
    if (materials.empty()) {
        return false;  // 至少需要一个材料
    }

    for (const auto& material : materials) {
        if (!IsUpgradeMaterial(material.itemIndex)) {
            return false;  // 包含无效材料
        }
    }

    return true;
}

// 计算基础成功率
int ItemManager::CalculateBasicSuccessRate(int currentLevel) const {
    int baseRate = m_upgradeConfig.baseSuccessRate -
                   (currentLevel * m_upgradeConfig.successRateDecrement);

    return std::max(m_upgradeConfig.minSuccessRate, baseRate);
}

// 计算材料加成
int ItemManager::CalculateMaterialBonus(const std::vector<UserItem>& materials) const {
    int totalBonus = 0;

    // 统计各种材料的数量
    std::unordered_map<UpgradeMaterial, int> materialCounts;

    for (const auto& material : materials) {
        UpgradeMaterial materialType = GetMaterialType(material.itemIndex);
        if (materialType != UpgradeMaterial::NONE) {
            materialCounts[materialType] += material.dura;  // 使用持久度作为数量
        }
    }

    // 计算总加成
    for (const auto& pair : materialCounts) {
        totalBonus += GetMaterialBonus(pair.first, pair.second);
    }

    return totalBonus;
}

// 判断是否应该在失败时摧毁物品
bool ItemManager::ShouldDestroyOnFailure(int currentLevel) const {
    if (!m_upgradeConfig.canDestroy) {
        return false;
    }

    if (currentLevel < m_upgradeConfig.destroyStartLevel) {
        return false;
    }

    // 生成随机数判断是否摧毁
    std::uniform_int_distribution<> dist(1, 100);
    int roll = dist(m_randomEngine);

    return roll <= m_upgradeConfig.destroyRate;
}

// 更新物品名称（添加强化等级显示）
void ItemManager::UpdateItemName(UserItem& item) const {
    int upgradeLevel = GetItemUpgradeLevel(item);

    if (upgradeLevel > 0) {
        // 获取基础物品名称
        const StdItem* stdItem = GetStdItem(item.itemIndex);
        if (stdItem) {
            std::string baseName = stdItem->name;

            // 移除已有的强化等级标记
            size_t pos = baseName.find(" +");
            if (pos != std::string::npos) {
                baseName = baseName.substr(0, pos);
            }

            // 添加新的强化等级标记
            item.itemName = baseName + " +" + std::to_string(upgradeLevel);
        }
    } else {
        // 没有强化等级，使用原始名称
        const StdItem* stdItem = GetStdItem(item.itemIndex);
        if (stdItem) {
            item.itemName = stdItem->name;
        }
    }
}

} // namespace MirServer
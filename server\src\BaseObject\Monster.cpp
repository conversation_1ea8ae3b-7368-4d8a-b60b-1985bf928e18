// Monster.cpp - 怪物对象实现
#include "Monster.h"
#include "PlayObject.h"
#include "../Protocol/PacketTypes.h"
#include <algorithm>
#include <random>

namespace MirServer {

// ==================== Monster基类实现 ====================

Monster::Monster() : BaseObject() {
    // 设置怪物基本属性
    m_viewRange = 9;
    m_light = 0; // 怪物通常没有光源
    m_moveInterval = 1000;
    m_lastMoveTime = GetCurrentTime();
    m_lastTargetSearchTime = GetCurrentTime();
}

Monster::~Monster() {
    m_currentTarget = nullptr;
    m_master = nullptr;
}

void Monster::Initialize() {
    // 记录出生点
    m_spawnPoint = m_currentPos;
    
    // 根据等级设置基础属性
    SetMonsterLevel(m_level);
    
    // 初始化AI状态
    m_aiState = MonsterAIState::IDLE;
}

void Monster::Finalize() {
    // 清理目标
    m_currentTarget = nullptr;
}

void Monster::Run() {
    // 死亡状态下检查重生
    if (IsDead()) {
        DWORD currentTime = GetCurrentTime();
        if (m_deathTime > 0 && currentTime - m_deathTime >= m_respawnTime) {
            // 重生
            Revive();
            SpaceMove(m_mapName, m_spawnPoint.x, m_spawnPoint.y);
            m_deathTime = 0;
        }
        return;
    }
    
    // 处理AI
    ProcessAI();
}

void Monster::ProcessAI() {
    DWORD currentTime = GetCurrentTime();
    
    // 定期搜索目标
    if (currentTime - m_lastTargetSearchTime >= 1000) {
        m_currentTarget = SearchTarget();
        m_lastTargetSearchTime = currentTime;
    }
    
    // 根据当前状态处理
    switch (m_aiState) {
        case MonsterAIState::IDLE:
            ProcessIdleState();
            break;
        case MonsterAIState::PATROL:
            ProcessPatrolState();
            break;
        case MonsterAIState::CHASE:
            ProcessChaseState();
            break;
        case MonsterAIState::ATTACK:
            ProcessAttackState();
            break;
        case MonsterAIState::FLEE:
            ProcessFleeState();
            break;
        case MonsterAIState::RETURN:
            ProcessReturnState();
            break;
    }
}

void Monster::ProcessIdleState() {
    // 如果有目标，切换到追击状态
    if (m_currentTarget && IsValidTarget(m_currentTarget)) {
        SetAIState(MonsterAIState::CHASE);
        return;
    }
    
    // 被动怪物不主动巡逻
    if (m_monsterType == MonsterType::PASSIVE) {
        return;
    }
    
    // 随机切换到巡逻状态
    if (GenerateRandom(1, 100) <= 20) {
        SetAIState(MonsterAIState::PATROL);
    }
}

void Monster::ProcessPatrolState() {
    // 如果有目标，切换到追击状态
    if (m_currentTarget && IsValidTarget(m_currentTarget)) {
        SetAIState(MonsterAIState::CHASE);
        return;
    }
    
    DWORD currentTime = GetCurrentTime();
    if (currentTime - m_lastMoveTime >= m_moveInterval) {
        RandomMove();
        m_lastMoveTime = currentTime;
        
        // 巡逻一段时间后返回空闲
        if (GenerateRandom(1, 100) <= 30) {
            SetAIState(MonsterAIState::IDLE);
        }
    }
}

void Monster::ProcessChaseState() {
    // 检查目标是否有效
    if (!m_currentTarget || !IsValidTarget(m_currentTarget)) {
        // 检查是否离出生点太远
        double distanceFromSpawn = GetDistance(m_currentPos, m_spawnPoint);
        if (distanceFromSpawn > m_chaseRange * 2) {
            SetAIState(MonsterAIState::RETURN);
        } else {
            SetAIState(MonsterAIState::IDLE);
        }
        m_currentTarget = nullptr;
        return;
    }
    
    // 检查是否在攻击范围内
    double distance = GetDistance(m_currentPos, m_currentTarget->GetCurrentPos());
    if (distance <= m_attackRange) {
        SetAIState(MonsterAIState::ATTACK);
        return;
    }
    
    // 移动到目标
    DWORD currentTime = GetCurrentTime();
    if (currentTime - m_lastMoveTime >= m_moveInterval) {
        if (MoveToTarget(m_currentTarget)) {
            m_lastMoveTime = currentTime;
        }
    }
}

void Monster::ProcessAttackState() {
    // 检查目标是否有效
    if (!m_currentTarget || !IsValidTarget(m_currentTarget)) {
        SetAIState(MonsterAIState::IDLE);
        m_currentTarget = nullptr;
        return;
    }
    
    // 检查是否在攻击范围内
    double distance = GetDistance(m_currentPos, m_currentTarget->GetCurrentPos());
    if (distance > m_attackRange) {
        SetAIState(MonsterAIState::CHASE);
        return;
    }
    
    // 面向目标
    DirectionType dir = GetDirectionFromPoints(m_currentPos, m_currentTarget->GetCurrentPos());
    TurnTo(dir);
    
    // 执行攻击
    ProcessAttack();
}

void Monster::ProcessFleeState() {
    // 逃跑逻辑（低血量时触发）
    if (m_hp > m_maxHP * 0.3) {
        SetAIState(MonsterAIState::IDLE);
        return;
    }
    
    // TODO: 实现逃跑移动
}

void Monster::ProcessReturnState() {
    // 返回出生点
    double distance = GetDistance(m_currentPos, m_spawnPoint);
    if (distance <= 1) {
        SetAIState(MonsterAIState::IDLE);
        return;
    }
    
    DWORD currentTime = GetCurrentTime();
    if (currentTime - m_lastMoveTime >= m_moveInterval) {
        if (MoveToPoint(m_spawnPoint)) {
            m_lastMoveTime = currentTime;
        }
    }
}

BaseObject* Monster::SearchTarget() {
    // 召唤物优先攻击主人的目标
    if (m_master && m_master->GetTargetCreature()) {
        BaseObject* masterTarget = m_master->GetTargetCreature();
        if (IsValidTarget(masterTarget)) {
            return masterTarget;
        }
    }
    
    // 搜索视野内的对象
    std::vector<BaseObject*> viewObjects;
    GetViewObjects(viewObjects);
    
    BaseObject* nearestTarget = nullptr;
    double nearestDistance = m_aggroRange + 1;
    
    for (BaseObject* obj : viewObjects) {
        if (!IsValidTarget(obj)) continue;
        
        double distance = GetDistance(m_currentPos, obj->GetCurrentPos());
        if (distance <= m_aggroRange && distance < nearestDistance) {
            nearestDistance = distance;
            nearestTarget = obj;
        }
    }
    
    return nearestTarget;
}

bool Monster::IsValidTarget(BaseObject* target) const {
    if (!target || target == this || !target->IsAlive()) {
        return false;
    }
    
    // 召唤物不攻击主人
    if (m_master && target == m_master) {
        return false;
    }
    
    // 检查是否是可攻击的目标类型
    ObjectType targetType = target->GetObjectType();
    if (targetType == ObjectType::HUMAN) {
        // 被动怪物被攻击后才会反击
        if (m_monsterType == MonsterType::PASSIVE && 
            target->GetTargetCreature() != this) {
            return false;
        }
        return true;
    }
    
    // 怪物之间一般不互相攻击
    return false;
}

bool Monster::IsAttackTarget(const BaseObject* target) const {
    return IsValidTarget(const_cast<BaseObject*>(target));
}

bool Monster::IsProperTarget(const BaseObject* target) const {
    return IsAttackTarget(target) && CanSee(target);
}

void Monster::AttackTarget(BaseObject* target) {
    if (!target) return;
    
    SetTargetCreature(target);
    m_currentTarget = target;
    SetAIState(MonsterAIState::CHASE);
}

void Monster::BeAttacked(BaseObject* attacker, int damage) {
    if (!attacker || damage <= 0) return;
    
    // 扣除生命值
    m_hp = (damage >= m_hp) ? 0 : (m_hp - damage);
    OnHPChanged();
    
    // 被动怪物被攻击后会反击
    if (m_monsterType == MonsterType::PASSIVE && !m_currentTarget) {
        m_currentTarget = attacker;
        SetTargetCreature(attacker);
        SetAIState(MonsterAIState::CHASE);
    }
    
    // 低血量时可能逃跑
    if (m_hp < m_maxHP * 0.2 && m_monsterType != MonsterType::BOSS) {
        if (GenerateRandom(1, 100) <= 30) {
            SetAIState(MonsterAIState::FLEE);
        }
    }
    
    // 死亡处理
    if (m_hp <= 0) {
        Die();
    }
}

void Monster::ProcessAttack() {
    DWORD currentTime = GetCurrentTime();
    if (currentTime - m_lastAttackTime < m_attackSpeed) {
        return;
    }
    
    if (!m_currentTarget) return;
    
    // 计算伤害
    int damage = CalculateDamage(m_currentTarget);
    
    // 发送攻击动作
    SendDefMessage(Protocol::SM_HIT, 0, m_currentPos.x, m_currentPos.y, static_cast<WORD>(m_direction));
    
    // 造成伤害
    m_currentTarget->BeAttacked(this, damage);
    
    // 应用特殊效果
    ApplySpecialEffect(m_currentTarget);
    
    m_lastAttackTime = currentTime;
}

int Monster::CalculateDamage(BaseObject* target) {
    if (!target) return 0;
    
    // 基础伤害计算
    int minDC = m_level * 2;
    int maxDC = m_level * 3 + 5;
    int damage = GenerateRandom(minDC, maxDC);
    
    // TODO: 计算防御力减免
    
    return std::max(1, damage);
}

void Monster::ApplySpecialEffect(BaseObject* target) {
    if (!target) return;
    
    // 毒性攻击
    if (m_canPoison && GenerateRandom(1, 100) <= 30) {
        // TODO: 使目标中毒
    }
    
    // 麻痹攻击
    if (m_canParalyze && GenerateRandom(1, 100) <= 20) {
        // TODO: 使目标麻痹
    }
}

bool Monster::MoveToTarget(BaseObject* target) {
    if (!target) return false;
    
    Point targetPos = target->GetCurrentPos();
    DirectionType dir = GetDirectionFromPoints(m_currentPos, targetPos);
    Point nextPos = GetNextPosition(dir);
    
    // 先转向
    TurnTo(dir);
    
    // 检查是否可以移动
    if (!CanMove(nextPos)) {
        // 尝试其他方向
        return false;
    }
    
    // 执行移动
    m_currentPos = nextPos;
    OnPositionChanged();
    
    // 发送移动消息
    SendDefMessage(Protocol::SM_WALK, 0, nextPos.x, nextPos.y, static_cast<WORD>(dir));
    
    return true;
}

bool Monster::MoveToPoint(const Point& target) {
    DirectionType dir = GetDirectionFromPoints(m_currentPos, target);
    Point nextPos = GetNextPosition(dir);
    
    // 先转向
    TurnTo(dir);
    
    // 检查是否可以移动
    if (!CanMove(nextPos)) {
        return false;
    }
    
    // 执行移动
    m_currentPos = nextPos;
    OnPositionChanged();
    
    // 发送移动消息
    SendDefMessage(Protocol::SM_WALK, 0, nextPos.x, nextPos.y, static_cast<WORD>(dir));
    
    return true;
}

void Monster::RandomMove() {
    // 随机选择一个方向
    DirectionType newDir = static_cast<DirectionType>(GenerateRandom(0, 7));
    Point nextPos = GetNextPosition(newDir);
    
    // 不要离出生点太远
    double distanceFromSpawn = GetDistance(nextPos, m_spawnPoint);
    if (distanceFromSpawn > m_aggroRange * 2) {
        return;
    }
    
    if (CanMove(nextPos)) {
        TurnTo(newDir);
        m_currentPos = nextPos;
        OnPositionChanged();
        
        SendDefMessage(Protocol::SM_WALK, 0, nextPos.x, nextPos.y, static_cast<WORD>(newDir));
    }
}

void Monster::Die() {
    BaseObject::Die();
    
    // 记录死亡时间
    m_deathTime = GetCurrentTime();
    
    // 清理目标
    m_currentTarget = nullptr;
    SetTargetCreature(nullptr);
    
    // 生成掉落
    GenerateDrops();
    
    // 给予击杀者经验值
    BaseObject* killer = GetTargetCreature(); // TODO: 应该记录最后的攻击者
    if (killer && killer->GetObjectType() == ObjectType::HUMAN) {
        PlayObject* player = static_cast<PlayObject*>(killer);
        player->GainExp(m_expValue);
    }
}

void Monster::SetMonsterLevel(BYTE level) {
    m_level = level;
    
    // 根据等级设置属性
    m_maxHP = 50 + level * 20;
    m_hp = m_maxHP;
    
    // 经验值计算
    m_expValue = level * level * 10;
    
    // 根据类型调整属性
    switch (m_monsterType) {
        case MonsterType::ELITE:
            m_maxHP *= 3;
            m_hp = m_maxHP;
            m_expValue *= 5;
            break;
        case MonsterType::BOSS:
            m_maxHP *= 10;
            m_hp = m_maxHP;
            m_expValue *= 20;
            break;
        default:
            break;
    }
}

void Monster::AddSkill(WORD skillId, BYTE level) {
    MonsterSkill skill;
    skill.skillId = skillId;
    skill.level = level;
    skill.cooldown = 5000; // 默认5秒冷却
    skill.lastUseTime = 0;
    m_skills.push_back(skill);
}

bool Monster::UseSkill(WORD skillId, BaseObject* target) {
    // TODO: 实现技能使用
    return false;
}

void Monster::AddDropItem(const DropItem& item) {
    m_dropItems.push_back(item);
}

void Monster::ClearDropItems() {
    m_dropItems.clear();
}

void Monster::GenerateDrops() {
    for (const DropItem& dropItem : m_dropItems) {
        // 计算是否掉落
        float roll = static_cast<float>(GenerateRandom(0, 10000)) / 10000.0f;
        if (roll <= dropItem.dropRate) {
            int count = GenerateRandom(dropItem.minCount, dropItem.maxCount);
            
            // TODO: 在死亡位置生成物品
            for (int i = 0; i < count; i++) {
                // CreateMapItem(dropItem.itemIndex, m_currentPos);
            }
        }
    }
    
    // 金币掉落
    if (GenerateRandom(1, 100) <= 60) {
        int goldAmount = GenerateRandom(m_level * 10, m_level * 50);
        // TODO: CreateGoldDrop(goldAmount, m_currentPos);
    }
}

void Monster::OnStateChanged() {
    // AI状态改变时的处理
}

void Monster::OnHPChanged() {
    // 生命值改变时的处理
    if (m_hp <= 0) {
        Die();
    }
}

// ==================== EliteMonster实现 ====================

EliteMonster::EliteMonster() : Monster() {
    SetMonsterType(MonsterType::ELITE);
}

EliteMonster::~EliteMonster() {
}

void EliteMonster::Initialize() {
    Monster::Initialize();
    
    // 精英怪物属性加成
    m_attackSpeed = 800; // 攻击速度更快
    SetAggroRange(10);   // 更大的仇恨范围
    SetChaseRange(20);   // 更大的追击范围
}

int EliteMonster::CalculateDamage(BaseObject* target) {
    // 精英怪物伤害加成50%
    int damage = Monster::CalculateDamage(target);
    return static_cast<int>(damage * 1.5);
}

// ==================== BossMonster实现 ====================

BossMonster::BossMonster() : Monster() {
    SetMonsterType(MonsterType::BOSS);
}

BossMonster::~BossMonster() {
}

void BossMonster::Initialize() {
    Monster::Initialize();
    
    // BOSS属性
    m_attackSpeed = 1500; // 攻击较慢但伤害高
    SetAggroRange(15);
    SetChaseRange(30);
    SetViewRange(20);
    
    // BOSS默认免疫
    m_stunImmune = true;
    m_poisonImmune = true;
    m_paralysisImmune = true;
}

void BossMonster::Die() {
    Monster::Die();
    
    // BOSS死亡公告
    // TODO: BroadcastMessage(m_charName + " 已被击败！");
    
    // BOSS特殊掉落
    // TODO: 生成稀有物品
}

void BossMonster::ProcessAI() {
    // 处理BOSS阶段
    ProcessBossPhase();
    
    // 执行基础AI
    Monster::ProcessAI();
}

void BossMonster::ProcessBossPhase() {
    // 根据血量判断阶段
    float hpPercent = static_cast<float>(m_hp) / m_maxHP;
    
    int newPhase = 1;
    if (hpPercent <= 0.3) {
        newPhase = 3;
    } else if (hpPercent <= 0.6) {
        newPhase = 2;
    }
    
    if (newPhase != m_currentPhase) {
        m_currentPhase = newPhase;
        
        // TODO: 触发阶段转换效果
        // 使用阶段技能
        auto it = m_phaseSkills.find(m_currentPhase);
        if (it != m_phaseSkills.end()) {
            for (WORD skillId : it->second) {
                UseSkill(skillId, m_currentTarget);
            }
        }
    }
}

void BossMonster::SetImmunities(bool stunImmune, bool poisonImmune, bool paralysisImmune) {
    m_stunImmune = stunImmune;
    m_poisonImmune = poisonImmune;
    m_paralysisImmune = paralysisImmune;
}

void BossMonster::AddPhaseSkill(int phase, WORD skillId) {
    m_phaseSkills[phase].push_back(skillId);
}

} // namespace MirServer 
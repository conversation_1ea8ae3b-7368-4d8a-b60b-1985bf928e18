#include "GuildManager.h"
#include "../Common/Logger.h"
#include <iostream>
#include <memory>

using namespace MirServer;

// 简单的模拟PlayObject类，只用于测试
class MockPlayObject {
public:
    MockPlayObject(const std::string& name) : m_name(name) {
        m_humData.charName = name;
        m_humData.level = 50;
        m_humData.job = JobType::WARRIOR;
    }

    const std::string& GetCharName() const { return m_name; }
    const HumDataInfo& GetHumDataInfo() const { return m_humData; }
    HumDataInfo& GetHumDataInfo() { return m_humData; }

    const std::string& GetGuildName() const { return m_humData.guildName; }
    BYTE GetGuildRank() const { return m_humData.guildRank; }

    void SendMessage(const std::string& msg, int type) {
        std::cout << "[" << m_name << "] " << msg << std::endl;
    }

    void SendDefMessage(WORD cmd, WORD param1, WORD param2, WORD param3, WORD param4) {
        std::cout << "[" << m_name << "] DefMessage: " << cmd << std::endl;
    }

private:
    std::string m_name;
    HumDataInfo m_humData;
};

int main() {
    std::cout << "=== Basic Guild System Test ===" << std::endl;

    try {
        // 初始化日志系统
        std::cout << "1. Initializing logger..." << std::endl;
        Logger::SetLogFile("basic_guild_test.log");
        Logger::SetLogLevel(LogLevel::LOG_DEBUG);
        std::cout << "   Logger initialized." << std::endl;

        // 跳过行会管理器初始化，直接测试Guild类
        std::cout << "2. Skipping guild manager initialization for now..." << std::endl;

        // 创建模拟玩家
        std::cout << "3. Creating mock players..." << std::endl;
        auto player1 = std::make_unique<MockPlayObject>("TestPlayer1");
        auto player2 = std::make_unique<MockPlayObject>("TestPlayer2");

        std::cout << "   Mock players created: " << player1->GetCharName()
                  << ", " << player2->GetCharName() << std::endl;

        // 测试1: 创建行会
        std::cout << "4. Testing guild creation..." << std::endl;

        // 直接创建Guild对象进行测试
        auto guild = std::make_unique<Guild>("TestGuild");
        if (!guild) {
            std::cout << "   ✗ Guild creation failed" << std::endl;
            return 1;
        }

        std::cout << "   ✓ Guild created successfully: " << guild->GetGuildName() << std::endl;

        // 测试2: 设置行会信息
        std::cout << "5. Testing guild info setup..." << std::endl;
        bool setupResult = guild->SetGuildInfo("TestPlayer1");
        if (setupResult) {
            std::cout << "   ✓ Guild info set successfully" << std::endl;
            std::cout << "   ✓ Chief: " << guild->GetChiefName() << std::endl;
            std::cout << "   ✓ Member count: " << guild->GetMemberCount() << std::endl;
        } else {
            std::cout << "   ✗ Guild info setup failed" << std::endl;
        }

        // 测试3: 行会属性
        std::cout << "6. Testing guild properties..." << std::endl;
        guild->SetBuildPoint(1000);
        guild->SetAurae(500);
        guild->SetStability(800);
        guild->SetFlourishing(600);

        std::cout << "   ✓ Build Point: " << guild->GetBuildPoint() << std::endl;
        std::cout << "   ✓ Aurae: " << guild->GetAurae() << std::endl;
        std::cout << "   ✓ Stability: " << guild->GetStability() << std::endl;
        std::cout << "   ✓ Flourishing: " << guild->GetFlourishing() << std::endl;

        // 测试4: 行会公告
        std::cout << "7. Testing guild notices..." << std::endl;
        guild->AddNotice("Welcome to our guild!");
        guild->AddNotice("Guild event tonight at 8 PM");
        guild->AddNotice("Please participate in guild activities");

        const auto& notices = guild->GetNotices();
        std::cout << "   ✓ Notice count: " << notices.size() << std::endl;
        for (size_t i = 0; i < notices.size(); ++i) {
            std::cout << "     " << (i + 1) << ". " << notices[i] << std::endl;
        }

        // 测试5: 清除公告
        std::cout << "8. Testing notice clearing..." << std::endl;
        guild->ClearNotices();
        const auto& clearedNotices = guild->GetNotices();
        std::cout << "   ✓ Notices after clearing: " << clearedNotices.size() << std::endl;

        // 测试6: 团队战功能
        std::cout << "9. Testing team fight features..." << std::endl;
        guild->StartTeamFight();
        std::cout << "   ✓ Team fight started: " << (guild->IsTeamFightActive() ? "Active" : "Inactive") << std::endl;

        guild->TeamFightWhoWinPoint("TestPlayer1", 100);
        std::cout << "   ✓ Contest points: " << guild->GetContestPoint() << std::endl;

        guild->EndTeamFight();
        std::cout << "   ✓ Team fight ended: " << (guild->IsTeamFightActive() ? "Active" : "Inactive") << std::endl;

        // 测试7: 运行时处理
        std::cout << "10. Testing runtime processing..." << std::endl;
        guild->Run();
        std::cout << "    ✓ Guild runtime processing completed" << std::endl;

        // 测试8: 文件操作
        std::cout << "11. Testing file operations..." << std::endl;
        bool saveResult = guild->SaveToFile();
        std::cout << "    ✓ Save to file: " << (saveResult ? "Success" : "Failed") << std::endl;

        bool loadResult = guild->LoadFromFile();
        std::cout << "    ✓ Load from file: " << (loadResult ? "Success" : "Failed") << std::endl;

        // 测试9: 创建第二个行会测试联盟和战争
        std::cout << "12. Testing guild alliance and war..." << std::endl;
        auto guild2 = std::make_unique<Guild>("TestGuild2");
        guild2->SetGuildInfo("TestPlayer2");

        // 测试联盟
        bool allyResult = guild->AddAlly(guild2.get());
        std::cout << "    ✓ Alliance creation: " << (allyResult ? "Success" : "Failed") << std::endl;
        std::cout << "    ✓ Is ally: " << (guild->IsAlly(guild2.get()) ? "Yes" : "No") << std::endl;

        // 解除联盟
        bool removeAllyResult = guild->RemoveAlly(guild2.get());
        std::cout << "    ✓ Alliance removal: " << (removeAllyResult ? "Success" : "Failed") << std::endl;

        // 测试战争
        bool warResult = guild->StartWar(guild2.get());
        std::cout << "    ✓ War declaration: " << (warResult ? "Success" : "Failed") << std::endl;
        std::cout << "    ✓ Is at war: " << (guild->IsWarWith(guild2.get()) ? "Yes" : "No") << std::endl;

        // 停战
        bool peaceResult = guild->EndWar(guild2.get());
        std::cout << "    ✓ Peace declaration: " << (peaceResult ? "Success" : "Failed") << std::endl;

        // 清理
        std::cout << "13. Cleaning up..." << std::endl;
        guild.reset();
        guild2.reset();
        std::cout << "    ✓ Cleanup completed" << std::endl;

        std::cout << "\n=== All Basic Tests Passed Successfully! ===" << std::endl;
        std::cout << "Guild system core functionality is working correctly." << std::endl;
        return 0;

    } catch (const std::exception& e) {
        std::cerr << "Exception occurred: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown exception occurred" << std::endl;
        return 1;
    }
}

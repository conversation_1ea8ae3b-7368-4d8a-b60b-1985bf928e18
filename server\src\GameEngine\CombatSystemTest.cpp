// CombatSystemTest.cpp - 战斗系统测试
#include "../BaseObject/BaseObject.h"
#include "../Common/Logger.h"
#include "../Common/Types.h"
#include "../Protocol/PacketTypes.h"
#include <iostream>
#include <memory>
#include <cassert>

namespace MirServer {

class TestObject : public BaseObject {
public:
    TestObject(const std::string& name) {
        m_sCharName = name;
        m_btRaceServer = RC_PLAYOBJECT;

        // 初始化基础属性
        m_Abil.Level = 30;
        m_WAbil.HP = 1000;
        m_WAbil.MaxHP = 1000;
        m_WAbil.MP = 500;
        m_WAbil.MaxMP = 500;
        m_WAbil.DC.min = 50;
        m_WAbil.DC.max = 80;
        m_WAbil.AC.min = 10;
        m_WAbil.AC.max = 20;
        m_WAbil.MAC.min = 5;
        m_WAbil.MAC.max = 15;

        m_btHitPoint = 15;
        m_btSpeedPoint = 10;

        // 初始化装备
        m_useItems[U_WEAPON].wIndex = 1001; // 假设武器ID
        m_useItems[U_WEAPON].Dura = 5000;
        m_useItems[U_WEAPON].DuraMax = 5000;

        m_useItems[U_DRESS].wIndex = 2001; // 假设衣服ID
        m_useItems[U_DRESS].Dura = 3000;
        m_useItems[U_DRESS].DuraMax = 3000;
    }

    // 重写虚函数以便测试
    ObjectType GetObjectType() const override {
        return ObjectType::Player;
    }

    bool IsProperTarget(const BaseObject* target) const override {
        return target && target != this && !target->IsDead();
    }

    void SendDefMessage(WORD msgType, WORD recog = 0, WORD param = 0, WORD tag = 0, WORD series = 0) override {
        std::cout << "SendDefMessage: " << msgType << " from " << m_sCharName << std::endl;
    }

    void SendMsg(BaseObject* obj, WORD msgId, int param1, int param2, int param3, int param4, const std::string& msg) override {
        std::cout << "SendMsg: " << msgId << " to " << (obj ? obj->GetCharName() : "null") << std::endl;
    }

    void SendDelayMsg(BaseObject* obj, WORD msgId, WORD subId, int param1, int param2, int param3, int param4, const std::string& msg, int delay) override {
        std::cout << "SendDelayMsg: " << msgId << "/" << subId << " delay=" << delay << std::endl;
    }

    int GetCharStatus() const override {
        return 0; // 正常状态
    }

    bool InSafeZone() const override {
        return false; // 不在安全区
    }
};

class CombatSystemTest {
public:
    static void RunAllTests() {
        std::cout << "=== 战斗系统测试开始 ===" << std::endl;

        TestBasicDamageCalculation();
        TestPhysicalAttack();
        TestMagicalAttack();
        TestEquipmentDamage();
        TestSpecialAttacks();
        TestPoisonSystem();
        TestCombatFlow();

        std::cout << "=== 战斗系统测试完成 ===" << std::endl;
    }

private:
    static void TestBasicDamageCalculation() {
        std::cout << "\n--- 测试基础伤害计算 ---" << std::endl;

        auto attacker = std::make_shared<TestObject>("攻击者");
        auto target = std::make_shared<TestObject>("目标");

        // 测试物理伤害计算
        int damage = 100;
        int finalDamage = target->GetHitStruckDamage(attacker.get(), damage);

        std::cout << "原始伤害: " << damage << ", 最终伤害: " << finalDamage << std::endl;
        assert(finalDamage >= 0 && finalDamage <= damage);

        // 测试魔法伤害计算
        int magDamage = 80;
        int finalMagDamage = target->GetMagStruckDamage(attacker.get(), magDamage);

        std::cout << "原始魔法伤害: " << magDamage << ", 最终魔法伤害: " << finalMagDamage << std::endl;
        assert(finalMagDamage >= 0 && finalMagDamage <= magDamage);

        std::cout << "基础伤害计算测试通过" << std::endl;
    }

    static void TestPhysicalAttack() {
        std::cout << "\n--- 测试物理攻击 ---" << std::endl;

        auto attacker = std::make_shared<TestObject>("战士");
        auto target = std::make_shared<TestObject>("怪物");

        int originalHP = target->GetHP();

        // 执行普通攻击
        WORD hitMode = 0; // 普通攻击
        bool attackResult = attacker->_Attack(hitMode, target.get());

        std::cout << "攻击结果: " << (attackResult ? "命中" : "未命中") << std::endl;
        std::cout << "目标生命值: " << originalHP << " -> " << target->GetHP() << std::endl;

        if (attackResult) {
            assert(target->GetHP() < originalHP);
        }

        std::cout << "物理攻击测试通过" << std::endl;
    }

    static void TestMagicalAttack() {
        std::cout << "\n--- 测试魔法攻击 ---" << std::endl;

        auto mage = std::make_shared<TestObject>("法师");
        auto target = std::make_shared<TestObject>("目标");

        // 设置法师属性（通过public接口）
        // 这里简化处理，实际应该通过public方法设置

        int originalHP = target->GetHP();
        int magicDamage = 60;

        // 直接测试魔法伤害
        int finalDamage = target->GetMagStruckDamage(mage.get(), magicDamage);
        target->StruckDamage(finalDamage);

        std::cout << "魔法伤害: " << magicDamage << " -> " << finalDamage << std::endl;
        std::cout << "目标生命值: " << originalHP << " -> " << target->GetHP() << std::endl;

        assert(target->GetHP() < originalHP);
        std::cout << "魔法攻击测试通过" << std::endl;
    }

    static void TestEquipmentDamage() {
        std::cout << "\n--- 测试装备损坏 ---" << std::endl;

        auto target = std::make_shared<TestObject>("测试者");

        std::cout << "测试装备损坏机制" << std::endl;

        // 造成大量伤害以测试装备损坏
        for (int i = 0; i < 10; ++i) {
            target->StruckDamage(50);
        }

        std::cout << "装备损坏测试完成" << std::endl;

        std::cout << "装备损坏测试通过" << std::endl;
    }

    static void TestSpecialAttacks() {
        std::cout << "\n--- 测试特殊攻击 ---" << std::endl;

        auto warrior = std::make_shared<TestObject>("战士");
        auto target = std::make_shared<TestObject>("目标");

        // 测试刺杀剑法
        bool longAttackResult = warrior->SwordLongAttack(target.get(), 100);
        std::cout << "刺杀攻击结果: " << (longAttackResult ? "成功" : "失败") << std::endl;

        // 测试半月弯刀
        bool wideAttackResult = warrior->SwordWideAttack(80);
        std::cout << "半月攻击结果: " << (wideAttackResult ? "成功" : "失败") << std::endl;

        // 测试野蛮冲撞
        bool crsAttackResult = warrior->CrsWideAttack(90);
        std::cout << "野蛮攻击结果: " << (crsAttackResult ? "成功" : "失败") << std::endl;

        std::cout << "特殊攻击测试通过" << std::endl;
    }

    static void TestPoisonSystem() {
        std::cout << "\n--- 测试中毒系统 ---" << std::endl;

        auto target = std::make_shared<TestObject>("中毒者");

        // 测试中毒
        bool poisonResult = target->MakePosion(POISON_GREEN, 10, 5);
        std::cout << "中毒结果: " << (poisonResult ? "成功" : "失败") << std::endl;

        assert(poisonResult);

        std::cout << "中毒系统测试通过" << std::endl;
    }

    static void TestCombatFlow() {
        std::cout << "\n--- 测试完整战斗流程 ---" << std::endl;

        auto player1 = std::make_shared<TestObject>("玩家1");
        auto player2 = std::make_shared<TestObject>("玩家2");

        std::cout << "战斗开始:" << std::endl;
        std::cout << "玩家1 HP: " << player1->GetHP() << std::endl;
        std::cout << "玩家2 HP: " << player2->GetHP() << std::endl;

        // 模拟几轮战斗
        for (int round = 1; round <= 5 && !player1->IsDead() && !player2->IsDead(); ++round) {
            std::cout << "\n第" << round << "轮:" << std::endl;

            // 玩家1攻击玩家2
            WORD hitMode1 = 0;
            bool attack1 = player1->_Attack(hitMode1, player2.get());
            std::cout << "玩家1攻击: " << (attack1 ? "命中" : "未命中") << ", 玩家2 HP: " << player2->GetHP() << std::endl;

            if (!player2->IsDead()) {
                // 玩家2反击
                WORD hitMode2 = 0;
                bool attack2 = player2->_Attack(hitMode2, player1.get());
                std::cout << "玩家2攻击: " << (attack2 ? "命中" : "未命中") << ", 玩家1 HP: " << player1->GetHP() << std::endl;
            }
        }

        std::cout << "\n战斗结束:" << std::endl;
        std::cout << "玩家1状态: " << (player1->IsDead() ? "死亡" : "存活") << " HP: " << player1->GetHP() << std::endl;
        std::cout << "玩家2状态: " << (player2->IsDead() ? "死亡" : "存活") << " HP: " << player2->GetHP() << std::endl;

        std::cout << "完整战斗流程测试通过" << std::endl;
    }
};

} // namespace MirServer

// 主测试函数
int main() {
    try {
        MirServer::CombatSystemTest::RunAllTests();
        std::cout << "\n所有测试通过！战斗系统实现正确。" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}

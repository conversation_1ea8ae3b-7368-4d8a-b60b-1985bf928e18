#pragma once

#include "../Common/Types.h"
#include "../BaseObject/Monster.h"
#include <unordered_map>
#include <vector>
#include <string>
#include <memory>
#include <shared_mutex>

namespace MirServer {

// 前向声明
class Environment;

// 怪物模板结构（对应delphi的TMonsterInfo）
struct MonsterTemplate {
    WORD monsterId = 0;                // 怪物ID
    std::string monsterName;           // 怪物名称
    MonsterType monsterType = MonsterType::NORMAL; // 怪物类型
    WORD appr = 0;                     // 外观
    BYTE level = 1;                    // 等级

    // 基础属性
    int hp = 100;                      // 生命值
    int mp = 0;                        // 魔法值
    int ac = 0;                        // 防御力
    int mac = 0;                       // 魔法防御
    int dc = 10;                       // 攻击力
    int mc = 0;                        // 魔法攻击
    int sc = 0;                        // 道术

    // 战斗属性
    int attackSpeed = 1000;            // 攻击速度（毫秒）
    int attackRange = 1;               // 攻击范围
    int viewRange = 9;                 // 视野范围
    int aggroRange = 7;                // 仇恨范围
    int chaseRange = 15;               // 追击范围

    // 移动属性
    int moveSpeed = 1000;              // 移动速度
    bool canMove = true;               // 是否可移动
    bool canAttack = true;             // 是否可攻击

    // 特殊能力
    bool canPoison = false;            // 是否可中毒攻击
    bool canParalyze = false;          // 是否可麻痹攻击
    int magicResist = 0;               // 魔法抗性
    int poisonResist = 0;              // 毒抗性

    // 经验和掉落
    DWORD expValue = 10;               // 经验值
    std::vector<DropItem> dropItems;   // 掉落物品

    // 技能列表
    std::vector<WORD> skills;          // 技能ID列表

    // 重生相关
    DWORD respawnTime = 60000;         // 重生时间（毫秒）
    bool canRespawn = true;            // 是否可重生

    // 描述
    std::string description;           // 描述
};

// 怪物刷新点结构
struct MonsterSpawnPoint {
    WORD monsterTemplateId = 0;        // 怪物模板ID
    std::string mapName;               // 地图名称
    Point position{0, 0};              // 位置
    int spawnRange = 0;                // 刷新范围（0表示固定位置）
    int maxCount = 1;                  // 最大数量
    int currentCount = 0;              // 当前数量
    DWORD respawnTime = 60000;         // 重生时间
    DWORD lastSpawnTime = 0;           // 上次刷新时间
    bool isActive = true;              // 是否激活

    // 刷新条件
    int minLevel = 0;                  // 最小等级要求
    int maxLevel = 999;                // 最大等级要求
    std::string spawnCondition;        // 刷新条件脚本
};

// 怪物管理器类
class MonsterManager {
public:
    MonsterManager();
    ~MonsterManager();

    // 初始化和清理
    bool Initialize();
    void Finalize();

    // 怪物模板管理
    bool LoadMonsterTemplates(const std::string& filename = "GameData/MonsterList.txt");
    bool AddMonsterTemplate(const MonsterTemplate& monsterTemplate);
    const MonsterTemplate* GetMonsterTemplate(WORD monsterId) const;
    void ClearMonsterTemplates();

    // 刷新点管理
    bool LoadSpawnPoints(const std::string& filename = "GameData/MonsterSpawn.txt");
    bool AddSpawnPoint(const MonsterSpawnPoint& spawnPoint);
    void ClearSpawnPoints();

    // 怪物创建和管理
    std::shared_ptr<Monster> CreateMonster(WORD monsterTemplateId, const std::string& mapName, const Point& pos);
    std::shared_ptr<Monster> CreateMonsterFromTemplate(const MonsterTemplate& monsterTemplate, const std::string& mapName, const Point& pos);
    bool SpawnMonster(const MonsterSpawnPoint& spawnPoint);
    void SpawnAllMonsters();

    // 怪物查找
    std::shared_ptr<Monster> FindMonster(uint32_t objectId) const;
    std::vector<std::shared_ptr<Monster>> GetMonstersInMap(const std::string& mapName) const;
    std::vector<std::shared_ptr<Monster>> GetMonstersInRange(const std::string& mapName, const Point& center, int range) const;
    std::vector<std::shared_ptr<Monster>> GetMonstersByType(MonsterType type) const;

    // 怪物移除
    bool RemoveMonster(uint32_t objectId);
    void RemoveAllMonsters();
    void RemoveMonstersInMap(const std::string& mapName);

    // 运行时更新
    void Run();
    void ProcessMonsters();
    void CheckRespawns();
    void ProcessAI();

    // 特殊怪物管理
    std::shared_ptr<BossMonster> CreateBoss(WORD monsterTemplateId, const std::string& mapName, const Point& pos);
    std::shared_ptr<EliteMonster> CreateElite(WORD monsterTemplateId, const std::string& mapName, const Point& pos);

    // 召唤怪物
    std::shared_ptr<Monster> SummonMonster(WORD monsterTemplateId, BaseObject* master, const std::string& mapName, const Point& pos, DWORD duration = 0);
    void RemoveSummonedMonsters(BaseObject* master);

    // 统计信息
    struct Statistics {
        int totalMonsters = 0;
        int aliveMonsters = 0;
        int normalMonsters = 0;
        int eliteMonsters = 0;
        int bossMonsters = 0;
        int summonedMonsters = 0;
        int activeSpawnPoints = 0;
        DWORD lastUpdateTime = 0;
    };

    const Statistics& GetStatistics() const { return m_statistics; }
    void UpdateStatistics();

    // 配置管理
    void SetGlobalRespawnRate(float rate) { m_globalRespawnRate = rate; }
    float GetGlobalRespawnRate() const { return m_globalRespawnRate; }
    void SetMaxMonstersPerMap(int max) { m_maxMonstersPerMap = max; }
    int GetMaxMonstersPerMap() const { return m_maxMonstersPerMap; }

private:
    // 数据解析
    bool ParseMonsterTemplateLine(const std::string& line, MonsterTemplate& monsterTemplate);
    bool ParseSpawnPointLine(const std::string& line, MonsterSpawnPoint& spawnPoint);
    bool ParseDropItemsString(const std::string& dropStr, std::vector<DropItem>& dropItems);

    // 怪物设置
    void SetupMonsterFromTemplate(std::shared_ptr<Monster> monster, const MonsterTemplate& monsterTemplate);
    Point GetRandomSpawnPosition(const MonsterSpawnPoint& spawnPoint);
    bool CanSpawnAt(const std::string& mapName, const Point& pos);

    // 刷新逻辑
    bool ShouldRespawn(const MonsterSpawnPoint& spawnPoint);
    void UpdateSpawnPointCount(const MonsterSpawnPoint& spawnPoint, int delta);

    // 环境管理
    void SetEnvironmentManager(std::shared_ptr<Environment> envManager) { m_envManager = envManager; }

private:
    bool m_initialized;

    // 怪物模板数据
    std::unordered_map<WORD, MonsterTemplate> m_monsterTemplates;

    // 刷新点数据
    std::vector<MonsterSpawnPoint> m_spawnPoints;
    std::unordered_map<std::string, std::vector<size_t>> m_spawnPointsByMap; // mapName -> spawnPoint indices

    // 活跃的怪物列表
    std::unordered_map<uint32_t, std::shared_ptr<Monster>> m_monsters; // objectId -> monster
    std::unordered_map<std::string, std::vector<uint32_t>> m_monstersByMap; // mapName -> objectIds

    // 召唤怪物管理
    std::unordered_map<uint32_t, std::vector<uint32_t>> m_summonedMonsters; // masterId -> monsterIds

    // 环境管理器
    std::shared_ptr<Environment> m_envManager;

    // 统计信息
    Statistics m_statistics;

    // 配置参数
    float m_globalRespawnRate = 1.0f;  // 全局重生速率倍数
    int m_maxMonstersPerMap = 1000;    // 每个地图最大怪物数量

    // 运行时状态
    DWORD m_lastRunTime = 0;
    DWORD m_lastRespawnCheckTime = 0;
    DWORD m_lastAIProcessTime = 0;
    DWORD m_respawnCheckInterval = 10000;  // 10秒检查一次重生
    DWORD m_aiProcessInterval = 1000;      // 1秒处理一次AI

    // 线程安全
    mutable std::shared_mutex m_monstersMutex;
    mutable std::mutex m_templatesMutex;
    mutable std::mutex m_spawnPointsMutex;
};

// 全局怪物管理器实例
extern std::unique_ptr<MonsterManager> g_MonsterManager;

} // namespace MirServer

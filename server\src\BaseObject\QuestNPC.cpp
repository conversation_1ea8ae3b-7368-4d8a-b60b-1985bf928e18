// QuestNPC.cpp - 任务NPC实现
#include "QuestNPC.h"
#include "PlayObject.h"
#include "../Common/Logger.h"

namespace MirServer {

QuestNPC::QuestNPC() : NPC() {
}

QuestNPC::~QuestNPC() {
}

void QuestNPC::Initialize() {
    NPC::Initialize();
}

void QuestNPC::AddQuest(const QuestInfo& quest) {
    m_quests[quest.questId] = quest;
}

void QuestNPC::RemoveQuest(WORD questId) {
    m_quests.erase(questId);
}

const QuestInfo* QuestNPC::GetQuest(WORD questId) const {
    auto it = m_quests.find(questId);
    return (it != m_quests.end()) ? &it->second : nullptr;
}

std::vector<WORD> QuestNPC::GetAvailableQuests(PlayObject* player) const {
    std::vector<WORD> availableQuests;
    
    if (!player) return availableQuests;
    
    for (const auto& pair : m_quests) {
        const QuestInfo& quest = pair.second;
        if (CanAcceptQuest(player, quest.questId)) {
            availableQuests.push_back(quest.questId);
        }
    }
    
    return availableQuests;
}

bool QuestNPC::CanAcceptQuest(PlayObject* player, WORD questId) const {
    if (!player) return false;
    
    const QuestInfo* quest = GetQuest(questId);
    if (!quest) return false;
    
    // 检查等级要求
    BYTE playerLevel = player->GetLevel();
    if (playerLevel < quest->minLevel || playerLevel > quest->maxLevel) {
        return false;
    }
    
    // 检查是否已经接受了这个任务
    // TODO: 实现玩家任务状态检查
    // if (player->HasQuest(questId)) {
    //     return false;
    // }
    
    // 检查是否已经完成过（如果不可重复）
    // if (!quest->repeatable && player->HasCompletedQuest(questId)) {
    //     return false;
    // }
    
    return CheckQuestRequirements(player, *quest);
}

bool QuestNPC::AcceptQuest(PlayObject* player, WORD questId) {
    if (!CanAcceptQuest(player, questId)) {
        return false;
    }
    
    const QuestInfo* quest = GetQuest(questId);
    if (!quest) return false;
    
    // TODO: 将任务添加到玩家的任务列表
    // player->AddQuest(questId);
    
    Logger::Info("Player " + player->GetCharName() + " accepted quest: " + quest->questName);
    return true;
}

bool QuestNPC::CanCompleteQuest(PlayObject* player, WORD questId) const {
    if (!player) return false;
    
    const QuestInfo* quest = GetQuest(questId);
    if (!quest) return false;
    
    // 检查玩家是否有这个任务
    // TODO: 实现玩家任务状态检查
    // if (!player->HasQuest(questId)) {
    //     return false;
    // }
    
    // 检查任务完成条件
    // TODO: 检查玩家是否有需要的物品
    // for (WORD itemId : quest->requiredItems) {
    //     if (!player->HasItem(itemId)) {
    //         return false;
    //     }
    // }
    
    return true;
}

bool QuestNPC::CompleteQuest(PlayObject* player, WORD questId) {
    if (!CanCompleteQuest(player, questId)) {
        return false;
    }
    
    const QuestInfo* quest = GetQuest(questId);
    if (!quest) return false;
    
    // 移除需要的物品
    // TODO: 从玩家背包中移除任务物品
    // for (WORD itemId : quest->requiredItems) {
    //     player->RemoveItem(itemId);
    // }
    
    // 给予奖励
    GiveQuestRewards(player, *quest);
    
    // 从玩家任务列表中移除
    // TODO: 从玩家任务列表中移除
    // player->RemoveQuest(questId);
    // player->AddCompletedQuest(questId);
    
    Logger::Info("Player " + player->GetCharName() + " completed quest: " + quest->questName);
    return true;
}

void QuestNPC::OnPlayerTalk(PlayObject* player) {
    if (!player) return;
    
    // 获取可用任务
    std::vector<WORD> availableQuests = GetAvailableQuests(player);
    
    if (availableQuests.empty()) {
        // 没有可用任务，显示默认对话
        NPC::OnPlayerTalk(player);
    } else {
        // 显示任务对话
        // TODO: 发送任务对话界面
        // player->SendQuestDialog(this, availableQuests);
    }
}

bool QuestNPC::CheckQuestRequirements(PlayObject* player, const QuestInfo& quest) const {
    // 基础检查已在CanAcceptQuest中完成
    // 这里可以添加额外的自定义检查
    return true;
}

void QuestNPC::GiveQuestRewards(PlayObject* player, const QuestInfo& quest) {
    if (!player) return;
    
    // 给予经验奖励
    if (quest.rewardExp > 0) {
        // TODO: 给予经验
        // player->GainExp(quest.rewardExp);
    }
    
    // 给予金币奖励
    if (quest.rewardGold > 0) {
        // TODO: 给予金币
        // player->AddGold(quest.rewardGold);
    }
    
    // 给予物品奖励
    for (WORD itemId : quest.rewardItems) {
        // TODO: 给予物品
        // player->AddItem(itemId);
    }
}

} // namespace MirServer

#include "RepairManager.h"
#include "../Common/Logger.h"
#include "../Common/Utils.h"
#include "../Protocol/PacketTypes.h"
#include <algorithm>

namespace MirServer {

using namespace Protocol;

RepairManager::RepairManager() : m_initialized(false) {
    memset(&m_statistics, 0, sizeof(m_statistics));
}

RepairManager::~RepairManager() {
    Finalize();
}

bool RepairManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    Logger::Info("Initializing RepairManager...");

    // 重置统计信息
    ResetStatistics();

    m_initialized = true;
    Logger::Info("RepairManager initialized successfully");

    return true;
}

void RepairManager::Finalize() {
    if (!m_initialized) {
        return;
    }

    Logger::Info("Finalizing RepairManager...");

    m_initialized = false;
    Logger::Info("RepairManager finalized");
}

RepairResult RepairManager::RepairItem(PlayObject* player, WORD itemIndex) {
    if (!player || !m_initialized) {
        return RepairResult::REPAIR_FAILED;
    }

    // 验证修理请求
    if (!ValidateRepairRequest(player, itemIndex)) {
        return RepairResult::ITEM_NOT_FOUND;
    }

    // 获取物品 - 从背包中查找
    const auto& bagItems = player->GetBagItems();
    UserItem* item = nullptr;
    for (auto& bagItem : const_cast<std::vector<UserItem>&>(bagItems)) {
        if (bagItem.makeIndex == itemIndex) {
            item = &bagItem;
            break;
        }
    }

    if (!item) {
        return RepairResult::ITEM_NOT_FOUND;
    }

    // 检查物品是否可以修理
    if (!CanRepairItem(*item)) {
        return RepairResult::ITEM_CANNOT_REPAIR;
    }

    // 检查物品是否损坏
    if (!IsItemDamaged(*item)) {
        return RepairResult::ITEM_NOT_DAMAGED;
    }

    // 计算修理费用
    DWORD cost = CalculateRepairCost(*item);

    // 检查玩家金币是否足够
    if (player->GetGold() < cost) {
        return RepairResult::INSUFFICIENT_GOLD;
    }

    // 执行修理
    if (ExecuteRepair(player, *item, cost)) {
        UpdateStatistics(true, cost);

        // 发送修理成功消息
        player->SendDefMessage(SM_USERREPAIRITEM_OK, itemIndex, 0, 0, 0);
        player->SendMessage("物品修理成功，花费 " + std::to_string(cost) + " 金币", 0);

        Logger::Debug("Player " + player->GetCharName() + " repaired item " + std::to_string(itemIndex) +
                     " for " + std::to_string(cost) + " gold");

        return RepairResult::SUCCESS;
    } else {
        UpdateStatistics(false, 0);

        // 发送修理失败消息
        player->SendDefMessage(SM_USERREPAIRITEM_FAIL, itemIndex, 0, 0, 0);
        player->SendMessage("物品修理失败", 0);

        return RepairResult::REPAIR_FAILED;
    }
}

RepairResult RepairManager::RepairAllItems(PlayObject* player) {
    if (!player || !m_initialized) {
        return RepairResult::REPAIR_FAILED;
    }

    // 计算修理所有物品的费用
    DWORD totalCost = GetRepairAllCost(player);

    if (totalCost == 0) {
        player->SendMessage("没有需要修理的物品", 0);
        return RepairResult::ITEM_NOT_DAMAGED;
    }

    // 检查玩家金币是否足够
    if (player->GetGold() < totalCost) {
        player->SendMessage("金币不足，需要 " + std::to_string(totalCost) + " 金币", 0);
        return RepairResult::INSUFFICIENT_GOLD;
    }

    // 修理所有损坏的物品
    int repairedCount = 0;
    DWORD actualCost = 0;

    // 修理装备栏物品
    for (int i = 0; i < static_cast<int>(EquipPosition::MAX_EQUIP); i++) {
        const UserItem* equipItemPtr = player->GetEquipItem(static_cast<EquipPosition>(i));
        if (equipItemPtr && equipItemPtr->itemIndex > 0 && CanRepairItem(*equipItemPtr) && IsItemDamaged(*equipItemPtr)) {
            UserItem equipItem = *equipItemPtr; // 复制一份用于修改
            DWORD itemCost = CalculateRepairCost(equipItem);
            if (ExecuteRepair(player, equipItem, itemCost)) {
                // TODO: 需要更新装备栏中的物品
                actualCost += itemCost;
                repairedCount++;
            }
        }
    }

    // 修理背包物品
    auto& bagItems = const_cast<std::vector<UserItem>&>(player->GetBagItems());
    for (size_t i = 0; i < bagItems.size(); i++) {
        UserItem& bagItem = bagItems[i];
        if (bagItem.itemIndex > 0 && CanRepairItem(bagItem) && IsItemDamaged(bagItem)) {
            DWORD itemCost = CalculateRepairCost(bagItem);
            if (ExecuteRepair(player, bagItem, itemCost)) {
                // 物品已经直接修改，不需要额外更新
                actualCost += itemCost;
                repairedCount++;
            }
        }
    }

    if (repairedCount > 0) {
        UpdateStatistics(true, actualCost);

        player->SendDefMessage(SM_USERREPAIRITEM_OK, 0, 0, 0, 0);
        player->SendMessage("修理了 " + std::to_string(repairedCount) + " 件物品，花费 " +
                          std::to_string(actualCost) + " 金币", 0);

        Logger::Debug("Player " + player->GetCharName() + " repaired " + std::to_string(repairedCount) +
                     " items for " + std::to_string(actualCost) + " gold");

        return RepairResult::SUCCESS;
    } else {
        player->SendMessage("修理失败", 0);
        return RepairResult::REPAIR_FAILED;
    }
}

DWORD RepairManager::GetRepairCost(PlayObject* player, WORD itemIndex) {
    if (!player || !m_initialized) {
        return 0;
    }

    // 从背包中查找物品
    const auto& bagItems = player->GetBagItems();
    UserItem* item = nullptr;
    for (auto& bagItem : const_cast<std::vector<UserItem>&>(bagItems)) {
        if (bagItem.makeIndex == itemIndex) {
            item = &bagItem;
            break;
        }
    }
    if (!item || !CanRepairItem(*item) || !IsItemDamaged(*item)) {
        return 0;
    }

    return CalculateRepairCost(*item);
}

DWORD RepairManager::GetRepairAllCost(PlayObject* player) {
    if (!player || !m_initialized) {
        return 0;
    }

    DWORD totalCost = 0;

    // 计算装备栏物品修理费用
    for (int i = 0; i < static_cast<int>(EquipPosition::MAX_EQUIP); i++) {
        const UserItem* equipItemPtr = player->GetEquipItem(static_cast<EquipPosition>(i));
        if (equipItemPtr && equipItemPtr->itemIndex > 0 && CanRepairItem(*equipItemPtr) && IsItemDamaged(*equipItemPtr)) {
            totalCost += CalculateRepairCost(*equipItemPtr);
        }
    }

    // 计算背包物品修理费用
    const auto& bagItems = player->GetBagItems();
    for (const auto& bagItem : bagItems) {
        if (bagItem.itemIndex > 0 && CanRepairItem(bagItem) && IsItemDamaged(bagItem)) {
            totalCost += CalculateRepairCost(bagItem);
        }
    }

    return totalCost;
}

bool RepairManager::CanRepairItem(const UserItem& item) {
    if (item.itemIndex == 0) {
        return false;
    }

    // TODO: 根据物品类型判断是否可以修理
    // 一般来说，装备类物品可以修理，消耗品不能修理
    // 这里需要根据具体的物品数据库来实现

    return true; // 临时返回true
}

bool RepairManager::IsItemDamaged(const UserItem& item) {
    if (item.itemIndex == 0) {
        return false;
    }

    WORD maxDura = GetMaxDurability(item);
    WORD currentDura = GetItemDurability(item);

    return currentDura < maxDura;
}

WORD RepairManager::GetItemDurability(const UserItem& item) {
    return item.dura;
}

WORD RepairManager::GetMaxDurability(const UserItem& item) {
    return item.duraMax;
}

DWORD RepairManager::CalculateRepairCost(const UserItem& item) {
    WORD maxDura = GetMaxDurability(item);
    WORD currentDura = GetItemDurability(item);

    if (currentDura >= maxDura) {
        return 0;
    }

    // TODO: 根据物品价值计算修理费用
    // 这里需要从物品数据库获取物品的基础价值
    WORD itemValue = 1000; // 临时使用固定值

    return CalculateRepairCostByDamage(maxDura, currentDura, itemValue);
}

DWORD RepairManager::CalculateRepairCostByDamage(WORD maxDura, WORD currentDura, WORD itemValue) {
    if (currentDura >= maxDura || maxDura == 0) {
        return 0;
    }

    // 计算损坏比例
    double damageRatio = static_cast<double>(maxDura - currentDura) / maxDura;

    // 计算修理费用
    DWORD cost = static_cast<DWORD>(itemValue * damageRatio * REPAIR_COST_MULTIPLIER);

    // 限制费用范围
    cost = std::max(static_cast<DWORD>(MIN_REPAIR_COST), cost);
    cost = std::min(static_cast<DWORD>(MAX_REPAIR_COST), cost);

    return cost;
}

RepairManager::RepairStatistics RepairManager::GetStatistics() const {
    std::shared_lock<std::shared_mutex> lock(m_statsMutex);
    return m_statistics;
}

void RepairManager::ResetStatistics() {
    std::unique_lock<std::shared_mutex> lock(m_statsMutex);
    memset(&m_statistics, 0, sizeof(m_statistics));
}

bool RepairManager::ValidateRepairRequest(PlayObject* player, WORD itemIndex) {
    if (!player) {
        return false;
    }

    // 从背包中查找物品
    const auto& bagItems = player->GetBagItems();
    UserItem* item = nullptr;
    for (auto& bagItem : const_cast<std::vector<UserItem>&>(bagItems)) {
        if (bagItem.makeIndex == itemIndex) {
            item = &bagItem;
            break;
        }
    }
    return item != nullptr;
}

bool RepairManager::ExecuteRepair(PlayObject* player, UserItem& item, DWORD cost) {
    if (!player) {
        return false;
    }

    // 扣除金币
    if (!player->DecGold(cost)) {
        return false;
    }

    // 修复物品耐久度
    item.dura = item.duraMax;

    // 发送金币变化通知
    player->SendGoldChanged();

    return true;
}

void RepairManager::UpdateStatistics(bool success, DWORD cost) {
    std::unique_lock<std::shared_mutex> lock(m_statsMutex);

    m_statistics.totalRepairs++;
    m_statistics.totalCost += cost;

    if (success) {
        m_statistics.successfulRepairs++;
    } else {
        m_statistics.failedRepairs++;
    }
}

} // namespace MirServer

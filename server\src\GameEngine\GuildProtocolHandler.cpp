#include "GuildProtocolHandler.h"
#include "GuildManager.h"
#include "../BaseObject/PlayObject.h"
#include "../Common/Logger.h"
#include "../Common/Utils.h"
#include <sstream>

namespace MirServer {

bool GuildProtocolHandler::HandleGuildProtocol(PlayObject* player, Protocol::PacketType packetType,
                                              const std::string& data) {
    if (!player) {
        return false;
    }

    switch (packetType) {
        case Protocol::CM_OPENGUILDDLG:
            HandleOpenGuildDlg(player);
            return true;

        case Protocol::CM_GUILDHOME:
            HandleGuildHome(player);
            return true;

        case Protocol::CM_GUILDMEMBERLIST:
            HandleGuildMemberList(player);
            return true;

        case Protocol::CM_GUILDADDMEMBER: {
            std::string targetName = Trim(data);
            HandleGuildAddMember(player, targetName);
            return true;
        }

        case Protocol::CM_GUILDDELMEMBER: {
            std::string targetName = Trim(data);
            HandleGuildDelMember(player, targetName);
            return true;
        }

        case Protocol::CM_GUILDUPDATENOTICE:
            HandleGuildUpdateNotice(player, data);
            return true;

        case Protocol::CM_GUILDUPDATERANKINFO: {
            // 解析目标名称和职位名称
            size_t pos = data.find('|');
            if (pos != std::string::npos) {
                std::string targetName = Trim(data.substr(0, pos));
                std::string rankName = Trim(data.substr(pos + 1));
                HandleGuildUpdateRankInfo(player, targetName, rankName);
            }
            return true;
        }

        case Protocol::CM_GUILDMAKEALLY: {
            std::string guildName = Trim(data);
            HandleGuildMakeAlly(player, guildName);
            return true;
        }

        case Protocol::CM_GUILDBREAKALLY: {
            std::string guildName = Trim(data);
            HandleGuildBreakAlly(player, guildName);
            return true;
        }

        case Protocol::CM_GUILDWAR: {
            std::string guildName = Trim(data);
            HandleGuildWar(player, guildName);
            return true;
        }

        case Protocol::CM_GUILDPEACE: {
            std::string guildName = Trim(data);
            HandleGuildPeace(player, guildName);
            return true;
        }

        case Protocol::CM_GUILDMSG:
            HandleGuildMsg(player, data);
            return true;

        default:
            return false;
    }
}

void GuildProtocolHandler::HandleOpenGuildDlg(PlayObject* player) {
    Guild* guild = GetPlayerGuild(player);

    if (!guild) {
        SendGuildResponse(player, Protocol::SM_OPENGUILDDLG_FAIL, false, "您不在任何行会中");
        return;
    }

    // 发送行会对话框打开成功响应
    SendGuildResponse(player, Protocol::SM_OPENGUILDDLG, true);

    // 发送行会基本信息
    SendGuildInfo(player);
}

void GuildProtocolHandler::HandleGuildHome(PlayObject* player) {
    Guild* guild = GetPlayerGuild(player);

    if (!guild) {
        player->SendMessage("您不在任何行会中", 0);
        return;
    }

    // 发送行会主页信息
    SendGuildInfo(player);
    SendGuildNotices(player);
}

void GuildProtocolHandler::HandleGuildMemberList(PlayObject* player) {
    Guild* guild = GetPlayerGuild(player);

    if (!guild) {
        player->SendMessage("您不在任何行会中", 0);
        return;
    }

    // 发送行会成员列表
    SendGuildMemberList(player);
}

void GuildProtocolHandler::HandleGuildAddMember(PlayObject* player, const std::string& targetName) {
    Guild* guild = GetPlayerGuild(player);

    if (!guild) {
        SendGuildResponse(player, Protocol::SM_GUILDADDMEMBER_FAIL, false, "您不在任何行会中");
        return;
    }

    // 检查权限
    if (!CanManageMembers(player)) {
        SendGuildResponse(player, Protocol::SM_GUILDADDMEMBER_FAIL, false, "您没有权限添加成员");
        return;
    }

    // 查找目标玩家
    // TODO: 需要实现查找在线玩家的功能
    // PlayObject* targetPlayer = GameEngine::FindPlayer(targetName);
    PlayObject* targetPlayer = nullptr; // 临时实现

    if (!targetPlayer) {
        SendGuildResponse(player, Protocol::SM_GUILDADDMEMBER_FAIL, false, "目标玩家不在线");
        return;
    }

    // 检查目标玩家是否已有行会
    if (!targetPlayer->GetGuildName().empty()) {
        SendGuildResponse(player, Protocol::SM_GUILDADDMEMBER_FAIL, false, "目标玩家已有行会");
        return;
    }

    // 添加成员
    if (guild->AddMember(targetPlayer, GuildRank::MEMBER)) {
        SendGuildResponse(player, Protocol::SM_GUILDADDMEMBER_OK, true,
                         targetName + " 已加入行会");

        // 通知目标玩家
        targetPlayer->SendMessage("您已加入行会: " + guild->GetGuildName(), 0);

        // 更新成员列表
        SendGuildMemberList(player);
    } else {
        SendGuildResponse(player, Protocol::SM_GUILDADDMEMBER_FAIL, false, "添加成员失败");
    }
}

void GuildProtocolHandler::HandleGuildDelMember(PlayObject* player, const std::string& targetName) {
    Guild* guild = GetPlayerGuild(player);

    if (!guild) {
        SendGuildResponse(player, Protocol::SM_GUILDDELMEMBER_FAIL, false, "您不在任何行会中");
        return;
    }

    // 检查权限
    if (!CanManageMembers(player)) {
        SendGuildResponse(player, Protocol::SM_GUILDDELMEMBER_FAIL, false, "您没有权限删除成员");
        return;
    }

    // 不能删除自己
    if (targetName == player->GetCharName()) {
        SendGuildResponse(player, Protocol::SM_GUILDDELMEMBER_FAIL, false, "不能删除自己");
        return;
    }

    // 检查目标是否是会长
    if (guild->GetMemberRank(targetName) == GuildRank::CHIEF) {
        SendGuildResponse(player, Protocol::SM_GUILDDELMEMBER_FAIL, false, "不能删除会长");
        return;
    }

    // 删除成员
    if (guild->RemoveMember(targetName)) {
        SendGuildResponse(player, Protocol::SM_GUILDDELMEMBER_OK, true,
                         targetName + " 已被移出行会");

        // 更新成员列表
        SendGuildMemberList(player);
    } else {
        SendGuildResponse(player, Protocol::SM_GUILDDELMEMBER_FAIL, false, "删除成员失败");
    }
}

void GuildProtocolHandler::HandleGuildUpdateNotice(PlayObject* player, const std::string& notice) {
    Guild* guild = GetPlayerGuild(player);

    if (!guild) {
        player->SendMessage("您不在任何行会中", 0);
        return;
    }

    // 检查权限
    if (!CanUpdateNotice(player)) {
        player->SendMessage("您没有权限更新公告", 0);
        return;
    }

    // 更新公告
    guild->AddNotice(notice);

    // 通知所有成员
    guild->SendGuildMessage("行会公告已更新: " + notice);

    player->SendMessage("公告更新成功", 0);
}

void GuildProtocolHandler::HandleGuildUpdateRankInfo(PlayObject* player, const std::string& targetName,
                                                    const std::string& rankName) {
    Guild* guild = GetPlayerGuild(player);

    if (!guild) {
        SendGuildResponse(player, Protocol::SM_GUILDRANKUPDATE_FAIL, false, "您不在任何行会中");
        return;
    }

    // 检查权限（只有会长和副会长可以更新职位）
    if (!IsGuildChief(player) && !IsGuildViceChief(player)) {
        SendGuildResponse(player, Protocol::SM_GUILDRANKUPDATE_FAIL, false, "您没有权限更新职位");
        return;
    }

    // 确定新职位
    GuildRank newRank = GuildRank::MEMBER;
    if (rankName == "副会长") {
        newRank = GuildRank::VICE_CHIEF;
    } else if (rankName == "队长") {
        newRank = GuildRank::CAPTAIN;
    }

    // 更新职位
    if (guild->UpdateMemberRank(targetName, newRank, rankName)) {
        guild->SendGuildMessage(targetName + " 的职位已更新为: " + rankName);
        SendGuildMemberList(player);
    } else {
        SendGuildResponse(player, Protocol::SM_GUILDRANKUPDATE_FAIL, false, "更新职位失败");
    }
}

void GuildProtocolHandler::HandleGuildMakeAlly(PlayObject* player, const std::string& guildName) {
    Guild* guild = GetPlayerGuild(player);

    if (!guild) {
        SendGuildResponse(player, Protocol::SM_GUILDMAKEALLY_FAIL, false, "您不在任何行会中");
        return;
    }

    // 检查权限
    if (!CanManageAlliance(player)) {
        SendGuildResponse(player, Protocol::SM_GUILDMAKEALLY_FAIL, false, "您没有权限管理联盟");
        return;
    }

    // 查找目标行会
    auto& guildManager = GuildManager::GetInstance();
    Guild* targetGuild = guildManager.FindGuild(guildName);

    if (!targetGuild) {
        SendGuildResponse(player, Protocol::SM_GUILDMAKEALLY_FAIL, false, "目标行会不存在");
        return;
    }

    // 不能与自己结盟
    if (targetGuild == guild) {
        SendGuildResponse(player, Protocol::SM_GUILDMAKEALLY_FAIL, false, "不能与自己的行会结盟");
        return;
    }

    // 结盟
    if (guild->AddAlly(targetGuild)) {
        SendGuildResponse(player, Protocol::SM_GUILDMAKEALLY_OK, true,
                         "与 " + guildName + " 结盟成功");

        // 通知双方行会
        guild->SendGuildMessage("与 " + guildName + " 结成联盟");
        targetGuild->SendGuildMessage("与 " + guild->GetGuildName() + " 结成联盟");
    } else {
        SendGuildResponse(player, Protocol::SM_GUILDMAKEALLY_FAIL, false, "结盟失败");
    }
}

void GuildProtocolHandler::HandleGuildBreakAlly(PlayObject* player, const std::string& guildName) {
    Guild* guild = GetPlayerGuild(player);

    if (!guild) {
        SendGuildResponse(player, Protocol::SM_GUILDBREAKALLY_FAIL, false, "您不在任何行会中");
        return;
    }

    // 检查权限
    if (!CanManageAlliance(player)) {
        SendGuildResponse(player, Protocol::SM_GUILDBREAKALLY_FAIL, false, "您没有权限管理联盟");
        return;
    }

    // 查找目标行会
    auto& guildManager = GuildManager::GetInstance();
    Guild* targetGuild = guildManager.FindGuild(guildName);

    if (!targetGuild) {
        SendGuildResponse(player, Protocol::SM_GUILDBREAKALLY_FAIL, false, "目标行会不存在");
        return;
    }

    // 解除联盟
    if (guild->RemoveAlly(targetGuild)) {
        SendGuildResponse(player, Protocol::SM_GUILDBREAKALLY_OK, true,
                         "与 " + guildName + " 解除联盟成功");

        // 通知双方行会
        guild->SendGuildMessage("与 " + guildName + " 解除联盟");
        targetGuild->SendGuildMessage("与 " + guild->GetGuildName() + " 解除联盟");
    } else {
        SendGuildResponse(player, Protocol::SM_GUILDBREAKALLY_FAIL, false, "解除联盟失败");
    }
}

void GuildProtocolHandler::HandleGuildWar(PlayObject* player, const std::string& guildName) {
    Guild* guild = GetPlayerGuild(player);

    if (!guild) {
        SendGuildResponse(player, Protocol::SM_GUILDWAR_FAIL, false, "您不在任何行会中");
        return;
    }

    // 检查权限
    if (!CanDeclareWar(player)) {
        SendGuildResponse(player, Protocol::SM_GUILDWAR_FAIL, false, "您没有权限宣战");
        return;
    }

    // 查找目标行会
    auto& guildManager = GuildManager::GetInstance();
    Guild* targetGuild = guildManager.FindGuild(guildName);

    if (!targetGuild) {
        SendGuildResponse(player, Protocol::SM_GUILDWAR_FAIL, false, "目标行会不存在");
        return;
    }

    // 不能与自己宣战
    if (targetGuild == guild) {
        SendGuildResponse(player, Protocol::SM_GUILDWAR_FAIL, false, "不能与自己的行会宣战");
        return;
    }

    // 宣战
    if (guild->StartWar(targetGuild)) {
        SendGuildResponse(player, Protocol::SM_GUILDWAR_OK, true,
                         "向 " + guildName + " 宣战成功");

        // 通知双方行会
        guild->SendGuildMessage("向 " + guildName + " 宣战！");
        targetGuild->SendGuildMessage(guild->GetGuildName() + " 向我们宣战！");
    } else {
        SendGuildResponse(player, Protocol::SM_GUILDWAR_FAIL, false, "宣战失败");
    }
}

void GuildProtocolHandler::HandleGuildPeace(PlayObject* player, const std::string& guildName) {
    Guild* guild = GetPlayerGuild(player);

    if (!guild) {
        SendGuildResponse(player, Protocol::SM_GUILDPEACE_FAIL, false, "您不在任何行会中");
        return;
    }

    // 检查权限
    if (!CanDeclareWar(player)) {
        SendGuildResponse(player, Protocol::SM_GUILDPEACE_FAIL, false, "您没有权限停战");
        return;
    }

    // 查找目标行会
    auto& guildManager = GuildManager::GetInstance();
    Guild* targetGuild = guildManager.FindGuild(guildName);

    if (!targetGuild) {
        SendGuildResponse(player, Protocol::SM_GUILDPEACE_FAIL, false, "目标行会不存在");
        return;
    }

    // 停战
    if (guild->EndWar(targetGuild)) {
        SendGuildResponse(player, Protocol::SM_GUILDPEACE_OK, true,
                         "与 " + guildName + " 停战成功");

        // 通知双方行会
        guild->SendGuildMessage("与 " + guildName + " 停战");
        targetGuild->SendGuildMessage("与 " + guild->GetGuildName() + " 停战");
    } else {
        SendGuildResponse(player, Protocol::SM_GUILDPEACE_FAIL, false, "停战失败");
    }
}

void GuildProtocolHandler::HandleGuildMsg(PlayObject* player, const std::string& message) {
    Guild* guild = GetPlayerGuild(player);

    if (!guild) {
        player->SendMessage("您不在任何行会中", 0);
        return;
    }

    // 发送行会消息
    guild->SendGuildMessage(player->GetCharName() + ": " + message);
}

// ============================================================================
// 发送行会信息到客户端
// ============================================================================

void GuildProtocolHandler::SendGuildInfo(PlayObject* player) {
    Guild* guild = GetPlayerGuild(player);
    if (!guild) {
        return;
    }

    // 构建行会信息字符串
    std::ostringstream oss;
    oss << "行会名称: " << guild->GetGuildName() << "\n";
    oss << "会长: " << guild->GetChiefName() << "\n";
    oss << "成员数量: " << guild->GetMemberCount() << "\n";
    oss << "建设度: " << guild->GetBuildPoint() << "\n";
    oss << "灵气值: " << guild->GetAurae() << "\n";
    oss << "安定度: " << guild->GetStability() << "\n";
    oss << "繁荣度: " << guild->GetFlourishing() << "\n";

    player->SendMessage(oss.str(), 0);
}

void GuildProtocolHandler::SendGuildMemberList(PlayObject* player) {
    Guild* guild = GetPlayerGuild(player);
    if (!guild) {
        return;
    }

    std::string memberInfo = FormatGuildMemberInfo(guild);

    // 发送成员列表（使用SendMessage代替SendDefMessage）
    player->SendMessage(memberInfo, 0);
}

void GuildProtocolHandler::SendGuildNotices(PlayObject* player) {
    Guild* guild = GetPlayerGuild(player);
    if (!guild) {
        return;
    }

    const auto& notices = guild->GetNotices();
    if (notices.empty()) {
        player->SendMessage("暂无行会公告", 0);
        return;
    }

    std::ostringstream oss;
    oss << "=== 行会公告 ===\n";
    for (size_t i = 0; i < notices.size() && i < 5; ++i) {
        oss << (i + 1) << ". " << notices[i] << "\n";
    }

    player->SendMessage(oss.str(), 0);
}

void GuildProtocolHandler::SendGuildMessage(PlayObject* player, const std::string& message) {
    player->SendMessage("[行会] " + message, 0);
}

// ============================================================================
// 权限检查
// ============================================================================

bool GuildProtocolHandler::CanManageMembers(PlayObject* player) {
    return IsGuildChief(player) || IsGuildViceChief(player);
}

bool GuildProtocolHandler::CanUpdateNotice(PlayObject* player) {
    return IsGuildChief(player) || IsGuildViceChief(player);
}

bool GuildProtocolHandler::CanManageAlliance(PlayObject* player) {
    return IsGuildChief(player);
}

bool GuildProtocolHandler::CanDeclareWar(PlayObject* player) {
    return IsGuildChief(player);
}

// ============================================================================
// 内部辅助方法
// ============================================================================

Guild* GuildProtocolHandler::GetPlayerGuild(PlayObject* player) {
    if (!player || player->GetGuildName().empty()) {
        return nullptr;
    }

    auto& guildManager = GuildManager::GetInstance();
    return guildManager.FindGuild(player->GetGuildName());
}

bool GuildProtocolHandler::IsGuildChief(PlayObject* player) {
    return player && player->GetGuildRank() == static_cast<BYTE>(GuildRank::CHIEF);
}

bool GuildProtocolHandler::IsGuildViceChief(PlayObject* player) {
    return player && player->GetGuildRank() == static_cast<BYTE>(GuildRank::VICE_CHIEF);
}

bool GuildProtocolHandler::HasGuildRank(PlayObject* player, BYTE minRank) {
    return player && player->GetGuildRank() <= minRank;
}

void GuildProtocolHandler::SendGuildResponse(PlayObject* player, Protocol::PacketType responseType,
                                           bool success, const std::string& message) {
    if (!player) {
        return;
    }

    WORD param = success ? 1 : 0;
    player->SendDefMessage(static_cast<WORD>(responseType), 0, param, 0, 0);

    if (!message.empty()) {
        player->SendMessage(message, 0);
    }
}

std::string GuildProtocolHandler::FormatGuildMemberInfo(const Guild* guild) {
    if (!guild) {
        return "";
    }

    std::ostringstream oss;
    const auto& members = guild->GetMembers();

    oss << "=== 行会成员列表 ===\n";
    oss << "总成员数: " << members.size() << "\n\n";

    for (const auto& member : members) {
        oss << member.playerName << " - " << member.rankName;
        if (member.isOnline) {
            oss << " [在线]";
        } else {
            oss << " [离线]";
        }
        oss << "\n";
    }

    return oss.str();
}

std::string GuildProtocolHandler::FormatGuildNotices(const Guild* guild) {
    if (!guild) {
        return "";
    }

    const auto& notices = guild->GetNotices();
    if (notices.empty()) {
        return "暂无公告";
    }

    std::ostringstream oss;
    oss << "=== 行会公告 ===\n";
    for (size_t i = 0; i < notices.size() && i < 10; ++i) {
        oss << (i + 1) << ". " << notices[i] << "\n";
    }

    return oss.str();
}

} // namespace MirServer

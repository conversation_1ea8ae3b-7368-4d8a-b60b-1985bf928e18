#include "LocalDB.h"
#include "../Common/Logger.h"
#include "../Common/Config.h"
#include "../Database/SQLiteDatabase.h"
#include <iostream>
#include <filesystem>

using namespace MirServer;

// 创建测试数据库并初始化测试数据
bool CreateTestDatabase(const std::string& dbPath) {
    auto db = std::make_shared<MirServer::SQLiteDatabase>();
    if (!db->Connect(dbPath)) {
        Logger::Error("LocalDBTest", "Failed to connect to test database");
        return false;
    }
    
    // 创建StdItems表
    std::string createItemsSQL = R"(
        CREATE TABLE IF NOT EXISTS StdItems (
            Idx INTEGER PRIMARY KEY,
            Name TEXT NOT NULL,
            StdMode INTEGER NOT NULL,
            Shape INTEGER NOT NULL,
            Weight INTEGER NOT NULL,
            AniCount INTEGER NOT NULL,
            Source INTEGER NOT NULL,
            Reserved INTEGER NOT NULL,
            Looks INTEGER NOT NULL,
            <PERSON>raMax INTEGER NOT NULL,
            AC INTEGER NOT NULL,
            AC2 INTEGER NOT NULL,
            MAC INTEGER NOT NULL,
            MAC2 INTEGER NOT NULL,
            DC INTEGER NOT NULL,
            DC2 INTEGER NOT NULL,
            MC INTEGER NOT NULL,
            MC2 INTEGER NOT NULL,
            SC INTEGER NOT NULL,
            SC2 INTEGER NOT NULL,
            Need INTEGER NOT NULL,
            NeedLevel INTEGER NOT NULL,
            Price INTEGER NOT NULL
        )
    )";
    
    if (!db->Execute(createItemsSQL)) {
        Logger::Error("LocalDBTest", "Failed to create StdItems table");
        return false;
    }
    
    // 插入测试数据
    std::vector<std::string> insertSQL = {
        "INSERT OR REPLACE INTO StdItems VALUES (0, '木剑', 5, 1, 1, 0, 0, 0, 1, 5000, 0, 1, 0, 0, 1, 4, 0, 0, 0, 0, 0, 1, 10)",
        "INSERT OR REPLACE INTO StdItems VALUES (1, '布衣(男)', 10, 1, 1, 0, 0, 0, 1, 5000, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 50)",
        "INSERT OR REPLACE INTO StdItems VALUES (2, '铁剑', 5, 2, 2, 0, 0, 0, 2, 8000, 0, 2, 0, 0, 2, 6, 0, 0, 0, 0, 0, 5, 100)"
    };
    
    for (const auto& sql : insertSQL) {
        if (!db->Execute(sql)) {
            Logger::Error("LocalDBTest", "Failed to insert test data: {}", sql);
            return false;
        }
    }
    
    // 创建Magic表
    std::string createMagicSQL = R"(
        CREATE TABLE IF NOT EXISTS Magic (
            MagId INTEGER PRIMARY KEY,
            MagName TEXT NOT NULL,
            EffectType INTEGER NOT NULL,
            Effect INTEGER NOT NULL,
            Spell INTEGER NOT NULL,
            Power INTEGER NOT NULL,
            MaxPower INTEGER NOT NULL,
            Job INTEGER NOT NULL,
            NeedL1 INTEGER NOT NULL,
            NeedL2 INTEGER NOT NULL,
            NeedL3 INTEGER NOT NULL,
            L1Train INTEGER NOT NULL,
            L2Train INTEGER NOT NULL,
            L3Train INTEGER NOT NULL,
            Delay INTEGER NOT NULL,
            DefSpell INTEGER NOT NULL,
            DefPower INTEGER NOT NULL,
            DefMaxPower INTEGER NOT NULL,
            Descr TEXT
        )
    )";
    
    if (!db->Execute(createMagicSQL)) {
        Logger::Error("LocalDBTest", "Failed to create Magic table");
        return false;
    }
    
    // 插入魔法测试数据
    std::vector<std::string> insertMagicSQL = {
        "INSERT OR REPLACE INTO Magic VALUES (1, '火球术', 1, 1, 1, 6, 12, 0, 7, 11, 16, 50, 100, 200, 800, 1, 6, 12, '基础攻击魔法')",
        "INSERT OR REPLACE INTO Magic VALUES (2, '治愈术', 2, 2, 2, 4, 8, 1, 7, 11, 16, 50, 100, 200, 1000, 2, 4, 8, '基础治疗魔法')"
    };
    
    for (const auto& sql : insertMagicSQL) {
        if (!db->Execute(sql)) {
            Logger::Error("LocalDBTest", "Failed to insert magic test data: {}", sql);
            return false;
        }
    }
    
    // 创建Monster表
    std::string createMonsterSQL = R"(
        CREATE TABLE IF NOT EXISTS Monster (
            NAME TEXT PRIMARY KEY,
            Race INTEGER NOT NULL,
            RaceImg INTEGER NOT NULL,
            Appr INTEGER NOT NULL,
            Lvl INTEGER NOT NULL,
            Undead INTEGER NOT NULL,
            CoolEye INTEGER NOT NULL,
            Exp INTEGER NOT NULL,
            HP INTEGER NOT NULL,
            MP INTEGER NOT NULL,
            AC INTEGER NOT NULL,
            MAC INTEGER NOT NULL,
            DC INTEGER NOT NULL,
            DCMAX INTEGER NOT NULL,
            MC INTEGER NOT NULL,
            SC INTEGER NOT NULL,
            SPEED INTEGER NOT NULL,
            HIT INTEGER NOT NULL,
            WALK_SPD INTEGER NOT NULL,
            WalkStep INTEGER NOT NULL,
            WalkWait INTEGER NOT NULL,
            ATTACK_SPD INTEGER NOT NULL
        )
    )";
    
    if (!db->Execute(createMonsterSQL)) {
        Logger::Error("LocalDBTest", "Failed to create Monster table");
        return false;
    }
    
    // 插入怪物测试数据
    std::vector<std::string> insertMonsterSQL = {
        "INSERT OR REPLACE INTO Monster VALUES ('鸡', 50, 50, 50, 1, 0, 0, 5, 15, 0, 0, 0, 1, 1, 0, 0, 1800, 5, 1800, 1, 0, 1800)",
        "INSERT OR REPLACE INTO Monster VALUES ('鹿', 51, 51, 51, 3, 0, 0, 15, 35, 0, 0, 0, 2, 3, 0, 0, 1600, 8, 1600, 1, 0, 1600)"
    };
    
    for (const auto& sql : insertMonsterSQL) {
        if (!db->Execute(sql)) {
            Logger::Error("LocalDBTest", "Failed to insert monster test data: {}", sql);
            return false;
        }
    }
    
    db->Disconnect();
    Logger::Info("LocalDBTest", "Test database created successfully");
    return true;
}

// 创建测试用的文件数据
void CreateTestFiles() {
    std::filesystem::create_directories("./GameData/Envir");
    
    // 创建AdminList.txt
    std::ofstream adminFile("./GameData/Envir/AdminList.txt");
    adminFile << "; 管理员列表文件\n";
    adminFile << "; 格式: 等级 角色名 IP地址\n";
    adminFile << "*Admin ***********\n";
    adminFile << "1GameMaster *************\n";
    adminFile << "5Helper 0.0.0.0\n";
    adminFile.close();
    
    // 创建Npcs.txt
    std::ofstream npcFile("./GameData/Envir/Npcs.txt");
    npcFile << "; NPC列表文件\n";
    npcFile << "; 格式: \"名称\" 类型 地图名 X Y 标志 外观 变色 变色时间\n";
    npcFile << "\"武器店老板\" 0 比奇省 300 300 0 0 0 0\n";
    npcFile << "\"药店老板\" 0 比奇省 350 300 0 1 0 0\n";
    npcFile << "\"仓库管理员\" 1 比奇省 400 300 0 2 1 5000\n";
    npcFile.close();
    
    // 创建MonItems目录和测试怪物掉落文件
    std::filesystem::create_directories("./GameData/Envir/MonItems");
    
    std::ofstream monItemFile("./GameData/Envir/MonItems/鸡.txt");
    monItemFile << "; 鸡的掉落物品\n";
    monItemFile << "; 格式: 概率分子/概率分母 \"物品名\" 数量\n";
    monItemFile << "1/100 \"金币\" 10\n";
    monItemFile << "1/200 \"木剑\" 1\n";
    monItemFile << "1/500 \"布衣(男)\" 1\n";
    monItemFile.close();
}

int main() {
    try {
        // 初始化日志系统
        Logger::Initialize("LocalDBTest", LogLevel::DEBUG);
        Logger::Info("LocalDBTest", "Starting LocalDB test...");
        
        // 初始化配置
        g_Config.Initialize();
        
        // 创建测试文件
        CreateTestFiles();
        
        // 创建测试数据库
        std::string testDbPath = "./test_game.db";
        if (!CreateTestDatabase(testDbPath)) {
            Logger::Error("LocalDBTest", "Failed to create test database");
            return 1;
        }
        
        // 设置数据库连接字符串
        g_Config.database.connectionString = testDbPath;
        
        // 创建LocalDB实例
        auto localDB = std::make_unique<LocalDB>();
        
        // 初始化LocalDB（不传入数据库，让它自己创建）
        if (localDB->Initialize(nullptr)) {
            Logger::Info("LocalDBTest", "LocalDB initialized successfully");
            
            // 测试统计信息
            auto stats = localDB->GetStatistics();
            Logger::Info("LocalDBTest", "Statistics: {} items, {} magics, {} monsters, {} NPCs, {} admins",
                        stats.itemCount, stats.magicCount, stats.monsterCount, stats.npcCount, stats.adminCount);
            
            // 测试物品查询
            const auto* item = localDB->GetStdItem(0);
            if (item) {
                Logger::Info("LocalDBTest", "Found item 0: {}, mode: {}, weight: {}", 
                            item->name, item->stdMode, item->weight);
            }
            
            item = localDB->GetStdItemByName("木剑");
            if (item) {
                Logger::Info("LocalDBTest", "Found item by name '木剑': idx: {}, mode: {}", 
                            item->idx, item->stdMode);
            }
            
            // 测试管理员查询
            const auto* admin = localDB->GetAdmin("Admin");
            if (admin) {
                Logger::Info("LocalDBTest", "Found admin 'Admin': level: {}, IP: {}", 
                            admin->level, admin->ipAddr);
            }
            
            // 测试NPC查询
            const auto* npc = localDB->GetNpc("武器店老板");
            if (npc) {
                Logger::Info("LocalDBTest", "Found NPC '武器店老板': type: {}, map: {}, pos: ({}, {})", 
                            npc->npcType, npc->mapName, npc->x, npc->y);
            }
            
            // 测试怪物查询（虽然我们没有真实的Monster数据）
            const auto* monster = localDB->GetMonster("鸡");
            if (monster) {
                Logger::Info("LocalDBTest", "Found monster '鸡': level: {}, HP: {}", 
                            monster->level, monster->hp);
            } else {
                Logger::Info("LocalDBTest", "Monster '鸡' not found (expected since we use mock data)");
            }
            
            // 测试数据验证
            if (localDB->ValidateData()) {
                Logger::Info("LocalDBTest", "Data validation passed");
            } else {
                auto errors = localDB->GetValidationErrors();
                Logger::Error("LocalDBTest", "Data validation failed with {} errors", errors.size());
                for (const auto& error : errors) {
                    Logger::Error("LocalDBTest", "Validation error: {}", error);
                }
            }
            
            // 测试缓存管理
            Logger::Info("LocalDBTest", "Cache valid: {}", localDB->IsCacheValid());
            
            // 测试重载功能
            localDB->ReloadItems();
            Logger::Info("LocalDBTest", "Items reloaded");
            
            localDB->ReloadNpc();
            Logger::Info("LocalDBTest", "NPCs reloaded");
            
            Logger::Info("LocalDBTest", "All tests completed successfully!");
            
        } else {
            Logger::Error("LocalDBTest", "Failed to initialize LocalDB");
            return 1;
        }
        
        // 清理
        localDB->Finalize();
        Logger::Info("LocalDBTest", "LocalDB finalized");
        
    } catch (const std::exception& e) {
        Logger::Error("LocalDBTest", "Exception in test: {}", e.what());
        return 1;
    } catch (...) {
        Logger::Error("LocalDBTest", "Unknown exception in test");
        return 1;
    }
    
    return 0;
} 
// test_script_engine.cpp - 脚本执行引擎测试
#include "../src/GameEngine/ScriptEngine.h"
#include "../src/BaseObject/PlayObject.h"
#include "../src/BaseObject/NPC.h"
#include "../src/Common/Logger.h"
#include <iostream>
#include <memory>

using namespace MirServer;

// 简单的测试用PlayObject
class TestPlayObject : public PlayObject {
public:
    TestPlayObject() : PlayObject() {
        SetCharName("TestPlayer");
        SetLevel(15);
        SetJob(JobType::WARRIOR);
        SetGold(2000);

        // 设置属性
        Ability ability = GetAbility();
        ability.dc = 10;
        ability.mc = 5;
        ability.sc = 3;
        ability.exp = 5000;
        SetAbility(ability);

        SetHP(100);
        SetMP(50);
        SetGender(GenderType::MALE);
        SetMapName("比奇城");
    }

    // 重写一些方法用于测试
    virtual void SendDefMessage(WORD msg, WORD recog, WORD param, WORD tag, WORD series) override {
        std::cout << "[协议消息] " << msg << std::endl;
    }
};

// 简单的测试用NPC
class TestNPC : public NPC {
public:
    TestNPC() : NPC() {
        m_charName = "TestNPC";
        SetScriptFile("scripts/test_npc.txt");
    }
};

int main() {
    std::cout << "=== 脚本执行引擎测试 ===" << std::endl;

    // 初始化日志系统
    // Logger::Initialize("test_script_engine.log"); // 暂时注释掉

    try {
        // 创建测试对象
        auto player = std::make_shared<TestPlayObject>();
        auto npc = std::make_shared<TestNPC>();

        // 初始化NPC（这会加载脚本）
        npc->Initialize();

        std::cout << "\n1. 测试脚本加载..." << std::endl;
        if (npc->HasScript()) {
            std::cout << "✓ 脚本加载成功" << std::endl;
            const NPCScript* script = npc->GetScript();
            std::cout << "  脚本文件: " << script->scriptFile << std::endl;
            std::cout << "  脚本块数量: " << script->blocks.size() << std::endl;
        } else {
            std::cout << "✗ 脚本加载失败" << std::endl;
            return 1;
        }

        std::cout << "\n2. 测试主脚本执行..." << std::endl;
        bool result = npc->ExecuteNPCScript(player.get(), "main");
        if (result) {
            std::cout << "✓ 主脚本执行成功" << std::endl;
        } else {
            std::cout << "✗ 主脚本执行失败" << std::endl;
        }

        std::cout << "\n3. 测试商店脚本执行..." << std::endl;
        result = npc->ExecuteNPCScript(player.get(), "shop");
        if (result) {
            std::cout << "✓ 商店脚本执行成功" << std::endl;
        } else {
            std::cout << "✗ 商店脚本执行失败" << std::endl;
        }

        std::cout << "\n4. 测试任务脚本执行..." << std::endl;
        result = npc->ExecuteNPCScript(player.get(), "quest");
        if (result) {
            std::cout << "✓ 任务脚本执行成功" << std::endl;
        } else {
            std::cout << "✗ 任务脚本执行失败" << std::endl;
        }

        std::cout << "\n5. 测试仓库脚本执行..." << std::endl;
        result = npc->ExecuteNPCScript(player.get(), "storage");
        if (result) {
            std::cout << "✓ 仓库脚本执行成功" << std::endl;
        } else {
            std::cout << "✗ 仓库脚本执行失败" << std::endl;
        }

        std::cout << "\n6. 测试不存在的脚本标签..." << std::endl;
        result = npc->ExecuteNPCScript(player.get(), "nonexistent");
        if (!result) {
            std::cout << "✓ 正确处理了不存在的脚本标签" << std::endl;
        } else {
            std::cout << "✗ 未正确处理不存在的脚本标签" << std::endl;
        }

        // 清理
        npc->Finalize();

        std::cout << "\n=== 测试完成 ===" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }

    // Logger::Finalize(); // 暂时注释掉
    return 0;
}

#pragma once

#include "NetworkManager.h"
#include "../BaseObject/PlayObject.h"
#include "../GameEngine/GameEngine.h"

namespace MirServer {
namespace Protocol {

// 游戏协议处理器
class GameProtocolHandler : public IPacketHandler {
public:
    GameProtocolHandler();
    virtual ~GameProtocolHandler();

    // IPacketHandler接口实现
    virtual void HandlePacket(std::shared_ptr<Network::ClientConnection> client,
                             const PacketHeader& header,
                             const uint8_t* data,
                             size_t dataSize) override;

    virtual void OnClientConnected(std::shared_ptr<Network::ClientConnection> client) override;
    virtual void OnClientDisconnected(std::shared_ptr<Network::ClientConnection> client) override;

private:
    // 协议处理方法
    void HandleStorageItem(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleTakebackStorageItem(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleRepairItem(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleQueryRepairCost(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleDealTry(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleDealAddItem(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleDealDeleteItem(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleDealCancel(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleDealChangeGold(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleDealEnd(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleWantMinimap(PlayObject* player, const uint8_t* data, size_t dataSize);

    // 组队协议处理
    void HandleGroupMode(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleCreateGroup(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleAddGroupMember(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleDelGroupMember(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleGroupChat(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleGroupTeleport(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleGroupExpMode(PlayObject* player, const uint8_t* data, size_t dataSize);

    // 基础协议处理
    void HandleWalk(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleRun(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleTurn(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleSay(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandlePickup(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleDropItem(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleUseBagItem(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleTakeOnItem(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleTakeOffItem(PlayObject* player, const uint8_t* data, size_t dataSize);
    void HandleClickNPC(PlayObject* player, const uint8_t* data, size_t dataSize);

    // 工具方法
    PlayObject* GetPlayerByConnection(std::shared_ptr<Network::ClientConnection> client);
    void SendErrorMessage(std::shared_ptr<Network::ClientConnection> client, const std::string& message);

    // 数据解析方法
    struct StorageItemRequest {
        WORD itemIndex;
        std::string password;
    };

    struct TakebackItemRequest {
        WORD itemIndex;
    };

    struct RepairItemRequest {
        WORD itemIndex;
    };

    struct DealRequest {
        DWORD targetId;
    };

    struct DealItemRequest {
        WORD itemIndex;
        WORD count;
    };

    struct DealGoldRequest {
        DWORD amount;
    };

    struct MoveRequest {
        DirectionType direction;
        Point position;
    };

    struct SayRequest {
        std::string message;
        BYTE color;
    };

    struct PickupRequest {
        DWORD itemId;
        Point position;
    };

    struct DropItemRequest {
        WORD itemIndex;
        Point position;
    };

    struct UseItemRequest {
        WORD itemIndex;
    };

    struct TakeOnItemRequest {
        WORD itemIndex;
        EquipPosition position;
    };

    struct TakeOffItemRequest {
        EquipPosition position;
    };

    struct ClickNPCRequest {
        DWORD npcId;
    };

    // 组队相关数据结构
    struct GroupModeRequest {
        BYTE mode;  // 0=拒绝组队, 1=组队模式, 2=允许组队
    };

    struct AddGroupMemberRequest {
        std::string playerName;
    };

    struct DelGroupMemberRequest {
        std::string playerName;
    };

    struct GroupChatRequest {
        std::string message;
    };

    struct GroupTeleportRequest {
        BYTE teleportType;  // 0=召集队员, 1=传送到指定位置
        std::string mapName;
        WORD x;
        WORD y;
    };

    struct GroupExpModeRequest {
        BYTE expMode;  // 0=平均分配, 1=按等级分配
    };

    // 解析方法
    bool ParseStorageItemRequest(const uint8_t* data, size_t dataSize, StorageItemRequest& request);
    bool ParseTakebackItemRequest(const uint8_t* data, size_t dataSize, TakebackItemRequest& request);
    bool ParseRepairItemRequest(const uint8_t* data, size_t dataSize, RepairItemRequest& request);
    bool ParseDealRequest(const uint8_t* data, size_t dataSize, DealRequest& request);
    bool ParseDealItemRequest(const uint8_t* data, size_t dataSize, DealItemRequest& request);
    bool ParseDealGoldRequest(const uint8_t* data, size_t dataSize, DealGoldRequest& request);
    bool ParseMoveRequest(const uint8_t* data, size_t dataSize, MoveRequest& request);
    bool ParseSayRequest(const uint8_t* data, size_t dataSize, SayRequest& request);
    bool ParsePickupRequest(const uint8_t* data, size_t dataSize, PickupRequest& request);
    bool ParseDropItemRequest(const uint8_t* data, size_t dataSize, DropItemRequest& request);
    bool ParseUseItemRequest(const uint8_t* data, size_t dataSize, UseItemRequest& request);
    bool ParseTakeOnItemRequest(const uint8_t* data, size_t dataSize, TakeOnItemRequest& request);
    bool ParseTakeOffItemRequest(const uint8_t* data, size_t dataSize, TakeOffItemRequest& request);
    bool ParseClickNPCRequest(const uint8_t* data, size_t dataSize, ClickNPCRequest& request);

    // 组队相关解析方法
    bool ParseGroupModeRequest(const uint8_t* data, size_t dataSize, GroupModeRequest& request);
    bool ParseAddGroupMemberRequest(const uint8_t* data, size_t dataSize, AddGroupMemberRequest& request);
    bool ParseDelGroupMemberRequest(const uint8_t* data, size_t dataSize, DelGroupMemberRequest& request);
    bool ParseGroupChatRequest(const uint8_t* data, size_t dataSize, GroupChatRequest& request);
    bool ParseGroupTeleportRequest(const uint8_t* data, size_t dataSize, GroupTeleportRequest& request);
    bool ParseGroupExpModeRequest(const uint8_t* data, size_t dataSize, GroupExpModeRequest& request);
};

} // namespace Protocol
} // namespace MirServer

#include "LocalDB.h"
#include "../Common/Config.h"
#include "../Common/Utils.h"
#include "../Database/SQLiteDatabase.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <filesystem>
#include <chrono>

namespace MirServer {

// 数据库适配器类，将MirServer::IDatabase适配为MirServer::IDatabase接口
class DatabaseAdapter : public IDatabase {
public:
    DatabaseAdapter(std::shared_ptr<MirServer::IDatabase> dbImpl) : m_dbImpl(dbImpl) {}
    
    bool Connect(const std::string& connectionString) override {
        return m_dbImpl->Connect(connectionString);
    }
    
    void Disconnect() override {
        m_dbImpl->Disconnect();
    }
    
    bool IsConnected() const override {
        return m_dbImpl->IsConnected();
    }
    
    std::unique_ptr<IQueryResult> ExecuteQuery(const std::string& sql) override {
        auto results = m_dbImpl->Query(sql);
        return std::make_unique<QueryResultAdapter>(std::move(results));
    }
    
    bool ExecuteNonQuery(const std::string& sql) override {
        return m_dbImpl->Execute(sql);
    }
    
    int GetAffectedRows() const override {
        return m_dbImpl->GetAffectedRows();
    }
    
    long GetLastInsertId() const override {
        return static_cast<long>(m_dbImpl->GetLastInsertId());
    }
    
    bool BeginTransaction() override {
        m_transaction = m_dbImpl->BeginTransaction();
        return m_transaction != nullptr;
    }
    
    bool CommitTransaction() override {
        if (m_transaction) {
            bool result = m_transaction->Commit();
            m_transaction.reset();
            return result;
        }
        return false;
    }
    
    bool RollbackTransaction() override {
        if (m_transaction) {
            bool result = m_transaction->Rollback();
            m_transaction.reset();
            return result;
        }
        return false;
    }
    
    std::unique_ptr<IQueryResult> ExecuteQuery(const std::string& sql, const std::vector<std::string>& params) override {
        // 转换参数类型
        std::vector<std::any> anyParams;
        for (const auto& param : params) {
            anyParams.push_back(param);
        }
        auto results = m_dbImpl->Query(sql, anyParams);
        return std::make_unique<QueryResultAdapter>(std::move(results));
    }
    
    bool ExecuteNonQuery(const std::string& sql, const std::vector<std::string>& params) override {
        // 转换参数类型
        std::vector<std::any> anyParams;
        for (const auto& param : params) {
            anyParams.push_back(param);
        }
        return m_dbImpl->Execute(sql, anyParams);
    }
    
    std::string EscapeString(const std::string& str) override {
        // 简单的SQL转义实现
        std::string escaped = str;
        size_t pos = 0;
        while ((pos = escaped.find('\'', pos)) != std::string::npos) {
            escaped.replace(pos, 1, "''");
            pos += 2;
        }
        return escaped;
    }
    
    std::string GetLastError() const override {
        return m_dbImpl->GetLastError();
    }

private:
    // 查询结果适配器
    class QueryResultAdapter : public IQueryResult {
    public:
        QueryResultAdapter(MirServer::ResultSet results) : m_results(std::move(results)), m_currentRow(-1) {}
        
        bool Next() override {
            m_currentRow++;
            return m_currentRow < static_cast<int>(m_results.size());
        }
        
        bool HasData() const override {
            return m_currentRow >= 0 && m_currentRow < static_cast<int>(m_results.size());
        }
        
        int GetInt(const std::string& fieldName) const override {
            if (!HasData()) return 0;
            auto it = m_results[m_currentRow].find(fieldName);
            if (it == m_results[m_currentRow].end()) return 0;
            
            try {
                if (it->second.type() == typeid(int64_t)) {
                    return static_cast<int>(std::any_cast<int64_t>(it->second));
                } else if (it->second.type() == typeid(int)) {
                    return std::any_cast<int>(it->second);
                } else if (it->second.type() == typeid(std::string)) {
                    return StringToInt(std::any_cast<std::string>(it->second));
                }
            } catch (...) {}
            return 0;
        }
        
        long GetLong(const std::string& fieldName) const override {
            if (!HasData()) return 0;
            auto it = m_results[m_currentRow].find(fieldName);
            if (it == m_results[m_currentRow].end()) return 0;
            
            try {
                if (it->second.type() == typeid(int64_t)) {
                    return static_cast<long>(std::any_cast<int64_t>(it->second));
                } else if (it->second.type() == typeid(int)) {
                    return static_cast<long>(std::any_cast<int>(it->second));
                } else if (it->second.type() == typeid(std::string)) {
                    return StringToLong(std::any_cast<std::string>(it->second));
                }
            } catch (...) {}
            return 0;
        }
        
        double GetDouble(const std::string& fieldName) const override {
            if (!HasData()) return 0.0;
            auto it = m_results[m_currentRow].find(fieldName);
            if (it == m_results[m_currentRow].end()) return 0.0;
            
            try {
                if (it->second.type() == typeid(double)) {
                    return std::any_cast<double>(it->second);
                } else if (it->second.type() == typeid(float)) {
                    return static_cast<double>(std::any_cast<float>(it->second));
                } else if (it->second.type() == typeid(std::string)) {
                    return StringToDouble(std::any_cast<std::string>(it->second));
                }
            } catch (...) {}
            return 0.0;
        }
        
        std::string GetString(const std::string& fieldName) const override {
            if (!HasData()) return "";
            auto it = m_results[m_currentRow].find(fieldName);
            if (it == m_results[m_currentRow].end()) return "";
            
            try {
                if (it->second.type() == typeid(std::string)) {
                    return std::any_cast<std::string>(it->second);
                } else if (it->second.type() == typeid(int64_t)) {
                    return std::to_string(std::any_cast<int64_t>(it->second));
                } else if (it->second.type() == typeid(int)) {
                    return std::to_string(std::any_cast<int>(it->second));
                } else if (it->second.type() == typeid(double)) {
                    return std::to_string(std::any_cast<double>(it->second));
                }
            } catch (...) {}
            return "";
        }
        
        bool GetBool(const std::string& fieldName) const override {
            return GetInt(fieldName) != 0;
        }
        
        // 按索引获取（简化实现）
        int GetInt(int index) const override { return 0; }
        long GetLong(int index) const override { return 0; }
        double GetDouble(int index) const override { return 0.0; }
        std::string GetString(int index) const override { return ""; }
        bool GetBool(int index) const override { return false; }
        
        int GetFieldCount() const override {
            return HasData() ? static_cast<int>(m_results[m_currentRow].size()) : 0;
        }
        
        std::string GetFieldName(int index) const override { return ""; }
        int GetRowCount() const override { return static_cast<int>(m_results.size()); }
        
    private:
        MirServer::ResultSet m_results;
        int m_currentRow;
    };
    
    std::shared_ptr<MirServer::IDatabase> m_dbImpl;
    std::unique_ptr<MirServer::ITransaction> m_transaction;
};

// 全局实例
std::unique_ptr<LocalDB> g_LocalDB = nullptr;

LocalDB::LocalDB() {
    // 初始化
}

LocalDB::~LocalDB() {
    Finalize();
}

bool LocalDB::Initialize(std::shared_ptr<Database> database) {
    std::unique_lock<std::shared_mutex> lock(m_mutex);
    
    if (m_initialized) {
        return true;
    }
    
    // 如果没有提供数据库连接，创建一个SQLite数据库
    if (!database) {
        auto sqliteDB = std::make_shared<MirServer::SQLiteDatabase>();
        if (!sqliteDB->Connect(g_Config.database.connectionString)) {
            Logger::Error("Failed to connect to database: " + g_Config.database.connectionString);
            return false;
        }
        database = std::make_shared<DatabaseAdapter>(sqliteDB);
    }
    
    m_database = database;
    
    // 获取环境目录
    m_envirDir = g_Config.server.dataDir + "Envir/";
    
    // 确保目录存在
    std::filesystem::create_directories(m_envirDir);
    
    Logger::Info("Initializing local database cache...");
    
    // 加载所有数据
    bool success = true;
    
    // 从数据库加载核心数据
    if (LoadItemsDB() <= 0) {
        Logger::Error("Failed to load items database");
        success = false;
    }
    
    if (LoadMagicDB() <= 0) {
        Logger::Error("Failed to load magic database");
        success = false;
    }
    
    if (LoadMonsterDB() <= 0) {
        Logger::Error("Failed to load monster database");
        success = false;
    }
    
    // 从文件加载配置数据
    if (LoadAdminList() <= 0) {
        Logger::Warning("Failed to load admin list");
    }
    
    if (LoadNpcs() <= 0) {
        Logger::Warning("Failed to load NPCs");
    }
    
    if (LoadStartPoint() <= 0) {
        Logger::Warning("Failed to load start points");
    }
    
    if (LoadMonGen() <= 0) {
        Logger::Warning("Failed to load monster generation");
    }
    
    if (LoadMakeItem() <= 0) {
        Logger::Warning("Failed to load make item recipes");
    }
    
    // 构建索引
    BuildIndices();
    
    // 应用倍率配置
    ApplyPowerRates();
    
    m_lastLoadTime = GetTickCount();
    m_initialized = success;
    
    if (success) {
        Logger::Info("Local database cache initialized successfully");
        Logger::Info("Loaded: " + std::to_string(m_stdItems.size()) + " items, " + 
                    std::to_string(m_magics.size()) + " magics, " + 
                    std::to_string(m_monsters.size()) + " monsters, " + 
                    std::to_string(m_npcs.size()) + " NPCs");
    } else {
        Logger::Error("Local database cache initialization failed");
    }
    
    return success;
}

void LocalDB::Finalize() {
    std::unique_lock<std::shared_mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return;
    }
    
    // 清理缓存
    ClearCache();
    
    m_database.reset();
    m_initialized = false;
    
    Logger::Info("Local database cache finalized");
}

int LocalDB::LoadItemsDB() {
    if (!m_database) {
        Logger::Error("LocalDB", "Database connection not available");
        return -1;
    }
    
    try {
        Logger::Info("LocalDB", "Loading items database...");
        
        // 清空现有数据
        m_stdItems.clear();
        m_itemNameIndex.clear();
        
        // 查询数据库
        std::string sql = "SELECT Idx, Name, StdMode, Shape, Weight, AniCount, Source, Reserved, "
                         "Looks, DuraMax, AC, AC2, MAC, MAC2, DC, DC2, MC, MC2, SC, SC2, "
                         "Need, NeedLevel, Price FROM StdItems ORDER BY Idx";
        
        auto result = m_database->ExecuteQuery(sql);
        if (!result) {
            Logger::Error("LocalDB", "Failed to execute items query");
            return -2;
        }
        
        int loadCount = 0;
        while (result->Next()) {
            StdItemInfo item;
            
            item.idx = result->GetInt("Idx");
            item.name = result->GetString("Name");
            item.stdMode = result->GetInt("StdMode");
            item.shape = result->GetInt("Shape");
            item.weight = result->GetInt("Weight");
            item.aniCount = result->GetInt("AniCount");
            item.source = result->GetInt("Source");
            item.reserved = result->GetInt("Reserved");
            item.looks = result->GetInt("Looks");
            item.duraMax = static_cast<WORD>(result->GetInt("DuraMax"));
            
            // 处理AC值（原版使用MakeLong合并两个值）
            int ac1 = result->GetInt("AC");
            int ac2 = result->GetInt("AC2");
            item.ac = MakeLong(ac1, ac2);
            
            // 处理MAC值
            int mac1 = result->GetInt("MAC");
            int mac2 = result->GetInt("MAC2");
            item.mac = MakeLong(mac1, mac2);
            
            // 处理DC值
            int dc1 = result->GetInt("DC");
            int dc2 = result->GetInt("DC2");
            item.dc = MakeLong(dc1, dc2);
            
            // 处理MC值
            int mc1 = result->GetInt("MC");
            int mc2 = result->GetInt("MC2");
            item.mc = MakeLong(mc1, mc2);
            
            // 处理SC值
            int sc1 = result->GetInt("SC");
            int sc2 = result->GetInt("SC2");
            item.sc = MakeLong(sc1, sc2);
            
            item.need = result->GetInt("Need");
            item.needLevel = result->GetInt("NeedLevel");
            item.price = result->GetInt("Price");
            
            // 检查是否需要鉴定（这里简化处理）
            item.needIdentify = false; // TODO: 实现GetGameLogItemNameList逻辑
            
            // 验证索引顺序
            if (m_stdItems.size() != static_cast<size_t>(item.idx)) {
                Logger::Warning("LocalDB", "Item index mismatch: expected {}, got {}, name: {}", 
                               m_stdItems.size(), item.idx, item.name);
            }
            
            m_stdItems.push_back(std::move(item));
            loadCount++;
        }
        
        Logger::Info("LocalDB", "Loaded {} items from database", loadCount);
        return loadCount > 0 ? 1 : 0;
        
    } catch (const std::exception& e) {
        Logger::Error("LocalDB", "Exception loading items: {}", e.what());
        return -1;
    }
}

int LocalDB::LoadMagicDB() {
    if (!m_database) {
        Logger::Error("LocalDB", "Database connection not available");
        return -1;
    }
    
    try {
        Logger::Info("LocalDB", "Loading magic database...");
        
        // 清空现有数据
        m_magics.clear();
        m_magicNameIndex.clear();
        m_magicIdIndex.clear();
        
        // 查询数据库
        std::string sql = "SELECT MagId, MagName, EffectType, Effect, Spell, Power, MaxPower, "
                         "Job, NeedL1, NeedL2, NeedL3, L1Train, L2Train, L3Train, "
                         "Delay, DefSpell, DefPower, DefMaxPower, Descr FROM Magic WHERE MagId > 0";
        
        auto result = m_database->ExecuteQuery(sql);
        if (!result) {
            Logger::Error("LocalDB", "Failed to execute magic query");
            return -2;
        }
        
        int loadCount = 0;
        while (result->Next()) {
            MagicInfo magic;
            
            magic.magicId = static_cast<WORD>(result->GetInt("MagId"));
            magic.magicName = result->GetString("MagName");
            magic.effectType = static_cast<BYTE>(result->GetInt("EffectType"));
            magic.effect = static_cast<BYTE>(result->GetInt("Effect"));
            magic.spell = static_cast<WORD>(result->GetInt("Spell"));
            magic.power = static_cast<WORD>(result->GetInt("Power"));
            magic.maxPower = static_cast<WORD>(result->GetInt("MaxPower"));
            magic.job = static_cast<BYTE>(result->GetInt("Job"));
            
            // 训练等级要求
            magic.trainLevel[0] = static_cast<WORD>(result->GetInt("NeedL1"));
            magic.trainLevel[1] = static_cast<WORD>(result->GetInt("NeedL2"));
            magic.trainLevel[2] = static_cast<WORD>(result->GetInt("NeedL3"));
            magic.trainLevel[3] = magic.trainLevel[2]; // 原版逻辑
            
            // 最大训练值
            magic.maxTrain[0] = static_cast<DWORD>(result->GetInt("L1Train"));
            magic.maxTrain[1] = static_cast<DWORD>(result->GetInt("L2Train"));
            magic.maxTrain[2] = static_cast<DWORD>(result->GetInt("L3Train"));
            magic.maxTrain[3] = magic.maxTrain[2]; // 原版逻辑
            
            magic.trainLv = 3;
            magic.delayTime = static_cast<DWORD>(result->GetInt("Delay"));
            magic.defSpell = static_cast<BYTE>(result->GetInt("DefSpell"));
            magic.defPower = static_cast<BYTE>(result->GetInt("DefPower"));
            magic.defMaxPower = static_cast<BYTE>(result->GetInt("DefMaxPower"));
            magic.descr = result->GetString("Descr");
            
            m_magics.push_back(std::move(magic));
            loadCount++;
        }
        
        Logger::Info("LocalDB", "Loaded {} magics from database", loadCount);
        return loadCount > 0 ? 1 : 0;
        
    } catch (const std::exception& e) {
        Logger::Error("LocalDB", "Exception loading magics: {}", e.what());
        return -1;
    }
}

int LocalDB::LoadMonsterDB() {
    if (!m_database) {
        Logger::Error("LocalDB", "Database connection not available");
        return -1;
    }
    
    try {
        Logger::Info("LocalDB", "Loading monster database...");
        
        // 清空现有数据
        m_monsters.clear();
        m_monsterNameIndex.clear();
        
        // 查询数据库
        std::string sql = "SELECT NAME, Race, RaceImg, Appr, Lvl, Undead, CoolEye, Exp, "
                         "HP, MP, AC, MAC, DC, DCMAX, MC, SC, SPEED, HIT, WALK_SPD, "
                         "WalkStep, WalkWait, ATTACK_SPD FROM Monster";
        
        auto result = m_database->ExecuteQuery(sql);
        if (!result) {
            Logger::Error("LocalDB", "Failed to execute monster query");
            return -2;
        }
        
        int loadCount = 0;
        while (result->Next()) {
            MonsterInfo monster;
            
            monster.name = Trim(result->GetString("NAME"));
            monster.race = static_cast<BYTE>(result->GetInt("Race"));
            monster.raceImg = static_cast<BYTE>(result->GetInt("RaceImg"));
            monster.appr = static_cast<WORD>(result->GetInt("Appr"));
            monster.level = static_cast<WORD>(result->GetInt("Lvl"));
            monster.lifeAttrib = static_cast<BYTE>(result->GetInt("Undead"));
            monster.coolEye = static_cast<WORD>(result->GetInt("CoolEye"));
            monster.exp = static_cast<DWORD>(result->GetInt("Exp"));
            
            // HP特殊处理（原版对墙类怪物有特殊处理）
            if (monster.race == 110 || monster.race == 111) {
                monster.hp = static_cast<WORD>(result->GetInt("HP"));
            } else {
                monster.hp = static_cast<WORD>(result->GetInt("HP")); // TODO: 应用倍率
            }
            
            monster.mp = static_cast<WORD>(result->GetInt("MP"));
            monster.ac = static_cast<WORD>(result->GetInt("AC"));
            monster.mac = static_cast<WORD>(result->GetInt("MAC"));
            monster.dc = static_cast<WORD>(result->GetInt("DC"));
            monster.maxDc = static_cast<WORD>(result->GetInt("DCMAX"));
            monster.mc = static_cast<WORD>(result->GetInt("MC"));
            monster.sc = static_cast<WORD>(result->GetInt("SC"));
            monster.speed = static_cast<WORD>(result->GetInt("SPEED"));
            monster.hitPoint = static_cast<WORD>(result->GetInt("HIT"));
            monster.walkSpeed = std::max<WORD>(200, static_cast<WORD>(result->GetInt("WALK_SPD")));
            monster.walkStep = std::max<WORD>(1, static_cast<WORD>(result->GetInt("WalkStep")));
            monster.walkWait = static_cast<WORD>(result->GetInt("WalkWait"));
            monster.attackSpeed = std::max<WORD>(200, static_cast<WORD>(result->GetInt("ATTACK_SPD")));
            
            // 加载怪物掉落物品
            LoadMonItems(monster.name, monster.itemList);
            
            m_monsters.push_back(std::move(monster));
            loadCount++;
        }
        
        Logger::Info("LocalDB", "Loaded {} monsters from database", loadCount);
        return loadCount > 0 ? 1 : 0;
        
    } catch (const std::exception& e) {
        Logger::Error("LocalDB", "Exception loading monsters: {}", e.what());
        return -1;
    }
}

int LocalDB::LoadMonItems(const std::string& monName, std::vector<MonItemInfo>& itemList) {
    std::string fileName = m_envirDir + "MonItems/" + monName + ".txt";
    
    if (!std::filesystem::exists(fileName)) {
        return 0;
    }
    
    try {
        std::ifstream file(fileName);
        if (!file.is_open()) {
            return 0;
        }
        
        itemList.clear();
        std::string line;
        int loadCount = 0;
        
        while (std::getline(file, line)) {
            line = Trim(line);
            if (line.empty() || line[0] == ';') {
                continue;
            }
            
            // 解析格式: selectN/maxSel itemName count
            std::istringstream iss(line);
            std::string selectStr, itemName, countStr;
            
            if (!(iss >> selectStr >> itemName >> countStr)) {
                continue;
            }
            
            // 解析概率
            size_t slashPos = selectStr.find('/');
            if (slashPos == std::string::npos) {
                continue;
            }
            
            int selectN = std::stoi(selectStr.substr(0, slashPos));
            int maxSel = std::stoi(selectStr.substr(slashPos + 1));
            
            // 去除物品名的引号
            if (itemName.size() >= 2 && itemName[0] == '"' && itemName.back() == '"') {
                itemName = itemName.substr(1, itemName.size() - 2);
            }
            
            int count = countStr.empty() ? 1 : std::stoi(countStr);
            
            if (selectN > 0 && maxSel > 0 && !itemName.empty()) {
                MonItemInfo monItem;
                monItem.selectN = selectN - 1; // 原版逻辑：n00 = n18 - 1
                monItem.maxSel = maxSel;
                monItem.itemName = itemName;
                monItem.count = count;
                
                itemList.push_back(std::move(monItem));
                loadCount++;
            }
        }
        
        return loadCount;
        
    } catch (const std::exception& e) {
        Logger::Error("LocalDB", "Exception loading monster items for {}: {}", monName, e.what());
        return 0;
    }
}

int LocalDB::LoadAdminList() {
    std::string fileName = m_envirDir + "AdminList.txt";
    
    if (!std::filesystem::exists(fileName)) {
        Logger::Warning("LocalDB", "AdminList.txt not found");
        return 0;
    }
    
    try {
        std::ifstream file(fileName);
        if (!file.is_open()) {
            return -1;
        }
        
        m_admins.clear();
        m_adminNameIndex.clear();
        
        std::string line;
        int loadCount = 0;
        
        while (std::getline(file, line)) {
            line = Trim(line);
            if (line.empty() || line[0] == ';') {
                continue;
            }
            
            int level = -1;
            
            // 解析管理员等级
            if (line[0] == '*') level = 10;
            else if (line[0] >= '1' && line[0] <= '9') level = 10 - (line[0] - '0');
            
            if (level <= 0) {
                continue;
            }
            
            // 解析格式: level charName ipAddr
            std::istringstream iss(line.substr(1)); // 跳过等级字符
            std::string charName, ipAddr;
            
            if (!(iss >> charName >> ipAddr)) {
                continue;
            }
            
            if (!charName.empty() && !ipAddr.empty()) {
                AdminInfo admin;
                admin.level = level;
                admin.charName = charName;
                admin.ipAddr = ipAddr;
                
                m_admins.push_back(std::move(admin));
                loadCount++;
            }
        }
        
        Logger::Info("LocalDB", "Loaded {} admins from AdminList.txt", loadCount);
        return loadCount > 0 ? 1 : 0;
        
    } catch (const std::exception& e) {
        Logger::Error("LocalDB", "Exception loading admin list: {}", e.what());
        return -1;
    }
}

int LocalDB::LoadNpcs() {
    std::string fileName = m_envirDir + "Npcs.txt";
    
    if (!std::filesystem::exists(fileName)) {
        Logger::Warning("LocalDB", "Npcs.txt not found");
        return 0;
    }
    
    try {
        std::ifstream file(fileName);
        if (!file.is_open()) {
            return -1;
        }
        
        m_npcs.clear();
        m_npcNameIndex.clear();
        
        std::string line;
        int loadCount = 0;
        
        while (std::getline(file, line)) {
            line = Trim(line);
            if (line.empty() || line[0] == ';') {
                continue;
            }
            
            // 解析格式: "name" type mapName x y flag appr autoChangeColor autoChangeColorTime
            std::istringstream iss(line);
            std::string name, typeStr, mapName, xStr, yStr, flagStr, apprStr, colorStr, timeStr;
            
            // 读取带引号的名称
            if (line[0] == '"') {
                size_t endQuote = line.find('"', 1);
                if (endQuote != std::string::npos) {
                    name = line.substr(1, endQuote - 1);
                    iss.str(line.substr(endQuote + 1));
                    iss.clear();
                }
            } else {
                iss >> name;
            }
            
            if (!(iss >> typeStr >> mapName >> xStr >> yStr >> flagStr >> apprStr)) {
                continue;
            }
            
            iss >> colorStr >> timeStr; // 可选参数
            
            if (!name.empty() && !mapName.empty() && !apprStr.empty()) {
                NpcInfo npc;
                npc.name = name;
                npc.npcType = std::stoi(typeStr);
                npc.mapName = mapName;
                npc.x = std::stoi(xStr);
                npc.y = std::stoi(yStr);
                npc.flag = std::stoi(flagStr);
                npc.appr = static_cast<WORD>(std::stoi(apprStr));
                npc.autoChangeColor = !colorStr.empty() && std::stoi(colorStr) != 0;
                npc.autoChangeColorTime = timeStr.empty() ? 0 : std::stoi(timeStr) * 1000;
                
                m_npcs.push_back(std::move(npc));
                loadCount++;
            }
        }
        
        Logger::Info("LocalDB", "Loaded {} NPCs from Npcs.txt", loadCount);
        return loadCount > 0 ? 1 : 0;
        
    } catch (const std::exception& e) {
        Logger::Error("LocalDB", "Exception loading NPCs: {}", e.what());
        return -1;
    }
}

// TODO: 实现其他加载方法 (LoadStartPoint, LoadMonGen, LoadMakeItem 等)
int LocalDB::LoadStartPoint() {
    // 暂时返回1，表示成功但没有加载任何数据
    return 1;
}

int LocalDB::LoadMonGen() {
    // 暂时返回1，表示成功但没有加载任何数据
    return 1;
}

int LocalDB::LoadMakeItem() {
    // 暂时返回1，表示成功但没有加载任何数据
    return 1;
}

int LocalDB::LoadGuardList() {
    // 暂时返回1，表示成功但没有加载任何数据
    return 1;
}

int LocalDB::LoadMapInfo() {
    // 暂时返回1，表示成功但没有加载任何数据
    return 1;
}

int LocalDB::LoadMapEvent() {
    // 暂时返回1，表示成功但没有加载任何数据
    return 1;
}

int LocalDB::LoadMapQuest() {
    // 暂时返回1，表示成功但没有加载任何数据
    return 1;
}

int LocalDB::LoadQuestDiary() {
    // 暂时返回1，表示成功但没有加载任何数据
    return 1;
}

int LocalDB::LoadMinMap() {
    // 暂时返回1，表示成功但没有加载任何数据
    return 1;
}

int LocalDB::LoadUnbindList() {
    // 暂时返回1，表示成功但没有加载任何数据
    return 1;
}

int LocalDB::LoadMerchant() {
    // 暂时返回1，表示成功但没有加载任何数据
    return 1;
}

// 查询方法实现
const StdItemInfo* LocalDB::GetStdItem(int idx) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    
    if (idx < 0 || idx >= static_cast<int>(m_stdItems.size())) {
        return nullptr;
    }
    
    return &m_stdItems[idx];
}

const StdItemInfo* LocalDB::GetStdItemByName(const std::string& name) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    
    auto it = m_itemNameIndex.find(name);
    if (it == m_itemNameIndex.end()) {
        return nullptr;
    }
    
    return &m_stdItems[it->second];
}

const MagicInfo* LocalDB::GetMagic(WORD magicId) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    
    auto it = m_magicIdIndex.find(magicId);
    if (it == m_magicIdIndex.end()) {
        return nullptr;
    }
    
    return &m_magics[it->second];
}

const MagicInfo* LocalDB::GetMagicByName(const std::string& name) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    
    auto it = m_magicNameIndex.find(name);
    if (it == m_magicNameIndex.end()) {
        return nullptr;
    }
    
    return &m_magics[it->second];
}

const MonsterInfo* LocalDB::GetMonster(const std::string& name) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    
    auto it = m_monsterNameIndex.find(name);
    if (it == m_monsterNameIndex.end()) {
        return nullptr;
    }
    
    return &m_monsters[it->second];
}

const AdminInfo* LocalDB::GetAdmin(const std::string& charName) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    
    auto it = m_adminNameIndex.find(charName);
    if (it == m_adminNameIndex.end()) {
        return nullptr;
    }
    
    return &m_admins[it->second];
}

const NpcInfo* LocalDB::GetNpc(const std::string& name) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    
    auto it = m_npcNameIndex.find(name);
    if (it == m_npcNameIndex.end()) {
        return nullptr;
    }
    
    return &m_npcs[it->second];
}

// 重载方法
void LocalDB::ReloadItems() {
    Logger::Info("LocalDB", "Reloading items database...");
    LoadItemsDB();
    BuildIndices();
}

void LocalDB::ReloadMagic() {
    Logger::Info("LocalDB", "Reloading magic database...");
    LoadMagicDB();
    BuildIndices();
}

void LocalDB::ReloadMonsters() {
    Logger::Info("LocalDB", "Reloading monster database...");
    LoadMonsterDB();
    BuildIndices();
}

void LocalDB::ReloadMerchants() {
    Logger::Info("LocalDB", "Reloading merchants...");
    // TODO: 实现商人重载逻辑
}

void LocalDB::ReloadNpc() {
    Logger::Info("LocalDB", "Reloading NPCs...");
    LoadNpcs();
    BuildIndices();
}

// 缓存管理
void LocalDB::ClearCache() {
    m_stdItems.clear();
    m_itemNameIndex.clear();
    m_magics.clear();
    m_magicNameIndex.clear();
    m_magicIdIndex.clear();
    m_monsters.clear();
    m_monsterNameIndex.clear();
    m_admins.clear();
    m_adminNameIndex.clear();
    m_npcs.clear();
    m_npcNameIndex.clear();
    m_startPoints.clear();
    m_startPointMapIndex.clear();
    m_monGens.clear();
    m_monGenMapIndex.clear();
    m_makeItems.clear();
    m_maps.clear();
    m_mapEvents.clear();
}

void LocalDB::RefreshCache() {
    Logger::Info("LocalDB", "Refreshing local database cache...");
    
    ClearCache();
    
    // 重新加载所有数据
    LoadItemsDB();
    LoadMagicDB();
    LoadMonsterDB();
    LoadAdminList();
    LoadNpcs();
    LoadStartPoint();
    LoadMonGen();
    LoadMakeItem();
    
    BuildIndices();
    ApplyPowerRates();
    
    m_lastLoadTime = GetTickCount();
    
    Logger::Info("LocalDB", "Cache refreshed successfully");
}

bool LocalDB::IsCacheValid() const {
    if (!m_initialized) {
        return false;
    }
    
    DWORD currentTime = GetTickCount();
    return (currentTime - m_lastLoadTime) < m_cacheTimeout;
}

// 统计信息
LocalDB::Statistics LocalDB::GetStatistics() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    Statistics stats = m_statistics;
    stats.itemCount = m_stdItems.size();
    stats.magicCount = m_magics.size();
    stats.monsterCount = m_monsters.size();
    stats.npcCount = m_npcs.size();
    stats.adminCount = m_admins.size();
    stats.mapCount = m_maps.size();
    stats.lastLoadTime = m_lastLoadTime;
    
    return stats;
}

// 数据验证
bool LocalDB::ValidateData() const {
    std::lock_guard<std::mutex> lock(m_errorsMutex);
    m_validationErrors.clear();
    
    bool isValid = true;
    
    // 验证物品数据
    for (size_t i = 0; i < m_stdItems.size(); ++i) {
        const auto& item = m_stdItems[i];
        if (item.name.empty()) {
            m_validationErrors.push_back("Item " + std::to_string(i) + " has empty name");
            isValid = false;
        }
        if (item.idx != static_cast<int>(i)) {
            m_validationErrors.push_back("Item " + item.name + " has incorrect index");
            isValid = false;
        }
    }
    
    // 验证魔法数据
    for (const auto& magic : m_magics) {
        if (magic.magicName.empty()) {
            m_validationErrors.push_back("Magic " + std::to_string(magic.magicId) + " has empty name");
            isValid = false;
        }
    }
    
    // 验证怪物数据
    for (const auto& monster : m_monsters) {
        if (monster.name.empty()) {
            m_validationErrors.push_back("Monster has empty name");
            isValid = false;
        }
    }
    
    return isValid;
}

std::vector<std::string> LocalDB::GetValidationErrors() const {
    std::lock_guard<std::mutex> lock(m_errorsMutex);
    return m_validationErrors;
}

// 私有辅助方法
void LocalDB::BuildIndices() {
    // 构建物品名称索引
    m_itemNameIndex.clear();
    for (size_t i = 0; i < m_stdItems.size(); ++i) {
        m_itemNameIndex[m_stdItems[i].name] = i;
    }
    
    // 构建魔法索引
    m_magicNameIndex.clear();
    m_magicIdIndex.clear();
    for (size_t i = 0; i < m_magics.size(); ++i) {
        m_magicNameIndex[m_magics[i].magicName] = i;
        m_magicIdIndex[m_magics[i].magicId] = i;
    }
    
    // 构建怪物名称索引
    m_monsterNameIndex.clear();
    for (size_t i = 0; i < m_monsters.size(); ++i) {
        m_monsterNameIndex[m_monsters[i].name] = i;
    }
    
    // 构建管理员名称索引
    m_adminNameIndex.clear();
    for (size_t i = 0; i < m_admins.size(); ++i) {
        m_adminNameIndex[m_admins[i].charName] = i;
    }
    
    // 构建NPC名称索引
    m_npcNameIndex.clear();
    for (size_t i = 0; i < m_npcs.size(); ++i) {
        m_npcNameIndex[m_npcs[i].name] = i;
    }
    
    Logger::Debug("LocalDB", "Built indices for all cached data");
}

void LocalDB::ApplyPowerRates() {
    // 从配置文件读取倍率
    double monsterPowerRate = g_Config.game.monsterPowerRate / 10.0;
    double itemsACPowerRate = g_Config.game.itemsACPowerRate / 10.0;
    double itemsPowerRate = g_Config.game.itemsPowerRate / 10.0;
    
    // 应用怪物倍率
    for (auto& monster : m_monsters) {
        if (monster.race != 110 && monster.race != 111) { // 非墙类怪物
            monster.hp = static_cast<WORD>(monster.hp * monsterPowerRate);
            monster.mp = static_cast<WORD>(monster.mp * monsterPowerRate);
            monster.ac = static_cast<WORD>(monster.ac * monsterPowerRate);
            monster.mac = static_cast<WORD>(monster.mac * monsterPowerRate);
            monster.dc = static_cast<WORD>(monster.dc * monsterPowerRate);
            monster.maxDc = static_cast<WORD>(monster.maxDc * monsterPowerRate);
            monster.mc = static_cast<WORD>(monster.mc * monsterPowerRate);
            monster.sc = static_cast<WORD>(monster.sc * monsterPowerRate);
        }
    }
    
    // 应用物品倍率
    for (auto& item : m_stdItems) {
        // AC/MAC应用AC倍率
        WORD ac1 = LoWord(item.ac);
        WORD ac2 = HiWord(item.ac);
        ac1 = static_cast<WORD>(ac1 * itemsACPowerRate);
        ac2 = static_cast<WORD>(ac2 * itemsACPowerRate);
        item.ac = MakeLong(ac1, ac2);
        
        WORD mac1 = LoWord(item.mac);
        WORD mac2 = HiWord(item.mac);
        mac1 = static_cast<WORD>(mac1 * itemsACPowerRate);
        mac2 = static_cast<WORD>(mac2 * itemsACPowerRate);
        item.mac = MakeLong(mac1, mac2);
        
        // DC/MC/SC应用攻击倍率
        WORD dc1 = LoWord(item.dc);
        WORD dc2 = HiWord(item.dc);
        dc1 = static_cast<WORD>(dc1 * itemsPowerRate);
        dc2 = static_cast<WORD>(dc2 * itemsPowerRate);
        item.dc = MakeLong(dc1, dc2);
        
        WORD mc1 = LoWord(item.mc);
        WORD mc2 = HiWord(item.mc);
        mc1 = static_cast<WORD>(mc1 * itemsPowerRate);
        mc2 = static_cast<WORD>(mc2 * itemsPowerRate);
        item.mc = MakeLong(mc1, mc2);
        
        WORD sc1 = LoWord(item.sc);
        WORD sc2 = HiWord(item.sc);
        sc1 = static_cast<WORD>(sc1 * itemsPowerRate);
        sc2 = static_cast<WORD>(sc2 * itemsPowerRate);
        item.sc = MakeLong(sc1, sc2);
    }
}

// TODO: 实现其他方法 (LoadNpcScript, LoadGoodRecord 等)

} // namespace MirServer 
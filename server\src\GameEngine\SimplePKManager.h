#pragma once

#include "../Common/Types.h"
#include <memory>
#include <unordered_map>
#include <vector>
#include <string>
#include <mutex>

namespace MirServer {

// 行会战信息
struct GuildWarInfo {
    std::string guild1;         // 行会1名称
    std::string guild2;         // 行会2名称
    DWORD startTime = 0;        // 开始时间
    DWORD duration = 0;         // 持续时间(分钟)
    bool isActive = false;      // 是否激活
};

// 简化的PK系统管理器（仅用于测试）
class SimplePKManager {
public:
    static SimplePKManager& GetInstance();
    
    // 初始化和清理
    void Initialize();
    void Finalize();
    
    // 行会战管理
    bool StartGuildWar(const std::string& guild1, const std::string& guild2, DWORD duration = 30);
    bool EndGuildWar(const std::string& guild1, const std::string& guild2);
    bool IsGuildWar(const std::string& guild1, const std::string& guild2) const;
    std::vector<GuildWarInfo> GetActiveGuildWars() const;
    
    // 配置管理
    void SetKillAddPKValue(DWORD value) { m_killAddPKValue = value; }
    void SetPKFlagTime(DWORD time) { m_pkFlagTime = time; }
    void SetRedNameTime(DWORD time) { m_redNameTime = time; }
    void SetYellowNamePKValue(DWORD value) { m_yellowNamePKValue = value; }
    void SetRedNamePKValue(DWORD value) { m_redNamePKValue = value; }
    
    DWORD GetKillAddPKValue() const { return m_killAddPKValue; }
    DWORD GetPKFlagTime() const { return m_pkFlagTime; }
    DWORD GetRedNameTime() const { return m_redNameTime; }
    DWORD GetYellowNamePKValue() const { return m_yellowNamePKValue; }
    DWORD GetRedNamePKValue() const { return m_redNamePKValue; }
    
    // 定时更新
    void Update();
    
private:
    SimplePKManager() = default;
    ~SimplePKManager() = default;
    SimplePKManager(const SimplePKManager&) = delete;
    SimplePKManager& operator=(const SimplePKManager&) = delete;
    
    // 内部方法
    void ProcessGuildWars();
    
private:
    mutable std::mutex m_mutex;
    
    // 行会战信息
    std::vector<GuildWarInfo> m_guildWars;
    
    // 配置参数
    DWORD m_killAddPKValue = 100;       // 杀人增加PK值
    DWORD m_pkFlagTime = 300;           // PK标记时间(秒)
    DWORD m_redNameTime = 3600;         // 红名持续时间(秒)
    DWORD m_yellowNamePKValue = 100;    // 黄名PK值阈值
    DWORD m_redNamePKValue = 200;       // 红名PK值阈值
    
    // 时间管理
    DWORD m_lastUpdateTime = 0;
    
    bool m_initialized = false;
};

} // namespace MirServer

#include "PKManager.h"
#include "GuildManager.h"
#include "../BaseObject/PlayObject.h"
#include "Environment.h"
#include "MapManager.h"
#include "../Common/Logger.h"
#include <algorithm>
#include <ctime>

namespace MirServer {

PKManager& PKManager::GetInstance() {
    static PKManager instance;
    return instance;
}

void PKManager::Initialize() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_initialized) {
        return;
    }

    m_lastUpdateTime = GetTickCount();
    m_lastDecayTime = m_lastUpdateTime;
    m_initialized = true;

    Logger::Info("PKManager initialized");
}

void PKManager::Finalize() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) {
        return;
    }

    m_pkInfoMap.clear();
    m_guildWars.clear();
    m_initialized = false;

    Logger::Info("PKManager finalized");
}

void PKManager::OnPlayerKill(PlayObject* killer, PlayObject* victim) {
    if (!killer || !victim || killer == victim) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查是否在安全区
    if (IsInSafeZone(killer) || IsInSafeZone(victim)) {
        return;
    }

    // 检查是否可以攻击
    if (!CanAttack(killer, victim)) {
        return;
    }

    // 获取PK信息
    PKInfo& killerInfo = GetOrCreatePKInfo(killer);
    PKInfo& victimInfo = GetOrCreatePKInfo(victim);

    // 更新杀人数据
    killerInfo.killCount++;
    killerInfo.lastKillTime = GetTickCount();
    victimInfo.deathCount++;
    victimInfo.lastDeathTime = GetTickCount();

    // 根据被杀者的PK状态决定增加的PK值
    DWORD addPKValue = m_killAddPKValue;

    // 如果被杀者是白名，增加更多PK值
    if (!IsRedName(victim) && !IsYellowName(victim)) {
        addPKValue = m_killAddPKValue;
    }
    // 如果被杀者是红名，增加较少PK值
    else if (IsRedName(victim)) {
        addPKValue = m_killAddPKValue / 2;
    }
    // 如果被杀者是黄名，增加正常PK值
    else {
        addPKValue = m_killAddPKValue * 3 / 4;
    }

    // 增加杀手的PK值
    AddPKValue(killer, addPKValue);

    // 应用PK惩罚
    ApplyPKPenalty(victim);

    Logger::Info("Player " + killer->GetCharName() + " killed " + victim->GetCharName() +
                ", PK value increased by " + std::to_string(addPKValue));
}

void PKManager::OnPlayerDeath(PlayObject* player, BaseObject* killer) {
    if (!player) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    PKInfo& playerInfo = GetOrCreatePKInfo(player);
    playerInfo.deathCount++;
    playerInfo.lastDeathTime = GetTickCount();

    // 如果是红名死亡，减少PK值
    if (IsRedName(player)) {
        DWORD reducePKValue = m_killAddPKValue / 2;
        SubPKValue(player, reducePKValue);

        Logger::Info("Red name player " + player->GetCharName() +
                    " died, PK value reduced by " + std::to_string(reducePKValue));
    }

    // 如果有杀手且杀手是玩家，记录相关信息
    if (killer && killer->GetObjectType() == ObjectType::HUMAN) {
        PlayObject* killerPlayer = static_cast<PlayObject*>(killer);
        Logger::Debug("Player " + player->GetCharName() + " was killed by " + killerPlayer->GetCharName());
    }
}

void PKManager::OnPlayerLogin(PlayObject* player) {
    if (!player) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 获取或创建PK信息
    PKInfo& info = GetOrCreatePKInfo(player);

    // 从玩家数据中加载PK值
    info.pkValue = player->GetPKPoint();

    // 更新PK状态
    UpdatePKStatus(player);

    Logger::Debug("Player " + player->GetCharName() + " login, PK value: " + std::to_string(info.pkValue));
}

void PKManager::OnPlayerLogout(PlayObject* player) {
    if (!player) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 保存PK信息到玩家数据
    auto it = m_pkInfoMap.find(player->GetCharName());
    if (it != m_pkInfoMap.end()) {
        player->IncPKPoint(static_cast<int>(it->second.pkValue) - static_cast<int>(player->GetPKPoint()));

        Logger::Debug("Player " + player->GetCharName() + " logout, PK value saved: " +
                     std::to_string(it->second.pkValue));
    }
}

void PKManager::AddPKValue(PlayObject* player, DWORD value) {
    if (!player || value == 0) {
        return;
    }

    PKInfo& info = GetOrCreatePKInfo(player);
    info.pkValue += value;

    // 更新玩家对象的PK值
    player->IncPKPoint(static_cast<int>(value));

    // 更新PK状态
    UpdatePKStatus(player);

    // 通知状态变化
    NotifyPKStatusChange(player);
}

void PKManager::SubPKValue(PlayObject* player, DWORD value) {
    if (!player || value == 0) {
        return;
    }

    PKInfo& info = GetOrCreatePKInfo(player);

    if (info.pkValue >= value) {
        info.pkValue -= value;
        player->IncPKPoint(-static_cast<int>(value));
    } else {
        player->IncPKPoint(-static_cast<int>(info.pkValue));
        info.pkValue = 0;
    }

    // 更新PK状态
    UpdatePKStatus(player);

    // 通知状态变化
    NotifyPKStatusChange(player);
}

void PKManager::SetPKValue(PlayObject* player, DWORD value) {
    if (!player) {
        return;
    }

    PKInfo& info = GetOrCreatePKInfo(player);
    int diff = static_cast<int>(value) - static_cast<int>(info.pkValue);

    info.pkValue = value;
    player->IncPKPoint(diff);

    // 更新PK状态
    UpdatePKStatus(player);

    // 通知状态变化
    NotifyPKStatusChange(player);
}

DWORD PKManager::GetPKValue(PlayObject* player) const {
    if (!player) {
        return 0;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_pkInfoMap.find(player->GetCharName());
    if (it != m_pkInfoMap.end()) {
        return it->second.pkValue;
    }

    return player->GetPKPoint();
}

bool PKManager::IsRedName(PlayObject* player) const {
    if (!player) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_pkInfoMap.find(player->GetCharName());
    if (it != m_pkInfoMap.end()) {
        return it->second.isRedName;
    }

    return player->GetPKPoint() >= m_redNamePKValue;
}

bool PKManager::IsYellowName(PlayObject* player) const {
    if (!player) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_pkInfoMap.find(player->GetCharName());
    if (it != m_pkInfoMap.end()) {
        return it->second.isYellowName;
    }

    DWORD pkValue = player->GetPKPoint();
    return pkValue >= m_yellowNamePKValue && pkValue < m_redNamePKValue;
}

bool PKManager::CanAttack(PlayObject* attacker, PlayObject* target) const {
    if (!attacker || !target || attacker == target) {
        return false;
    }

    // 检查安全区
    if (IsInSafeZone(attacker) || IsInSafeZone(target)) {
        return false;
    }

    // 检查攻击模式
    PKMode mode = GetAttackMode(attacker);

    switch (mode) {
        case PKMode::PEACE:
            return false;

        case PKMode::ALL:
            return true;

        case PKMode::GROUP:
            return !IsGroupMember(attacker, target);

        case PKMode::GUILD:
            return !IsGuildMember(attacker, target);

        case PKMode::DEAR:
            return !IsDearPartner(attacker, target);

        case PKMode::MASTER:
            return !IsMasterStudent(attacker, target);

        case PKMode::REDNAME:
            return IsRedName(target);

        default:
            return false;
    }
}

bool PKManager::IsInSafeZone(PlayObject* player) const {
    if (!player) {
        return false;
    }

    Environment* env = player->GetEnvironment();
    if (!env) {
        return false;
    }

    return env->IsSafeZone(player->GetCurrentPos());
}

PKInfo& PKManager::GetOrCreatePKInfo(PlayObject* player) {
    const std::string& charName = player->GetCharName();

    auto it = m_pkInfoMap.find(charName);
    if (it == m_pkInfoMap.end()) {
        PKInfo info;
        info.pkValue = player->GetPKPoint();
        m_pkInfoMap[charName] = info;
        return m_pkInfoMap[charName];
    }

    return it->second;
}

void PKManager::UpdatePKStatus(PlayObject* player) {
    PKInfo& info = GetOrCreatePKInfo(player);

    bool wasRedName = info.isRedName;
    bool wasYellowName = info.isYellowName;

    // 更新状态
    info.isRedName = (info.pkValue >= m_redNamePKValue);
    info.isYellowName = (info.pkValue >= m_yellowNamePKValue && info.pkValue < m_redNamePKValue);

    // 如果变成红名，设置红名时间
    if (!wasRedName && info.isRedName) {
        info.redTime = m_redNameTime;
    }

    // 如果不再是红名，清除红名时间
    if (wasRedName && !info.isRedName) {
        info.redTime = 0;
    }
}

void PKManager::SetAttackMode(PlayObject* player, PKMode mode) {
    if (!player) {
        return;
    }

    // 转换为AttackMode枚举
    AttackMode attackMode = static_cast<AttackMode>(static_cast<BYTE>(mode));
    player->SetAttackMode(attackMode);

    Logger::Debug("Player " + player->GetCharName() + " attack mode changed to " + std::to_string(static_cast<int>(mode)));
}

PKMode PKManager::GetAttackMode(PlayObject* player) const {
    if (!player) {
        return PKMode::PEACE;
    }

    AttackMode attackMode = player->GetAttackMode();
    return static_cast<PKMode>(static_cast<BYTE>(attackMode));
}

bool PKManager::IsValidTarget(PlayObject* attacker, PlayObject* target) const {
    return CanAttack(attacker, target);
}

bool PKManager::StartGuildWar(const std::string& guild1, const std::string& guild2, DWORD duration) {
    if (guild1.empty() || guild2.empty() || guild1 == guild2) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查是否已经在战争中
    if (IsGuildWar(guild1, guild2)) {
        return false;
    }

    // 创建新的行会战
    GuildWarInfo warInfo;
    warInfo.guild1 = guild1;
    warInfo.guild2 = guild2;
    warInfo.startTime = GetTickCount();
    warInfo.duration = duration;
    warInfo.isActive = true;

    m_guildWars.push_back(warInfo);

    Logger::Info("Guild war started between " + guild1 + " and " + guild2 +
                " for " + std::to_string(duration) + " minutes");

    return true;
}

bool PKManager::EndGuildWar(const std::string& guild1, const std::string& guild2) {
    if (guild1.empty() || guild2.empty()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_guildWars.begin(), m_guildWars.end(),
        [&](const GuildWarInfo& war) {
            return war.isActive &&
                   ((war.guild1 == guild1 && war.guild2 == guild2) ||
                    (war.guild1 == guild2 && war.guild2 == guild1));
        });

    if (it != m_guildWars.end()) {
        it->isActive = false;

        Logger::Info("Guild war ended between " + guild1 + " and " + guild2);
        return true;
    }

    return false;
}

bool PKManager::IsGuildWar(const std::string& guild1, const std::string& guild2) const {
    if (guild1.empty() || guild2.empty()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_guildWars.begin(), m_guildWars.end(),
        [&](const GuildWarInfo& war) {
            return war.isActive &&
                   ((war.guild1 == guild1 && war.guild2 == guild2) ||
                    (war.guild1 == guild2 && war.guild2 == guild1));
        });

    return it != m_guildWars.end();
}

std::vector<GuildWarInfo> PKManager::GetActiveGuildWars() const {
    std::lock_guard<std::mutex> lock(m_mutex);

    std::vector<GuildWarInfo> activeWars;
    for (const auto& war : m_guildWars) {
        if (war.isActive) {
            activeWars.push_back(war);
        }
    }

    return activeWars;
}

void PKManager::Update() {
    if (!m_initialized) {
        return;
    }

    DWORD currentTime = GetTickCount();

    // 每秒更新一次
    if (currentTime - m_lastUpdateTime >= 1000) {
        std::lock_guard<std::mutex> lock(m_mutex);

        // 处理PK值衰减
        if (currentTime - m_lastDecayTime >= m_pkDecayInterval * 1000) {
            ProcessPKDecay();
            m_lastDecayTime = currentTime;
        }

        // 处理行会战
        ProcessGuildWars();

        m_lastUpdateTime = currentTime;
    }
}

void PKManager::UpdateRedTime(PlayObject* player) {
    if (!player) {
        return;
    }

    PKInfo& info = GetOrCreatePKInfo(player);

    if (info.redTime > 0) {
        info.redTime--;

        // 如果红名时间结束，重新计算状态
        if (info.redTime == 0) {
            UpdatePKStatus(player);
            NotifyPKStatusChange(player);
        }
    }
}

void PKManager::ProcessPKDecay() {
    // 遍历所有PK信息，进行PK值衰减
    for (auto& pair : m_pkInfoMap) {
        PKInfo& info = pair.second;

        if (info.pkValue > 0) {
            if (info.pkValue >= m_pkDecayValue) {
                info.pkValue -= m_pkDecayValue;
            } else {
                info.pkValue = 0;
            }

            // TODO: 如果玩家在线，更新其PK状态
            // 这里需要通过UserEngine获取在线玩家
        }
    }
}

void PKManager::ProcessGuildWars() {
    DWORD currentTime = GetTickCount();

    // 检查行会战是否超时
    for (auto& war : m_guildWars) {
        if (war.isActive) {
            DWORD elapsedTime = (currentTime - war.startTime) / 1000 / 60; // 转换为分钟

            if (elapsedTime >= war.duration) {
                war.isActive = false;

                Logger::Info("Guild war between " + war.guild1 + " and " + war.guild2 + " ended due to timeout");
            }
        }
    }

    // 清理过期的行会战记录
    m_guildWars.erase(
        std::remove_if(m_guildWars.begin(), m_guildWars.end(),
            [currentTime](const GuildWarInfo& war) {
                return !war.isActive &&
                       (currentTime - war.startTime) > 24 * 60 * 60 * 1000; // 24小时后清理
            }),
        m_guildWars.end()
    );
}

void PKManager::ApplyPKPenalty(PlayObject* player) {
    if (!player) {
        return;
    }

    // TODO: 实现PK死亡惩罚
    // 1. 掉落物品
    // 2. 损失经验值
    // 3. 其他惩罚机制

    Logger::Debug("Applied PK penalty to player " + player->GetCharName());
}

void PKManager::NotifyPKStatusChange(PlayObject* player) {
    if (!player) {
        return;
    }

    // TODO: 通知客户端PK状态变化
    // 发送相应的协议包

    Logger::Debug("Notified PK status change for player " + player->GetCharName());
}

bool PKManager::IsGroupMember(PlayObject* player1, PlayObject* player2) const {
    if (!player1 || !player2) {
        return false;
    }

    return player1->IsGroupMember(player2);
}

bool PKManager::IsGuildMember(PlayObject* player1, PlayObject* player2) const {
    if (!player1 || !player2) {
        return false;
    }

    return player1->IsGuildMember(player2);
}

bool PKManager::IsDearPartner(PlayObject* player1, PlayObject* player2) const {
    if (!player1 || !player2) {
        return false;
    }

    // TODO: 实现夫妻关系检查
    // 需要夫妻系统支持
    return false;
}

bool PKManager::IsMasterStudent(PlayObject* player1, PlayObject* player2) const {
    if (!player1 || !player2) {
        return false;
    }

    // TODO: 实现师徒关系检查
    // 需要师徒系统支持
    return false;
}

} // namespace MirServer

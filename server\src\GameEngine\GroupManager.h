#pragma once

#include "../Common/Types.h"
#include "../Common/Logger.h"
#include "../BaseObject/PlayObject.h"
#include "../Protocol/PacketTypes.h"
#include <memory>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <string>

namespace MirServer {

// 前向声明
class PlayObject;

// 组队模式枚举（对应原项目的组队模式）
enum class GroupMode : BYTE {
    NONE = 0,           // 无组队
    GROUP = 1,          // 组队模式
    ALLOW_GROUP = 2     // 允许组队
};

// 组队经验分配模式
enum class ExpShareMode : BYTE {
    EQUAL = 0,          // 平均分配
    LEVEL_BASED = 1     // 按等级分配
};

// 组队信息结构
struct GroupInfo {
    std::string groupId;                                    // 组队ID
    std::shared_ptr<PlayObject> leader;                     // 队长
    std::vector<std::shared_ptr<PlayObject>> members;       // 队员列表
    ExpShareMode expShareMode = ExpShareMode::EQUAL;        // 经验分配模式
    DWORD createTime;                                       // 创建时间
    bool allowJoin = true;                                  // 是否允许加入
    int maxMembers = 8;                                     // 最大成员数

    GroupInfo() : createTime(GetCurrentTime()) {}
};

// 组队管理器类（对应原项目的组队系统）
class GroupManager {
public:
    static GroupManager& GetInstance();

    // 初始化和清理
    bool Initialize();
    void Finalize();

    // 组队基本操作
    bool CreateGroup(PlayObject* leader);
    bool JoinGroup(PlayObject* player, const std::string& groupId);
    bool LeaveGroup(PlayObject* player);
    bool KickMember(PlayObject* leader, const std::string& memberName);
    bool DisbandGroup(PlayObject* leader);
    bool ChangeLeader(PlayObject* currentLeader, PlayObject* newLeader);

    // 组队邀请系统
    bool InvitePlayer(PlayObject* inviter, PlayObject* target);
    bool AcceptInvite(PlayObject* player, const std::string& inviterName);
    bool RejectInvite(PlayObject* player, const std::string& inviterName);

    // 组队查询
    std::shared_ptr<GroupInfo> GetPlayerGroup(PlayObject* player) const;
    std::shared_ptr<GroupInfo> GetGroup(const std::string& groupId) const;
    bool IsInGroup(PlayObject* player) const;
    bool IsGroupLeader(PlayObject* player) const;
    bool IsGroupMember(PlayObject* player1, PlayObject* player2) const;
    std::vector<std::shared_ptr<PlayObject>> GetGroupMembers(PlayObject* player) const;

    // 组队经验分配
    void ShareExperience(PlayObject* player, DWORD totalExp);
    void SetExpShareMode(PlayObject* leader, ExpShareMode mode);

    // 组队聊天
    void SendGroupMessage(PlayObject* sender, const std::string& message);

    // 组队状态管理
    void SetGroupMode(PlayObject* player, GroupMode mode);
    GroupMode GetGroupMode(PlayObject* player) const;

    // 组队信息同步
    void SendGroupInfo(PlayObject* player);
    void SendGroupMemberList(PlayObject* player);
    void NotifyGroupChange(const std::string& groupId, PlayObject* excludePlayer = nullptr);

    // 运行时处理
    void Run();
    void ProcessInvites();
    void CheckGroupStatus();

    // 玩家离线处理
    void OnPlayerLogout(PlayObject* player);
    void OnPlayerLogin(PlayObject* player);

    // 高级功能
    bool IsInExpRange(PlayObject* player1, PlayObject* player2) const;
    bool IsLevelDiffValid(PlayObject* player1, PlayObject* player2) const;
    void UpdateGroupPositions(const std::string& groupId);
    void SendGroupPositionUpdate(PlayObject* player);

    // 组队副本和特殊功能
    bool CanEnterDungeon(PlayObject* player, const std::string& dungeonName) const;
    void TeleportGroupToLeader(PlayObject* leader);
    void TeleportGroupToPosition(PlayObject* leader, const std::string& mapName, int x, int y);

    // 组队统计
    int GetGroupAverageLevel(const std::string& groupId) const;
    int GetGroupTotalLevel(const std::string& groupId) const;
    std::vector<std::string> GetGroupMemberNames(const std::string& groupId) const;

private:
    GroupManager() = default;
    ~GroupManager() = default;
    GroupManager(const GroupManager&) = delete;
    GroupManager& operator=(const GroupManager&) = delete;

    // 内部方法
    std::string GenerateGroupId();
    bool ValidateGroupOperation(PlayObject* player, const std::string& operation) const;
    void RemovePlayerFromGroup(PlayObject* player, std::shared_ptr<GroupInfo> group);
    void UpdateGroupLeader(std::shared_ptr<GroupInfo> group);
    void CleanupEmptyGroups();

    // 经验分配计算
    std::vector<std::pair<std::shared_ptr<PlayObject>, DWORD>> CalculateExpShare(
        std::shared_ptr<GroupInfo> group, DWORD totalExp) const;

    // 消息发送
    void SendToGroupMembers(std::shared_ptr<GroupInfo> group, WORD msgType,
                           WORD param1 = 0, WORD param2 = 0, WORD param3 = 0,
                           const std::string& data = "", PlayObject* excludePlayer = nullptr);

private:
    mutable std::mutex m_mutex;
    bool m_initialized = false;

    // 组队数据
    std::unordered_map<std::string, std::shared_ptr<GroupInfo>> m_groups;
    std::unordered_map<std::string, std::string> m_playerGroups;  // 玩家名 -> 组队ID
    std::unordered_map<std::string, GroupMode> m_playerGroupModes; // 玩家组队模式

    // 邀请系统
    struct GroupInvite {
        std::string inviterName;
        std::string targetName;
        std::string groupId;
        DWORD inviteTime;
        DWORD expireTime;
    };
    std::vector<GroupInvite> m_pendingInvites;

    // 配置参数
    static constexpr int MAX_GROUP_MEMBERS = 8;
    static constexpr DWORD INVITE_EXPIRE_TIME = 30000; // 30秒
    static constexpr int GROUP_EXP_RANGE = 15;         // 组队经验分享范围
    static constexpr int GROUP_CHAT_RANGE = 50;        // 组队聊天范围
    static constexpr DWORD GROUP_STATUS_UPDATE_INTERVAL = 5000; // 状态更新间隔
    static constexpr int MAX_LEVEL_DIFF = 10;          // 最大等级差异
};

} // namespace MirServer

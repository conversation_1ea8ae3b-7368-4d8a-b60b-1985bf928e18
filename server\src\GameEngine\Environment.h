#pragma once

#include "../BaseObject/BaseObject.h"
#include "../Common/Types.h"
#include <vector>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <functional>
#include <string>

namespace MirServer {

// 前向声明
class MapManager;
class PlayObject;
class Monster;
class NPC;

// 地图单元格信息
struct MapCell {
    bool canWalk = true;           // 是否可行走
    bool canFly = true;            // 是否可飞行
    bool canStand = true;          // 是否可站立
    std::vector<std::shared_ptr<BaseObject>> objects; // 该位置的对象列表
};

// 地图事件类型
enum class MapEventType {
    SAFE_ZONE,              // 安全区
    FIGHT_ZONE,            // 战斗区
    FIGHT3_ZONE,           // 行会战区
    NO_RECONNECT,          // 禁止重连区
    NO_RANDOM,             // 禁止随机传送
    NO_DRUG,               // 禁止使用药品
    MINE_ZONE,             // 矿区
    NO_POSITION_MOVE,      // 禁止使用移动符
    NO_RECALL,             // 禁止召回
    NO_GUILD_RECALL,       // 禁止行会召回
    NO_DEAR_RECALL,        // 禁止夫妻召回
    NO_MASTER_RECALL,      // 禁止师父召回
    NO_REINCARNATION,      // 禁止复活
    NO_HORSE,              // 禁止骑马
    MUSIC,                 // 音乐区
    QUIZ_ZONE,             // 答题区
    NEED_HOLE,             // 需要挖洞
    DARKNESS,              // 黑暗区域
    DAYLIGHT,              // 白天区域
    RUN_HUMAN,             // 允许人物跑步
    RUN_MONSTER,           // 允许怪物跑步
    INC_HP,                // 自动加血
    DEC_HP,                // 自动减血
    INC_GAME_GOLD,         // 自动加金币
    DEC_GAME_GOLD,         // 自动减金币
    INC_GAME_POINT,        // 自动加点数
    DEC_GAME_POINT,        // 自动减点数
    EXP_RATE,              // 经验倍率
    PK_WIN_LEVEL,          // PK胜利得等级
    PK_WIN_EXP,            // PK胜利得经验
    PK_LOST_LEVEL,         // PK失败失等级
    PK_LOST_EXP,           // PK失败失经验
    NO_FIRE_MAGIC          // 禁止火墙魔法
};

// 地图事件
struct MapEvent {
    MapEventType type;
    Point position;
    int range;
    std::string param;
    int value = 0;              // 事件数值（如经验倍率、加血量等）
    DWORD interval = 0;         // 事件间隔时间（毫秒）
    DWORD lastTriggerTime = 0;  // 上次触发时间
};

// 地图标志结构（对应原版MapFlag）
struct MapFlags {
    bool isSafe = false;                    // 安全区
    bool isFightZone = false;              // 战斗区
    bool isFight3Zone = false;             // 行会战区
    bool isDark = false;                   // 黑暗
    bool isDay = false;                    // 白天
    bool isQuiz = false;                   // 答题区
    bool noReconnect = false;              // 禁止重连
    bool needHole = false;                 // 需要挖洞
    bool noRecall = false;                 // 禁止召回
    bool noGuildRecall = false;            // 禁止行会召回
    bool noDearRecall = false;             // 禁止夫妻召回
    bool noMasterRecall = false;           // 禁止师父召回
    bool noRandomMove = false;             // 禁止随机传送
    bool noDrug = false;                   // 禁止使用药品
    bool isMine = false;                   // 矿区
    bool noPositionMove = false;           // 禁止使用移动符
    bool runHuman = false;                 // 允许人物跑步
    bool runMonster = false;               // 允许怪物跑步
    bool incHP = false;                    // 自动加血
    bool decHP = false;                    // 自动减血
    bool incGameGold = false;              // 自动加金币
    bool decGameGold = false;              // 自动减金币
    bool incGamePoint = false;             // 自动加点数
    bool decGamePoint = false;             // 自动减点数
    bool hasMusic = false;                 // 音乐
    bool expRate = false;                  // 经验倍率
    bool pkWinLevel = false;               // PK胜利得等级
    bool pkWinExp = false;                 // PK胜利得经验
    bool pkLostLevel = false;              // PK失败失等级
    bool pkLostExp = false;                // PK失败失经验
    bool noFireMagic = false;              // 禁止火墙魔法
    bool unAllowStdItems = false;          // 禁止使用指定物品

    // 数值参数
    int pkWinLevelValue = 0;               // PK胜利得等级数
    int pkLostLevelValue = 0;              // PK失败失等级数
    int pkWinExpValue = 0;                 // PK胜利得经验数
    int pkLostExpValue = 0;                // PK失败失经验数
    int decHPTime = 0;                     // 减血时间间隔
    int decHPPoint = 0;                    // 一次减血量
    int incHPTime = 0;                     // 加血时间间隔
    int incHPPoint = 0;                    // 一次加血量
    int decGameGoldTime = 0;               // 减金币时间间隔
    int decGameGoldValue = 0;              // 一次减金币数
    int incGameGoldTime = 0;               // 加金币时间间隔
    int incGameGoldValue = 0;              // 一次加金币数
    int decGamePointTime = 0;              // 减点数时间间隔
    int decGamePointValue = 0;             // 一次减点数
    int incGamePointTime = 0;              // 加点数时间间隔
    int incGamePointValue = 0;             // 一次加点数
    int musicID = 0;                       // 音乐ID
    int expRateValue = 100;                // 经验倍率（百分比）

    std::string noReconnectMap;            // 重连地图
    std::string unAllowStdItemsText;       // 禁用物品列表
};

// 环境类（对应delphi的TEnvironment）
class Environment {
public:
    Environment(const std::string& mapName, int width, int height);
    ~Environment();

    // 基本信息
    const std::string& GetMapName() const { return m_mapName; }
    const std::string& GetMapDesc() const { return m_mapDesc; }
    const std::string& GetMainMapName() const { return m_mainMapName; }
    const std::string& GetSubMapName() const { return m_subMapName; }
    int GetWidth() const { return m_width; }
    int GetHeight() const { return m_height; }
    int GetServerIndex() const { return m_serverIndex; }
    int GetRequestLevel() const { return m_requestLevel; }
    int GetMinMap() const { return m_minMap; }
    size_t GetObjectCount() const;

    // 地图标志设置和获取
    void SetMapFlags(const MapFlags& flags);
    const MapFlags& GetMapFlags() const { return m_mapFlags; }
    void SetMapDesc(const std::string& desc) { m_mapDesc = desc; }
    void SetMainMapName(const std::string& name) { m_mainMapName = name; }
    void SetSubMapName(const std::string& name) { m_subMapName = name; }
    void SetServerIndex(int index) { m_serverIndex = index; }
    void SetRequestLevel(int level) { m_requestLevel = level; }
    void SetMinMap(int minMap) { m_minMap = minMap; }
    bool IsMainMap() const { return m_isMainMap; }
    void SetMainMap(bool isMain) { m_isMainMap = isMain; }

    // 地图单元格操作
    bool CanWalk(int x, int y) const;
    bool CanFly(int x, int y) const;
    bool CanStand(int x, int y) const;
    void SetCellAttribute(int x, int y, bool canWalk, bool canFly, bool canStand);

    // 对象管理
    bool AddObject(std::shared_ptr<BaseObject> obj);
    bool RemoveObject(std::shared_ptr<BaseObject> obj);
    bool MoveObject(std::shared_ptr<BaseObject> obj, const Point& newPos);
    std::vector<std::shared_ptr<BaseObject>> GetObjectsAt(const Point& pos) const;
    std::vector<std::shared_ptr<BaseObject>> GetObjectsInRange(const Point& center, int range) const;
    std::shared_ptr<BaseObject> GetObjectAt(const Point& pos, std::function<bool(const std::shared_ptr<BaseObject>&)> filter) const;

    // 玩家相关
    std::vector<std::shared_ptr<PlayObject>> GetPlayersInRange(const Point& center, int range) const;
    size_t GetPlayerCount() const;
    void BroadcastInRange(const Point& center, int range, const std::vector<uint8_t>& packet);

    // 怪物相关
    std::vector<std::shared_ptr<Monster>> GetMonstersInRange(const Point& center, int range) const;
    size_t GetMonsterCount() const;
    bool SpawnMonster(std::shared_ptr<Monster> monster, const Point& pos);
    void ClearMonsters();

    // NPC相关
    std::vector<std::shared_ptr<NPC>> GetNPCsInRange(const Point& center, int range) const;
    bool AddNPC(std::shared_ptr<NPC> npc, const Point& pos);

    // 物品掉落
    bool DropItem(const UserItem& item, const Point& pos, std::shared_ptr<BaseObject> owner = nullptr);
    bool DropGold(DWORD amount, const Point& pos, std::shared_ptr<BaseObject> owner = nullptr);
    void CleanupDroppedItems();

    // 地图事件
    void AddMapEvent(const MapEvent& event);
    void RemoveMapEvent(MapEventType type, const Point& pos);
    bool HasMapEvent(MapEventType type, const Point& pos) const;
    std::vector<MapEvent> GetMapEventsAt(const Point& pos) const;

    // 寻路
    bool FindPath(const Point& start, const Point& end, std::vector<Point>& path, int maxSteps = 100) const;
    Point GetRandomWalkablePoint(const Point& center, int range) const;

    // 运行时更新
    void Run();
    void ProcessObjects();
    void ProcessMapEvents();
    void CheckRespawns();

    // 地图区域检查（对应原版各种地图标志检查）
    bool IsSafeZone(const Point& pos) const;
    bool IsFightZone(const Point& pos) const;
    bool IsFight3Zone(const Point& pos) const;
    bool IsNoReconnectZone(const Point& pos) const;
    bool IsNoRandomZone(const Point& pos) const;
    bool IsNoDrugZone(const Point& pos) const;
    bool IsMineZone(const Point& pos) const;
    bool IsNoPositionMoveZone(const Point& pos) const;
    bool IsNoRecallZone(const Point& pos) const;
    bool IsNoGuildRecallZone(const Point& pos) const;
    bool IsNoDearRecallZone(const Point& pos) const;
    bool IsNoMasterRecallZone(const Point& pos) const;
    bool IsQuizZone(const Point& pos) const;
    bool IsNeedHoleZone(const Point& pos) const;
    bool IsDarknessZone(const Point& pos) const;
    bool IsDaylightZone(const Point& pos) const;
    bool IsRunHumanZone(const Point& pos) const;
    bool IsRunMonsterZone(const Point& pos) const;
    bool IsNoFireMagicZone(const Point& pos) const;

    // 物品限制检查（对应原版AllowStdItems）
    bool AllowStdItems(const std::string& itemName) const;
    bool AllowStdItems(int itemIdx) const;

    // 地图环境信息（对应原版GetEnvirInfo）
    std::string GetEnvironmentInfo() const;

    // 传送门
    struct Portal {
        Point sourcePos;
        std::string targetMap;
        Point targetPos;
        int level;  // 需要的等级
    };
    void AddPortal(const Portal& portal);
    const Portal* GetPortalAt(const Point& pos) const;

    // 刷新点
    struct SpawnPoint {
        Point position;
        int range;
        std::string monsterName;
        int count;
        DWORD respawnTime;
        DWORD lastSpawnTime;
    };
    void AddSpawnPoint(const SpawnPoint& spawn);
    void ProcessSpawnPoints();

    // 调试和统计
    void DumpMapInfo() const;
    struct Statistics {
        size_t totalObjects = 0;
        size_t playerCount = 0;
        size_t monsterCount = 0;
        size_t npcCount = 0;
        size_t itemCount = 0;
        size_t eventCount = 0;
    };
    Statistics GetStatistics() const;

    // 位置验证
    bool IsValidPosition(int x, int y) const;

private:
    // 内部方法
    MapCell* GetCell(int x, int y);
    const MapCell* GetCell(int x, int y) const;
    void UpdateObjectView(std::shared_ptr<BaseObject> obj);
    void NotifyObjectAppear(std::shared_ptr<BaseObject> obj, std::shared_ptr<BaseObject> observer);
    void NotifyObjectDisappear(std::shared_ptr<BaseObject> obj, std::shared_ptr<BaseObject> observer);
    void NotifyObjectMove(std::shared_ptr<BaseObject> obj, const Point& oldPos, std::shared_ptr<BaseObject> observer);

    // A*寻路实现
    struct PathNode {
        Point pos;
        int g, h;
        PathNode* parent;
        int f() const { return g + h; }
    };
    int CalculateHeuristic(const Point& from, const Point& to) const;
    std::vector<Point> ReconstructPath(PathNode* node) const;

private:
    // 基本信息
    std::string m_mapName;
    std::string m_mapDesc;
    std::string m_mainMapName;
    std::string m_subMapName;
    int m_width;
    int m_height;
    int m_serverIndex = 0;
    int m_requestLevel = 0;
    int m_minMap = 0;
    bool m_isMainMap = false;

    // 地图标志
    MapFlags m_mapFlags;

    // 禁用物品列表
    std::vector<std::string> m_unAllowStdItemsList;
    std::vector<int> m_unAllowStdItemsIdxList;
    mutable std::mutex m_unAllowItemsMutex;

    // 地图数据
    std::vector<std::vector<MapCell>> m_cells;

    // 对象列表
    std::unordered_map<uint32_t, std::shared_ptr<BaseObject>> m_objects; // objectId -> object
    mutable std::shared_mutex m_objectsMutex;

    // 地图事件
    std::vector<MapEvent> m_mapEvents;
    mutable std::mutex m_eventsMutex;

    // 传送门
    std::vector<Portal> m_portals;
    mutable std::mutex m_portalsMutex;

    // 刷新点
    std::vector<SpawnPoint> m_spawnPoints;
    std::mutex m_spawnMutex;

    // 运行时状态
    DWORD m_lastRunTime = 0;
    DWORD m_lastCleanupTime = 0;
    DWORD m_lastSpawnCheckTime = 0;
    DWORD m_lastEventProcessTime = 0;

    // 对象计数
    int m_humanCount = 0;
    int m_monsterCount = 0;

    // 配置
    DWORD m_cleanupInterval = 60000; // 1分钟清理一次掉落物品
    DWORD m_spawnCheckInterval = 5000; // 5秒检查一次刷新点
    DWORD m_itemDisappearTime = 180000; // 物品3分钟后消失
    DWORD m_eventProcessInterval = 1000; // 1秒处理一次地图事件

    // 内部方法
    void ProcessMapEffects();
    void ProcessAutoHP();
    void ProcessAutoGameGold();
    void ProcessAutoGamePoint();
    void ProcessPKEffects();
    void UpdateUnAllowItemsList();
    bool CheckMapEventCondition(const MapEvent& event, const Point& pos) const;
};

// 环境管理器（管理所有地图环境）
class EnvironmentManager {
public:
    static EnvironmentManager& Instance();

    // 环境管理
    std::shared_ptr<Environment> CreateEnvironment(const std::string& mapName, int width, int height);
    std::shared_ptr<Environment> GetEnvironment(const std::string& mapName) const;
    void RemoveEnvironment(const std::string& mapName);
    std::vector<std::string> GetAllMapNames() const;

    // 运行时更新
    void RunAll();

public:
    ~EnvironmentManager() = default;

private:
    EnvironmentManager() = default;
    EnvironmentManager(const EnvironmentManager&) = delete;
    EnvironmentManager& operator=(const EnvironmentManager&) = delete;

private:
    std::unordered_map<std::string, std::shared_ptr<Environment>> m_environments;
    mutable std::shared_mutex m_mutex;
};

} // namespace MirServer
#include "../src/Utils/Logger.h"
#include "../src/Utils/ExceptionHandler.h"
#include <iostream>
#include <stdexcept>
#include <fstream>
#include <filesystem>

/**
 * @brief Function that intentionally throws an exception
 */
void ThrowTestException() {
    throw std::runtime_error("This is a test exception");
}

/**
 * @brief Function that might throw a division by zero exception
 */
int DivideNumbers(int a, int b) {
    if (b == 0) {
        throw std::invalid_argument("Division by zero");
    }
    return a / b;
}

/**
 * @brief Function that attempts to open a non-existent file
 */
void OpenNonExistentFile() {
    std::ifstream file("non_existent_file.txt");
    if (!file.is_open()) {
        throw std::runtime_error("Failed to open file: non_existent_file.txt");
    }
}

/**
 * @brief Function that attempts to create a directory with invalid characters
 */
void CreateInvalidDirectory() {
    std::filesystem::create_directories("invalid/*/directory");
}

/**
 * @brief Test the exception logging system
 */
void TestExceptionLogging() {
    // Initialize the logger
    Logger::GetInstance().Initialize("logs/test.log", LogLevel::LOG_LEVEL_DEBUG);

    LOG_INFO("Starting exception logging test");

    // Test 1: Catch a standard exception
    LOG_INFO("Test 1: Catching a standard exception");
    bool success = TRY_CATCH(ThrowTestException);
    LOG_INFO(success ? "Test 1 passed (should not happen)" : "Test 1 passed (exception caught)");

    // Test 2: Catch a division by zero exception with return value
    LOG_INFO("Test 2: Catching a division by zero exception");
    int result = TRY_CATCH_RETURN(-1, DivideNumbers, 10, 0);
    LOG_INFO("Test 2 result: " + std::to_string(result) + " (should be -1)");

    // Test 3: Successful division
    LOG_INFO("Test 3: Successful division");
    result = TRY_CATCH_RETURN(-1, DivideNumbers, 10, 2);
    LOG_INFO("Test 3 result: " + std::to_string(result) + " (should be 5)");

    // Test 4: File operation exception
    LOG_INFO("Test 4: File operation exception");
    success = TRY_CATCH(OpenNonExistentFile);
    LOG_INFO(success ? "Test 4 failed (should not happen)" : "Test 4 passed (exception caught)");

    // Test 5: Invalid directory creation
    LOG_INFO("Test 5: Invalid directory creation");
    success = TRY_CATCH(CreateInvalidDirectory);
    LOG_INFO(success ? "Test 5 failed (should not happen)" : "Test 5 passed (exception caught)");

    LOG_INFO("Exception logging test completed");
}

// Include the example function from ExceptionLoggingExample.cpp
extern void ExceptionLoggingExample();

/**
 * @brief Run all exception logging tests
 */
void RunExceptionLoggingTests() {
    // Run the example
    ExceptionLoggingExample();

    // Run the tests
    TestExceptionLogging();
} 
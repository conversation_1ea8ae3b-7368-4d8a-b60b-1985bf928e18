#include "GroupManager.h"
#include "../Common/Logger.h"
#include "../Common/Utils.h"
#include <iostream>
#include <memory>
#include <cassert>

using namespace MirServer;

// 简单的模拟PlayObject类
class MockPlayObject {
public:
    MockPlayObject(const std::string& name) : m_name(name), m_level(1), m_job(0), m_online(true) {}

    std::string GetCharName() const { return m_name; }
    WORD GetLevel() const { return m_level; }
    int GetJob() const { return m_job; }
    bool IsOnline() const { return m_online; }
    void SetGroupOwner(MockPlayObject* owner) { m_groupOwner = owner; }
    MockPlayObject* GetGroupOwner() const { return m_groupOwner; }
    void SendMessage(const std::string& msg, int color) {
        std::cout << "[" << m_name << "] " << msg << std::endl;
    }
    void SendDefMessage(WORD msgType, WORD param1, WORD param2, WORD param3, WORD param4) {
        std::cout << "[" << m_name << "] DefMessage: " << msgType << std::endl;
    }
    void GainExp(MirServer::DWORD exp) {
        std::cout << "[" << m_name << "] Gained " << exp << " experience" << std::endl;
    }

private:
    std::string m_name;
    WORD m_level;
    int m_job;
    bool m_online;
    MockPlayObject* m_groupOwner = nullptr;
};

// 简单的测试框架
class GroupSystemTest {
public:
    static void RunAllTests() {
        std::cout << "=== 组队系统测试开始 ===" << std::endl;

        TestGroupManagerBasics();
        TestGroupMode();

        std::cout << "=== 组队系统测试完成 ===" << std::endl;
    }

private:
    static void TestGroupManagerBasics() {
        std::cout << "测试组队管理器基础功能..." << std::endl;

        // 初始化组队管理器
        auto& groupManager = GroupManager::GetInstance();
        groupManager.Initialize();

        std::cout << "✓ 组队管理器初始化成功" << std::endl;

        groupManager.Finalize();

        std::cout << "✓ 组队管理器基础功能测试通过" << std::endl;
    }

    static void TestGroupMode() {
        std::cout << "测试组队模式..." << std::endl;

        auto& groupManager = GroupManager::GetInstance();
        groupManager.Initialize();

        auto player = std::make_unique<MockPlayObject>("TestPlayer");

        // 测试设置组队模式
        groupManager.SetGroupMode(reinterpret_cast<PlayObject*>(player.get()), GroupMode::NONE);
        assert(groupManager.GetGroupMode(reinterpret_cast<PlayObject*>(player.get())) == GroupMode::NONE);

        groupManager.SetGroupMode(reinterpret_cast<PlayObject*>(player.get()), GroupMode::ALLOW_GROUP);
        assert(groupManager.GetGroupMode(reinterpret_cast<PlayObject*>(player.get())) == GroupMode::ALLOW_GROUP);

        std::cout << "✓ 组队模式测试通过" << std::endl;

        groupManager.Finalize();
    }
};

int main() {
    try {
        // 运行测试
        GroupSystemTest::RunAllTests();

        std::cout << "所有测试通过！" << std::endl;
        return 0;

    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}

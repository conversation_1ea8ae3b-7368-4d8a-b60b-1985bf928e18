#pragma once

#include "../Common/Types.h"
#include "../Common/GameData.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <atomic>
#include <random>

namespace MirServer {

// 自定义物品名称结构
struct CustomItemName {
    int makeIndex;
    int itemIndex;
    std::string itemName;
};

// 地图物品结构
struct MapItem {
    std::string name;
    WORD looks;
    BYTE aniCount;
    BYTE reserved;
    int count;
    void* ofBaseObject;      // 所属对象
    void* dropBaseObject;    // 掉落对象
    DWORD canPickUpTick;     // 可拾取时间
    UserItem userItem;
    Point position;
    DWORD dropTime;
    std::string owner;       // 拾取保护所有者
    DWORD ownerProtectTime;  // 保护时间
};

// 可见地图物品结构
struct VisibleMapItem {
    WORD ident;
    int param1;
    void* buff;
    MapItem* mapItem;
    int visibleFlag;
    int x, y;
    std::string name;
    WORD looks;
};





// 掉落物品类（地上的物品）
class DroppedItem {
public:
    DroppedItem(const UserItem& item, const Point& pos, DWORD dropTime);
    ~DroppedItem();

    // 基本信息
    uint32_t GetId() const { return m_id; }
    const UserItem& GetItem() const { return m_item; }
    const Point& GetPosition() const { return m_position; }
    DWORD GetDropTime() const { return m_dropTime; }

    // 所有者
    void SetOwner(const std::string& owner, DWORD protectTime);
    const std::string& GetOwner() const { return m_owner; }
    bool CanPickup(const std::string& name) const;

    // 状态
    bool IsGold() const { return m_isGold; }
    DWORD GetGoldAmount() const { return m_goldAmount; }
    void SetGold(DWORD amount) { m_isGold = true; m_goldAmount = amount; }

    // 消失时间
    bool ShouldDisappear(DWORD currentTime) const;

private:
    static std::atomic<uint32_t> s_nextId;
    uint32_t m_id;
    UserItem m_item;
    Point m_position;
    DWORD m_dropTime;
    std::string m_owner;
    DWORD m_ownerProtectTime;
    bool m_isGold = false;
    DWORD m_goldAmount = 0;
};

// 物品管理器类（对应delphi的物品数据管理）
class ItemManager {
public:
    ItemManager();
    ~ItemManager();

    // 初始化
    bool Initialize(const std::string& dataPath);
    void Finalize();

    // 物品数据加载
    bool LoadItemDB(const std::string& fileName);
    bool LoadMonsterDrops(const std::string& fileName);
    bool LoadShopLists(const std::string& fileName);

    // 物品查询
    const StdItem* GetStdItem(WORD itemIdx) const;
    const StdItem* GetStdItemByName(const std::string& name) const;
    std::vector<const StdItem*> GetItemsByType(ItemType type) const;
    bool ItemExists(WORD itemIdx) const;

    // 创建物品
    UserItem CreateItem(WORD itemIdx) const;
    UserItem CreateItemByName(const std::string& name) const;
    bool CreateRandomItem(ItemType type, UserItem& item) const;
    bool UpgradeItem(UserItem& item, int upgradeLevel) const;

    // 物品属性
    int GetItemWeight(const UserItem& item) const;
    int GetItemValue(const UserItem& item) const;
    bool CanEquipItem(const UserItem& item, const Ability& ability, JobType job, GenderType gender = GenderType::MALE) const;
    bool IsStackable(WORD itemIdx) const;
    int GetMaxStack(WORD itemIdx) const;

    // 掉落系统
    struct MonsterDrop {
        WORD itemIdx;
        float dropRate;         // 掉落率 0.0001 = 0.01%
        int minCount;
        int maxCount;
        int minLevel;           // 最小怪物等级
        int questFlag;          // 任务标记
    };
    std::vector<UserItem> GenerateMonsterDrops(const std::string& monsterName, int monsterLevel, float luckFactor = 1.0f);
    bool AddMonsterDrop(const std::string& monsterName, const MonsterDrop& drop);

    // 商店系统
    struct ShopItem {
        WORD itemIdx;
        int stock;              // -1 表示无限
        float priceRate;        // 价格倍率
        int refreshTime;        // 刷新时间（秒）
    };
    std::vector<ShopItem> GetShopItems(const std::string& shopName) const;
    bool AddShopItem(const std::string& shopName, const ShopItem& item);

    // 物品生成
    UserItem GenerateRandomEquipment(ItemType type, int level) const;
    void AddRandomStats(UserItem& item, int bonusCount) const;
    void RepairItem(UserItem& item) const;

    // 物品随机属性生成（对应原项目ItmUnit.pas）
    void RandomUpgradeWeapon(UserItem& item) const;
    void RandomUpgradeDress(UserItem& item) const;
    void RandomUpgradeHelmet(UserItem& item) const;
    void RandomUpgrade19(UserItem& item) const;      // 项链类型19
    void RandomUpgrade22(UserItem& item) const;      // 戒指类型22
    void RandomUpgrade23(UserItem& item) const;      // 戒指类型23
    void RandomUpgrade26(UserItem& item) const;      // 手镯类型26
    void RandomUpgrade202124(UserItem& item) const;  // 项链类型20,21,24
    void UnknowHelmet(UserItem& item) const;         // 未知头盔
    void UnknowRing(UserItem& item) const;           // 未知戒指
    void UnknowNecklace(UserItem& item) const;       // 未知项链

    // 物品属性计算
    void GetItemAddValue(UserItem& item, StdItem& stdItem) const;

    // ==================== 物品强化系统 ====================

    // 强化结果枚举
    enum class UpgradeResult {
        SUCCESS,                // 强化成功
        FAILED,                 // 强化失败
        ITEM_DESTROYED,         // 物品被摧毁
        INSUFFICIENT_MATERIALS, // 材料不足
        INSUFFICIENT_GOLD,      // 金币不足
        MAX_LEVEL_REACHED,      // 已达最大强化等级
        INVALID_ITEM,           // 无效物品
        INVALID_MATERIAL        // 无效材料
    };

    // 强化材料类型
    enum class UpgradeMaterial {
        NONE = 0,
        BLACK_IRON = 1,         // 黑铁矿石
        SILVER_ORE = 2,         // 银矿石
        GOLD_ORE = 3,           // 金矿石
        DIAMOND = 4,            // 钻石
        BLESSING_OIL = 5,       // 祝福油
        SOUL_GEM = 6,           // 灵魂宝石
        MEMORY_HELMET = 7,      // 记忆头盔
        MEMORY_NECKLACE = 8,    // 记忆项链
        MEMORY_BRACELET = 9,    // 记忆手镯
        MEMORY_RING = 10        // 记忆戒指
    };

    // 强化配置结构
    struct UpgradeConfig {
        int maxUpgradeLevel = 10;           // 最大强化等级
        int baseSuccessRate = 80;           // 基础成功率(%)
        int successRateDecrement = 8;       // 每级成功率递减(%)
        int minSuccessRate = 5;             // 最小成功率(%)
        int baseCost = 1000;                // 基础强化费用
        float costMultiplier = 1.5f;        // 费用倍数
        bool canDestroy = true;             // 是否可能摧毁物品
        int destroyStartLevel = 7;          // 开始可能摧毁的等级
        int destroyRate = 20;               // 摧毁概率(%)
    };

    // 强化系统方法
    UpgradeResult UpgradeItemWithMaterials(UserItem& item,
                                         const std::vector<UserItem>& materials,
                                         DWORD& cost) const;

    int CalculateUpgradeSuccessRate(const UserItem& item,
                                  const std::vector<UserItem>& materials) const;

    DWORD CalculateUpgradeCost(const UserItem& item) const;

    bool CanUpgradeItem(const UserItem& item) const;

    int GetItemUpgradeLevel(const UserItem& item) const;

    void SetItemUpgradeLevel(UserItem& item, int level) const;

    bool IsUpgradeMaterial(WORD itemIdx) const;

    UpgradeMaterial GetMaterialType(WORD itemIdx) const;

    int GetMaterialBonus(UpgradeMaterial material, int count) const;

    void ApplyUpgradeEffect(UserItem& item, int newLevel) const;

    void ApplyUpgradeFailure(UserItem& item, bool destroyed) const;

    // 强化配置
    void SetUpgradeConfig(const UpgradeConfig& config);
    const UpgradeConfig& GetUpgradeConfig() const;

    // 强化系统辅助方法（用于测试）
    bool ValidateUpgradeMaterials(const std::vector<UserItem>& materials) const;
    bool ShouldDestroyOnFailure(int currentLevel) const;
    void UpdateItemName(UserItem& item) const;

    // 自定义物品名称管理
    bool LoadCustomItemName();
    bool SaveCustomItemName();
    bool AddCustomItemName(int makeIndex, int itemIndex, const std::string& itemName);
    bool DelCustomItemName(int makeIndex, int itemIndex);
    std::string GetCustomItemName(int makeIndex, int itemIndex) const;

    // 物品验证
    bool ValidateItem(const UserItem& item) const;
    bool IsQuestItem(WORD itemIdx) const;
    bool IsValuableItem(WORD itemIdx) const;

    // 统计信息
    struct Statistics {
        size_t totalItems = 0;
        size_t weaponCount = 0;
        size_t armorCount = 0;
        size_t accessoryCount = 0;
        size_t consumableCount = 0;
        size_t questItemCount = 0;
        size_t totalDropRules = 0;
        size_t totalShopItems = 0;
    };
    Statistics GetStatistics() const;

    // 调试
    void DumpItemInfo(WORD itemIdx) const;
    void ExportItemList(const std::string& fileName) const;

private:
    // 内部方法
    bool ParseItemLine(const std::string& line, StdItem& item);
    bool ParseDropLine(const std::string& line, std::string& monsterName, MonsterDrop& drop);
    bool ParseShopLine(const std::string& line, std::string& shopName, ShopItem& item);
    ItemType GetItemTypeFromStdMode(int stdMode) const;
    void InitializeRandomGenerators();
    int GetRandomStatValue(int baseValue, float variance) const;

    // 生成物品属性
    void GenerateWeaponStats(UserItem& item, const StdItem* stdItem, int level) const;
    void GenerateArmorStats(UserItem& item, const StdItem* stdItem, int level) const;
    void GenerateAccessoryStats(UserItem& item, const StdItem* stdItem, int level) const;

    // 随机数生成辅助方法
    int GetRandomRange(int count, int rate) const;

    // 装备条件检查辅助方法
    bool CheckJobRestriction(const StdItem* stdItem, JobType job) const;
    bool CheckGenderRestriction(const StdItem* stdItem, GenderType gender) const;

    // 特殊物品叠加检查
    bool IsSpecialStackableItem(const StdItem* stdItem) const;

private:
    std::string m_dataPath;

    // 物品数据
    std::unordered_map<WORD, std::unique_ptr<StdItem>> m_items;
    std::unordered_map<std::string, WORD> m_nameToIdx;
    mutable std::mutex m_itemsMutex;

    // 掉落数据
    std::unordered_map<std::string, std::vector<MonsterDrop>> m_monsterDrops;
    mutable std::mutex m_dropsMutex;

    // 商店数据
    std::unordered_map<std::string, std::vector<ShopItem>> m_shopLists;
    mutable std::mutex m_shopsMutex;

    // 自定义物品名称数据
    std::vector<CustomItemName> m_customItemNames;
    mutable std::mutex m_customNamesMutex;

    // 随机数生成器
    mutable std::mt19937 m_randomEngine;
    mutable std::uniform_real_distribution<float> m_floatDist{0.0f, 1.0f};

    // 运行状态
    bool m_initialized = false;

    // 配置
    float m_baseDropRate = 1.0f;       // 基础掉落率倍数
    float m_goldDropRate = 0.8f;       // 金币掉落率
    int m_maxDropItems = 10;           // 单次最大掉落数量

    // 强化系统配置
    UpgradeConfig m_upgradeConfig;

    // 强化材料映射表
    std::unordered_map<WORD, UpgradeMaterial> m_materialMap;

    // 强化系统辅助方法
    void InitializeUpgradeSystem();
    void LoadUpgradeMaterials();
    int CalculateBasicSuccessRate(int currentLevel) const;
    int CalculateMaterialBonus(const std::vector<UserItem>& materials) const;
};

// 全局物品管理器实例
extern std::unique_ptr<ItemManager> g_ItemManager;

} // namespace MirServer
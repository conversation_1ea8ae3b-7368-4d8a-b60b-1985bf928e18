#pragma once

#include "BaseObject.h"
#include <functional>
#include <unordered_map>
#include <memory>

namespace MirServer {

// 前向声明
class PlayObject;
class ScriptEngine;
struct NPCScript;

// NPC类型
enum class NPCType : BYTE {
    NORMAL = 0,         // 普通NPC
    MERCHANT = 1,       // 商人
    GUARD = 2,          // 守卫
    TRAINER = 3,        // 技能训练师
    WAREHOUSE = 4,      // 仓库管理员
    GUILD = 5,          // 行会管理员
    QUEST = 6           // 任务NPC
};

// NPC对话结构
struct NPCDialog {
    std::string text;                           // 对话文本
    std::vector<std::string> options;           // 选项列表
    std::unordered_map<int, int> optionGotos;  // 选项跳转
    std::string script;                         // 脚本命令
};

// NPC基类（对应delphi的TNormNpc）
class NPC : public BaseObject {
public:
    NPC();
    virtual ~NPC();

    // 重写基类方法
    virtual ObjectType GetObjectType() const override { return ObjectType::NPC; }
    virtual void Initialize() override;
    virtual void Finalize() override;
    virtual void Run() override;

    // NPC基本属性
    NPCType GetNPCType() const { return m_npcType; }
    void SetNPCType(NPCType type) { m_npcType = type; }

    // 对话相关
    virtual void OnClick(PlayObject* player);
    virtual void OnPlayerTalk(PlayObject* player);
    virtual void ProcessDialog(PlayObject* player, int dialogId, int optionId = -1);
    void SendDialogToPlayer(PlayObject* player, int dialogId);

    // 对话管理
    void AddDialog(int id, const NPCDialog& dialog);
    const NPCDialog* GetDialog(int id) const;
    void ClearDialogs();

    // 脚本相关
    void SetScriptFile(const std::string& filename) { m_scriptFile = filename; }
    const std::string& GetScriptFile() const { return m_scriptFile; }
    virtual void LoadScript();

    // 脚本执行引擎
    bool ExecuteNPCScript(PlayObject* player, const std::string& label = "main");
    bool HasScript() const { return m_script != nullptr; }
    const NPCScript* GetScript() const { return m_script.get(); }

    // 脚本对话处理
    virtual void OnScriptDialog(PlayObject* player, int optionIndex = -1);

    // 可见性（NPC通常对所有人可见）
    virtual bool IsVisible() const override { return true; }

    // 商店相关（由Merchant子类实现）
    virtual bool HasShop() const { return false; }
    virtual void OpenShop(PlayObject* player) {}

    // 任务相关
    virtual bool HasQuest() const { return false; }
    virtual void CheckQuest(PlayObject* player) {}

    // 服务相关
    virtual bool CanProvideService() const { return false; }
    virtual void ProvideService(PlayObject* player, const std::string& service) {}

    // AI相关
    void SetCanMove(bool canMove) { m_canMove = canMove; }
    bool CanMove() const { return m_canMove; }
    void SetMoveInterval(DWORD interval) { m_moveInterval = interval; }

    // 说话
    void Say(const std::string& msg);
    void SayTo(PlayObject* player, const std::string& msg);

protected:
    // 内部方法
    virtual void ProcessAI();
    virtual void RandomMove();
    virtual bool CheckPlayerInRange(PlayObject* player, int range = 3) const;

    // 脚本执行
    virtual void ExecuteScript(PlayObject* player, const std::string& script);

    // 默认对话加载
    void LoadDefaultDialog();

    // 对话替换变量
    std::string ReplaceDialogVariables(const std::string& text, PlayObject* player) const;

private:
    NPCType m_npcType = NPCType::NORMAL;        // NPC类型
    std::string m_scriptFile;                   // 脚本文件
    std::unordered_map<int, NPCDialog> m_dialogs; // 对话内容

    // 脚本执行引擎
    std::unique_ptr<NPCScript> m_script;        // NPC脚本
    static std::shared_ptr<ScriptEngine> s_scriptEngine; // 全局脚本执行引擎

    // AI相关
    bool m_canMove = false;                     // 是否可以移动
    DWORD m_moveInterval = 10000;               // 移动间隔（毫秒）
    DWORD m_lastMoveTime = 0;                   // 上次移动时间

    // 当前对话状态
    std::unordered_map<PlayObject*, int> m_playerDialogStates; // 玩家对话状态
    std::unordered_map<PlayObject*, std::string> m_playerScriptStates; // 玩家脚本状态
};

// 商人NPC类（对应delphi的TMerchant）
class Merchant : public NPC {
public:
    struct ShopItem {
        WORD itemIndex;         // 物品索引
        std::string itemName;   // 物品名称
        DWORD price;           // 价格
        int stock;             // 库存（-1表示无限）
    };

    Merchant();
    virtual ~Merchant();

    // 重写方法
    virtual void Initialize() override;
    virtual bool HasShop() const override { return true; }
    virtual void OpenShop(PlayObject* player) override;

    // 商品管理
    void AddShopItem(const ShopItem& item);
    bool AddShopItem(WORD itemIndex, int price = -1);  // 重载版本
    void RemoveShopItem(WORD itemIndex);
    void UpdateItemPrice(WORD itemIndex, DWORD newPrice);
    void UpdateItemStock(WORD itemIndex, int stock);
    const std::vector<ShopItem>& GetShopItems() const { return m_shopItems; }

    // 价格设置
    void SetBuyRate(int rate) { m_buyPriceRate = rate / 100.0f; }
    void SetSellRate(int rate) { m_sellPriceRate = rate / 100.0f; }

    // 交易
    bool SellItemToPlayer(PlayObject* player, WORD itemIndex, int count = 1);
    bool BuyItemFromPlayer(PlayObject* player, WORD makeIndex);
    DWORD GetBuyPrice(const UserItem& item) const;

    // 修理服务
    void SetCanRepair(bool canRepair) { m_canRepair = canRepair; }
    bool CanRepair() const { return m_canRepair; }
    DWORD GetRepairCost(const UserItem& item) const;
    bool RepairItem(PlayObject* player, WORD makeIndex);

    // 特殊商店类型
    void SetSpecialShopType(int type) { m_specialShopType = type; }
    int GetSpecialShopType() const { return m_specialShopType; }

protected:
    virtual void LoadShopData();

private:
    std::vector<ShopItem> m_shopItems;          // 商品列表
    bool m_canRepair = false;                   // 是否可以修理
    int m_specialShopType = 0;                  // 特殊商店类型

    // 价格系数
    float m_buyPriceRate = 1.0f;                // 购买价格系数
    float m_sellPriceRate = 0.5f;               // 出售价格系数
};

// 守卫NPC类
class Guard : public NPC {
public:
    Guard();
    virtual ~Guard();

    // 重写方法
    virtual void Initialize() override;
    virtual void Run() override;

    // 守卫功能
    void SetGuardRange(int range) { m_guardRange = range; }
    int GetGuardRange() const { return m_guardRange; }
    void SetAttackCriminals(bool attack) { m_attackCriminals = attack; }

protected:
    virtual void ProcessAI() override;
    void CheckAndAttackCriminals();

private:
    int m_guardRange = 10;                      // 守卫范围
    bool m_attackCriminals = true;              // 是否攻击红名玩家
    BaseObject* m_currentTarget = nullptr;      // 当前目标
};

// 智能指针类型定义
using NPCPtr = std::shared_ptr<NPC>;
using MerchantPtr = std::shared_ptr<Merchant>;
using GuardPtr = std::shared_ptr<Guard>;

} // namespace MirServer
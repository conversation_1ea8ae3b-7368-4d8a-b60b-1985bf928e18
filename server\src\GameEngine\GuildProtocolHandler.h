#pragma once

#include "../Common/Types.h"
#include "../Protocol/PacketTypes.h"
#include <string>

namespace MirServer {

// 前向声明
class PlayObject;
class Guild;

// 行会协议处理器类
class GuildProtocolHandler {
public:
    // 处理行会相关协议
    static bool HandleGuildProtocol(PlayObject* player, Protocol::PacketType packetType, 
                                   const std::string& data);

    // 具体协议处理方法
    static void HandleOpenGuildDlg(PlayObject* player);
    static void HandleGuildHome(PlayObject* player);
    static void HandleGuildMemberList(PlayObject* player);
    static void HandleGuildAddMember(PlayObject* player, const std::string& targetName);
    static void HandleGuildDelMember(PlayObject* player, const std::string& targetName);
    static void HandleGuildUpdateNotice(PlayObject* player, const std::string& notice);
    static void HandleGuildUpdateRankInfo(PlayObject* player, const std::string& targetName, 
                                         const std::string& rankName);
    static void HandleGuildMakeAlly(PlayObject* player, const std::string& guildName);
    static void HandleGuildBreakAlly(PlayObject* player, const std::string& guildName);
    static void HandleGuildWar(PlayObject* player, const std::string& guildName);
    static void HandleGuildPeace(PlayObject* player, const std::string& guildName);
    static void HandleGuildMsg(PlayObject* player, const std::string& message);

    // 发送行会信息到客户端
    static void SendGuildInfo(PlayObject* player);
    static void SendGuildMemberList(PlayObject* player);
    static void SendGuildNotices(PlayObject* player);
    static void SendGuildMessage(PlayObject* player, const std::string& message);

    // 权限检查
    static bool CanManageMembers(PlayObject* player);
    static bool CanUpdateNotice(PlayObject* player);
    static bool CanManageAlliance(PlayObject* player);
    static bool CanDeclareWar(PlayObject* player);

private:
    // 内部辅助方法
    static Guild* GetPlayerGuild(PlayObject* player);
    static bool IsGuildChief(PlayObject* player);
    static bool IsGuildViceChief(PlayObject* player);
    static bool HasGuildRank(PlayObject* player, BYTE minRank);
    
    // 消息发送辅助方法
    static void SendGuildResponse(PlayObject* player, Protocol::PacketType responseType, 
                                 bool success = true, const std::string& message = "");
    static std::string FormatGuildMemberInfo(const Guild* guild);
    static std::string FormatGuildNotices(const Guild* guild);
};

} // namespace MirServer

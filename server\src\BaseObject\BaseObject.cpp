// BaseObject.cpp - 基础对象实现
#include "BaseObject.h"
#include "../Common/Types.h"
#include "../Common/Logger.h"
#include "../Protocol/PacketTypes.h"
#include "../GameEngine/GameEngine.h"
#include "../GameEngine/MapManager.h"
#include "../GameEngine/Environment.h"
#include <algorithm>
#include <cmath>
#include <atomic>
#include <memory>

namespace MirServer {
using namespace Protocol;  // 添加Protocol命名空间

BaseObject::BaseObject() {
    // 初始化基本属性
    m_charName = "";
    m_currentPos = Point(0, 0);
    m_direction = DirectionType::DOWN;
    m_environment = nullptr;
    m_mapName = "";

    // 初始化外观属性
    m_appr = 0;
    m_raceServer = 0;
    m_raceImg = 0;
    m_light = 0;
    m_nameColor = 0;
    m_viewRange = 9;

    // 初始化状态属性
    m_state = ObjectState::NORMAL;
    m_hp = 100;
    m_maxHP = 100;
    m_mp = 100;
    m_maxMP = 100;
    m_level = 1;
    m_permission = 0;

    // 初始化战斗属性
    m_dc = 0;
    m_mc = 0;
    m_sc = 0;
    m_ac = 0;
    m_mac = 0;
    m_targetCreature = nullptr;

    // 初始化回城信息
    m_homePos = Point(0, 0);
    m_homeMap = "";

    // 初始化其他
    m_targetCreature = nullptr;
    m_lastMoveTime = GetCurrentTime();
    m_lastAttackTime = 0;

    // 初始化新增成员
    static std::atomic<uint32_t> s_nextId{1};
    m_objectId = s_nextId++;
    m_job = JobType::WARRIOR;
    m_gender = GenderType::MALE;
    m_isDead = false;
    m_visible = true;
    m_canBlock = true;  // 默认情况下对象会阻挡其他对象
}

BaseObject::~BaseObject() {
    Finalize();
}

bool BaseObject::TurnTo(DirectionType dir) {
    if (m_direction != dir) {
        m_direction = dir;
        OnDirectionChanged();
        StatusChanged();
        return true;
    }
    return false;
}

void BaseObject::Die() {
    if (m_state != ObjectState::DEAD) {
        m_state = ObjectState::DEAD;
        m_hp = 0;
        m_targetCreature = nullptr;
        OnStateChanged();
        StatusChanged();

        // 发送死亡消息
        SendDefMessage(Protocol::SM_NOWDEATH, 0, 0, 0, 0);
    }
}

void BaseObject::Revive() {
    if (m_state == ObjectState::DEAD || m_state == ObjectState::GHOST) {
        m_state = ObjectState::NORMAL;
        m_hp = m_maxHP / 2; // 复活时恢复一半生命值
        OnStateChanged();
        OnHPChanged();
        StatusChanged();

        // 发送复活消息
        SendDefMessage(Protocol::SM_ALIVE, 0, 0, 0, 0);
    }
}

bool BaseObject::CanMove(const Point& target) const {
    // 检查是否死亡或无法移动的状态
    if (IsDead() || m_state == ObjectState::PARALYSIS || m_state == ObjectState::STONE) {
        return false;
    }

    // 检查目标点是否在合理范围内（防止瞬移作弊）
    double distance = GetDistance(m_currentPos, target);
    if (distance > 50) { // 最大移动距离限制
        return false;
    }

    // TODO: 检查地图是否可通行
    // if (m_environment && !m_environment->CanWalk(target.x, target.y)) {
    //     return false;
    // }

    return true;
}

bool BaseObject::SpaceMove(const std::string& mapName, int x, int y) {
    // 保存原位置
    Point oldPos = m_currentPos;
    std::string oldMap = m_mapName;

    // 获取游戏引擎
    auto& gameEngine = GameEngine::GetInstance();
    auto* mapManager = gameEngine.GetMapManager();
    auto* envManager = gameEngine.GetEnvironmentManager();

    if (!mapManager || !envManager) {
        Logger::Error("MapManager or EnvironmentManager not available for SpaceMove");
        return false;
    }

    // 验证目标地图是否存在
    const MapInfo* mapInfo = mapManager->GetMapInfo(mapName);
    if (!mapInfo) {
        Logger::Error("Target map not found for SpaceMove: " + mapName);
        return false;
    }

    // 验证目标坐标是否有效
    if (!mapManager->IsValidPosition(mapName, x, y)) {
        Logger::Warning("Invalid position for SpaceMove: " + mapName +
                       " (" + std::to_string(x) + "," + std::to_string(y) + ")");
        return false;
    }

    // 检查目标位置是否可以行走
    if (!mapManager->CanWalk(mapName, x, y)) {
        Logger::Warning("Cannot walk to SpaceMove position: " + mapName +
                       " (" + std::to_string(x) + "," + std::to_string(y) + ")");
        return false;
    }

    // 如果地图改变了，需要切换环境
    if (oldMap != mapName) {
        // 从旧环境移除
        if (!oldMap.empty()) {
            auto oldEnv = envManager->GetEnvironment(oldMap);
            if (oldEnv) {
                oldEnv->RemoveObject(shared_from_this());
                Logger::Debug("Removed object " + GetCharName() + " from map: " + oldMap);
            }
        }

        // 添加到新环境
        auto newEnv = envManager->GetEnvironment(mapName);
        if (!newEnv) {
            // 如果环境不存在，创建它（需要从地图管理器获取地图尺寸）
            const MapInfo* mapInfo = mapManager->GetMapInfo(mapName);
            if (mapInfo) {
                newEnv = envManager->CreateEnvironment(mapName, mapInfo->width, mapInfo->height);
            }
        }

        if (newEnv) {
            // 更新位置
            m_currentPos = Point(x, y);
            m_mapName = mapName;

            // 添加到新环境
            if (newEnv->AddObject(shared_from_this())) {
                Logger::Debug("Added object " + GetCharName() + " to map: " + mapName +
                            " at (" + std::to_string(x) + "," + std::to_string(y) + ")");

                OnPositionChanged();

                // 发送瞬移消息
                SendDefMessage(Protocol::SM_SPACEMOVE_SHOW, 0, x, y, 0);

                return true;
            } else {
                // 回滚位置
                m_currentPos = oldPos;
                m_mapName = oldMap;

                // 重新添加到原环境
                auto originalEnv = envManager->GetEnvironment(oldMap);
                if (originalEnv) {
                    originalEnv->AddObject(shared_from_this());
                }

                Logger::Error("Failed to add object to new environment: " + mapName);
                return false;
            }
        } else {
            Logger::Error("Failed to create/get environment for map: " + mapName);
            return false;
        }
    } else {
        // 同一地图内移动
        auto env = envManager->GetEnvironment(mapName);
        if (env) {
            Point newPos(x, y);
            if (env->MoveObject(shared_from_this(), newPos)) {
                m_currentPos = newPos;

                OnPositionChanged();

                // 发送瞬移消息
                SendDefMessage(Protocol::SM_SPACEMOVE_SHOW, 0, x, y, 0);

                Logger::Debug("Moved object " + GetCharName() + " within map: " + mapName +
                            " to (" + std::to_string(x) + "," + std::to_string(y) + ")");
                return true;
            } else {
                Logger::Error("Failed to move object within map: " + mapName);
                return false;
            }
        } else {
            Logger::Error("Environment not found for map: " + mapName);
            return false;
        }
    }

    return false;
}

Point BaseObject::GetFrontPosition() const {
    return GetNextPosition(m_direction);
}

Point BaseObject::GetNextPosition(DirectionType dir) const {
    Point offset = GetDirectionOffset(dir);
    return Point(m_currentPos.x + offset.x, m_currentPos.y + offset.y);
}

void BaseObject::GetAroundPoints(std::vector<Point>& points, int range) const {
    points.clear();

    for (int x = -range; x <= range; x++) {
        for (int y = -range; y <= range; y++) {
            if (x == 0 && y == 0) continue; // 跳过中心点

            Point p(m_currentPos.x + x, m_currentPos.y + y);
            points.push_back(p);
        }
    }
}

bool BaseObject::CanSee(const BaseObject* target) const {
    if (!target || target == this) return false;

    // 检查是否在同一地图
    if (m_mapName != target->GetMapName()) {
        return false;
    }

    // 检查是否隐身
    if (!target->IsVisible() && !IsGM()) {
        return false;
    }

    // 检查视野范围
    return IsInViewRange(target->GetCurrentPos());
}

bool BaseObject::IsInViewRange(const Point& pos) const {
    int dx = std::abs(pos.x - m_currentPos.x);
    int dy = std::abs(pos.y - m_currentPos.y);

    // 使用切比雪夫距离（棋盘距离）
    int distance = std::max(dx, dy);
    return distance <= m_viewRange;
}

void BaseObject::GetViewObjects(std::vector<BaseObject*>& objects) const {
    objects.clear();

    // TODO: 从环境中获取视野内的对象
    // if (m_environment) {
    //     m_environment->GetObjectsInRange(m_currentPos, m_viewRange, objects);
    // }
}

DWORD BaseObject::GetFeature() const {
    // 特征值编码格式（对应delphi版本）：
    // 低16位：外观ID
    // 高16位：特殊标记（性别、职业等）
    DWORD feature = m_appr;

    // 根据对象类型添加特殊标记
    ObjectType objType = GetObjectType();
    if (objType == ObjectType::Player) {
        // TODO: 添加性别、职业等信息
    }

    return feature;
}

void BaseObject::SetFeature(DWORD feature) {
    m_appr = static_cast<WORD>(feature & 0xFFFF);
    FeatureChanged();
}

void BaseObject::FeatureChanged() {
    // 通知周围玩家外观改变
    SendDefMessage(Protocol::SM_FEATURECHANGED, GetFeature(), 0, 0, 0);
}

void BaseObject::StatusChanged() {
    // 通知状态改变
    SendDefMessage(Protocol::SM_MYSTATUS, 0, 0, 0, 0);
}

BYTE BaseObject::GetCharColor() const {
    // 根据状态返回名称颜色
    if (m_state == ObjectState::POISON) {
        return 2; // 绿色（中毒）
    } else if (m_state == ObjectState::STONE) {
        return 7; // 灰色（石化）
    } else if (IsGM()) {
        return 10; // 金色（GM）
    }

    return m_nameColor;
}

void BaseObject::AddCustomData(const std::string& key, const std::string& value) {
    m_customData[key] = value;
}

std::string BaseObject::GetCustomData(const std::string& key) const {
    auto it = m_customData.find(key);
    return (it != m_customData.end()) ? it->second : "";
}

void BaseObject::SetRecallPos(const Point& pos, const std::string& mapName) {
    m_homePos = pos;
    m_homeMap = mapName;
}

// 物理伤害计算
int BaseObject::GetHitStruckDamage(BaseObject* attacker, int damage) {
    // 计算防御力
    int defense = m_WAbil.AC.min + (rand() % (m_WAbil.AC.max - m_WAbil.AC.min + 1));
    damage = std::max(0, damage - defense);

    // 如果是不死系生物且攻击者有对不死系加成
    if (m_lifeAttrib == LA_UNDEAD && attacker) {
        // 这里可以添加对不死系的额外伤害
        // damage += attacker->m_addAbil.btUndeadDamage;
    }

    // 魔法盾防护
    if (damage > 0 && m_boAbilMagBubbleDefence) {
        damage = static_cast<int>((damage / 100.0) * (m_btMagBubbleDefenceLevel + 2) * 8.0);
        DamageBubbleDefence(damage);
    }

    return damage;
}

// 魔法伤害计算
int BaseObject::GetMagStruckDamage(BaseObject* attacker, int damage) {
    // 计算魔法防御力
    int magDefense = m_WAbil.MAC.min + (rand() % (m_WAbil.MAC.max - m_WAbil.MAC.min + 1));
    damage = std::max(0, damage - magDefense);

    // 如果是不死系生物
    if (m_lifeAttrib == LA_UNDEAD && attacker) {
        // 这里可以添加对不死系的额外魔法伤害
        // damage += attacker->m_addAbil.btUndeadMagDamage;
    }

    // 魔法盾防护
    if (damage > 0 && m_boAbilMagBubbleDefence) {
        damage = static_cast<int>((damage / 100.0) * (m_btMagBubbleDefenceLevel + 2) * 8.0);
        DamageBubbleDefence(damage);
    }

    return damage;
}

// 受到伤害处理
void BaseObject::StruckDamage(int damage) {
    if (damage <= 0) return;

    int weaponDamage = (rand() % 10) + 5;

    // 如果有腐蚀毒药效果，增加装备损坏和伤害
    if (m_wStatusTimeArr[POISON_DAMAGEARMOR] > 0) {
        weaponDamage = static_cast<int>(weaponDamage * 1.2);
        damage = static_cast<int>(damage * 1.2);
    }

    bool equipmentBroken = false;

    // 损坏衣服
    if (m_useItems[U_DRESS].wIndex > 0) {
        int oldDura = m_useItems[U_DRESS].Dura / 1000;
        m_useItems[U_DRESS].Dura -= weaponDamage;

        if (m_useItems[U_DRESS].Dura <= 0) {
            // 装备损坏
            m_useItems[U_DRESS].wIndex = 0;
            m_useItems[U_DRESS].Dura = 0;
            equipmentBroken = true;
            FeatureChanged();
        }

        if (oldDura != m_useItems[U_DRESS].Dura / 1000) {
            // 发送耐久度变化消息
            SendMsg(this, RM_DURACHANGE, U_DRESS, m_useItems[U_DRESS].Dura,
                   m_useItems[U_DRESS].DuraMax, 0, "");
        }
    }

    // 随机损坏其他装备
    for (int i = 0; i < U_MAXUSEITEM; ++i) {
        if (m_useItems[i].wIndex > 0 && (rand() % 8) == 0) {
            int oldDura = m_useItems[i].Dura / 1000;
            m_useItems[i].Dura -= weaponDamage;

            if (m_useItems[i].Dura <= 0) {
                m_useItems[i].wIndex = 0;
                m_useItems[i].Dura = 0;
                equipmentBroken = true;
                FeatureChanged();
            }

            if (oldDura != m_useItems[i].Dura / 1000) {
                SendMsg(this, RM_DURACHANGE, i, m_useItems[i].Dura,
                       m_useItems[i].DuraMax, 0, "");
            }
        }
    }

    if (equipmentBroken) {
        RecalcAbilitys();
    }

    // 扣除生命值
    DamageHealth(damage);
}

// 攻击力计算
int BaseObject::GetAttackPower(int basePower, int powerRange) {
    if (powerRange <= 0) return basePower;
    return basePower + (rand() % powerRange);
}

// 扣除生命值
void BaseObject::DamageHealth(int damage) {
    if (damage <= 0) return;

    m_WAbil.HP = std::max(0, m_WAbil.HP - damage);

    // 发送生命值变化消息
    HealthSpellChanged();

    // 如果生命值为0，死亡
    if (m_WAbil.HP <= 0) {
        Die();
    }
}

// 扣除魔法值
void BaseObject::DamageSpell(int spellPoint) {
    if (spellPoint <= 0) return;

    m_WAbil.MP = std::max(0, m_WAbil.MP - spellPoint);

    // 发送魔法值变化消息
    HealthSpellChanged();
}

// 武器损坏
void BaseObject::DoDamageWeapon(int weaponDamage) {
    if (weaponDamage <= 0 || m_useItems[U_WEAPON].wIndex == 0) return;

    int oldDura = m_useItems[U_WEAPON].Dura / 1000;
    m_useItems[U_WEAPON].Dura -= weaponDamage;

    if (m_useItems[U_WEAPON].Dura <= 0) {
        // 武器损坏
        m_useItems[U_WEAPON].wIndex = 0;
        m_useItems[U_WEAPON].Dura = 0;
        FeatureChanged();
        RecalcAbilitys();

        // 发送武器损坏消息
        SendDefMessage(SM_BREAKWEAPON, 0, 0, 0, 0);
    }

    if (oldDura != m_useItems[U_WEAPON].Dura / 1000) {
        SendMsg(this, RM_DURACHANGE, U_WEAPON, m_useItems[U_WEAPON].Dura,
               m_useItems[U_WEAPON].DuraMax, 0, "");
    }
}

// 主攻击方法
bool BaseObject::_Attack(WORD& hitMode, BaseObject* target) {
    if (!target) return false;

    bool result = false;
    int power = 0;
    int secPower = 0;
    int weaponDamage = 0;
    bool powerHit = false;

    try {
        // 计算基础攻击力
        power = GetAttackPower(m_WAbil.DC.min, m_WAbil.DC.max - m_WAbil.DC.min);

        // 处理烈火剑法
        if (hitMode == 7 && m_boFireHitSkill) {
            m_boFireHitSkill = false;
            m_dwLatestFireHitTick = GetCurrentTime();
            power = power + static_cast<int>(power / 100.0 * (m_nHitDouble * 10));
            powerHit = true;
        }

        // 处理刺杀剑法
        if (hitMode == 4) {
            secPower = 0;
            if (m_btRaceServer == RC_PLAYOBJECT || m_btRaceServer == RC_PLAYMOSTER) {
                if (m_MagicErgumSkill) {
                    secPower = static_cast<int>(power / (m_MagicErgumSkill->btLevel + 2) *
                              (m_MagicErgumSkill->btLevel + 2));
                }
            }

            if (secPower > 0) {
                // 执行刺杀攻击
                if (!SwordLongAttack(target, secPower)) {
                    hitMode = 0; // 刺杀失败，改为普通攻击
                }
            }
        }

        // 处理半月弯刀
        if (hitMode == 5) {
            secPower = 0;
            if (m_btRaceServer == RC_PLAYOBJECT || m_btRaceServer == RC_PLAYMOSTER) {
                if (m_MagicBanwolSkill) {
                    secPower = static_cast<int>(power / (m_MagicBanwolSkill->btLevel + 10) *
                              (m_MagicBanwolSkill->btLevel + 2));
                }
            }

            if (secPower > 0) {
                SwordWideAttack(secPower);
            }
        }

        // 处理野蛮冲撞
        if (hitMode == 6) {
            secPower = 0;
            if (m_btRaceServer == RC_PLAYOBJECT || m_btRaceServer == RC_PLAYMOSTER) {
                // 野蛮冲撞逻辑
            }

            if (secPower > 0) {
                CrsWideAttack(secPower);
            }
        }

        // 检查是否是合适的攻击目标
        if (!IsProperTarget(target)) {
            power = 0;
        } else {
            // 命中率检查
            if ((rand() % target->m_btSpeedPoint) >= m_btHitPoint) {
                power = 0; // 未命中
            }
        }

        // 如果攻击命中
        if (power > 0) {
            // 计算最终伤害
            power = target->GetHitStruckDamage(this, power);
            weaponDamage = (rand() % 5) + 2 - m_AddAbil.btWeaponStrong;
        }

        // 应用伤害
        if (power > 0) {
            target->StruckDamage(power);

            // 发送攻击消息
            target->SendDelayMsg(this, RM_STRUCK, RM_10101, power,
                               target->m_WAbil.HP, target->m_WAbil.MaxHP,
                               reinterpret_cast<intptr_t>(this), "", 200);

            // 麻痹效果
            if (!target->m_boUnParalysis && m_boParalysis &&
                (rand() % (target->m_btAntiPoison + 5)) == 0) {
                target->MakePosion(POISON_STONE, 5, 0);
            }

            // 吸血效果（红魔套装）
            if (m_nHongMoSuite > 0) {
                double absorbHP = power / 100.0 * m_nHongMoSuite;
                if (absorbHP >= 2.0) {
                    int healAmount = static_cast<int>(absorbHP);
                    DamageHealth(-healAmount); // 负数表示恢复
                }
            }

            result = true;
        }

        // 武器损坏
        if (weaponDamage > 0 && m_useItems[U_WEAPON].wIndex > 0) {
            DoDamageWeapon(weaponDamage);
        }

        // 发送攻击消息给非玩家对象
        if (target && target->m_btRaceServer != RC_PLAYOBJECT) {
            target->SendMsg(target, RM_STRUCK, power, target->m_WAbil.HP,
                          target->m_WAbil.MaxHP, reinterpret_cast<intptr_t>(this), "");
        }

    } catch (...) {
        Logger::Error("Exception in BaseObject::_Attack for " + m_sCharName);
    }

    return result;
}

// 刺杀剑法攻击
bool BaseObject::SwordLongAttack(BaseObject* target, int power) {
    if (!target) return false;

    // 计算刺杀攻击的伤害
    power = static_cast<int>(power * 0.8); // 刺杀伤害系数

    // 获取前方第二个位置的目标
    Point frontPos = GetFrontPosition();
    Point targetPos = GetNextPosition(m_direction);

    if (target->GetCurrentPos() == targetPos) {
        return DirectAttack(target, power);
    }

    return false;
}

// 半月弯刀攻击
bool BaseObject::SwordWideAttack(int power) {
    bool result = false;

    // 攻击前方三个方向的目标
    for (int i = -1; i <= 1; ++i) {
        DirectionType attackDir = static_cast<DirectionType>((static_cast<int>(m_direction) + i + 8) % 8);
        Point attackPos = GetNextPosition(attackDir);

        // 查找该位置的目标
        if (m_environment) {
            auto objects = m_environment->GetObjectsAt(attackPos);
            for (auto obj : objects) {
                if (obj && obj.get() != this && IsProperTarget(obj.get())) {
                    if (DirectAttack(obj.get(), power)) {
                        result = true;
                    }
                }
            }
        }
    }

    return result;
}

// 野蛮冲撞攻击
bool BaseObject::CrsWideAttack(int power) {
    bool result = false;

    // 攻击周围7个方向的目标
    for (int i = 0; i < 7; ++i) {
        DirectionType attackDir = static_cast<DirectionType>((static_cast<int>(m_direction) + i) % 8);
        Point attackPos = GetNextPosition(attackDir);

        // 查找该位置的目标
        if (m_environment) {
            auto objects = m_environment->GetObjectsAt(attackPos);
            for (auto obj : objects) {
                if (obj && obj.get() != this && IsProperTarget(obj.get())) {
                    if (DirectAttack(obj.get(), power)) {
                        result = true;
                    }
                }
            }
        }
    }

    return result;
}

// 直接攻击
bool BaseObject::DirectAttack(BaseObject* target, int power) {
    if (!target || !IsProperTarget(target)) return false;

    // 检查是否在安全区
    if (InSafeZone() && target->InSafeZone()) {
        return false;
    }

    // 命中率检查
    if ((rand() % target->m_btSpeedPoint) < m_btHitPoint) {
        target->StruckDamage(power);

        // 发送攻击消息
        target->SendDelayMsg(this, RM_STRUCK, RM_10101, power,
                           target->m_WAbil.HP, target->m_WAbil.MaxHP,
                           reinterpret_cast<intptr_t>(this), "", 500);

        // 发送给非玩家对象
        if (target->m_btRaceServer != RC_PLAYOBJECT) {
            target->SendMsg(target, RM_STRUCK, power, target->m_WAbil.HP,
                          target->m_WAbil.MaxHP, reinterpret_cast<intptr_t>(this), "");
        }

        return true;
    }

    return false;
}

// 中毒状态
bool BaseObject::MakePosion(int type, int time, int point) {
    if (type >= MAX_STATUS_ATTRIBUTE) return false;

    int oldCharStatus = m_nCharStatus;

    if (m_wStatusTimeArr[type] > 0) {
        if (m_wStatusTimeArr[type] < time) {
            m_wStatusTimeArr[type] = time;
        }
    } else {
        m_wStatusTimeArr[type] = time;
    }

    m_dwStatusArrTick[type] = GetCurrentTime();
    m_nCharStatus = GetCharStatus();
    m_btGreenPoisoningPoint = point;

    if (oldCharStatus != m_nCharStatus) {
        StatusChanged();
    }

    if (m_btRaceServer == RC_PLAYOBJECT) {
        // 发送中毒消息
        SendMsg(this, RM_SYSMESSAGE, 0, 0, 0, 0, "你中毒了！");
    }

    return true;
}

// 魔法盾伤害
void BaseObject::DamageBubbleDefence(int damage) {
    // 魔法盾承受伤害的逻辑
    // 这里可以根据需要实现具体的魔法盾机制
}

// 生命魔法值变化通知
void BaseObject::HealthSpellChanged() {
    // 发送生命魔法值变化消息
    SendDefMessage(SM_HEALTHSPELLCHANGED, 0, m_WAbil.HP, m_WAbil.MP, m_WAbil.MaxHP);
}

// 重新计算属性
void BaseObject::RecalcAbilitys() {
    // 重新计算所有属性，包括装备加成等
    // 这里需要根据装备、技能等重新计算各项属性

    // 基础属性重置
    m_WAbil = m_Abil;

    // 装备属性加成
    for (int i = 0; i < U_MAXUSEITEM; ++i) {
        if (m_useItems[i].wIndex > 0) {
            // 根据装备添加属性加成
            // 这里需要从装备数据库获取装备属性
        }
    }

    // 发送属性变化消息
    SendMsg(this, RM_ABILITY, 0, 0, 0, 0, "");
    SendMsg(this, RM_SUBABILITY, 0, 0, 0, 0, "");
}

} // namespace MirServer
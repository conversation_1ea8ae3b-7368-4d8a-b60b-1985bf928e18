// MapManager.cpp - 地图管理器实现
#include "MapManager.h"
#include "Environment.h"
#include "../Common/Logger.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <filesystem>

namespace MirServer {

// 全局实例
std::unique_ptr<MapManager> g_MapManager;

MapManager::MapManager() {
}

MapManager::~MapManager() {
    Finalize();
}

bool MapManager::Initialize(const std::string& mapPath) {
    if (m_initialized) {
        return true;
    }
    
    m_mapPath = mapPath;
    
    // 检查路径是否存在
    if (!std::filesystem::exists(mapPath)) {
        Logger::Error("Map path does not exist: " + mapPath);
        return false;
    }
    
    m_initialized = true;
    Logger::Info("MapManager initialized with path: " + mapPath);
    return true;
}

void MapManager::Finalize() {
    if (!m_initialized) {
        return;
    }
    
    // 清理所有地图数据
    {
        std::lock_guard<std::mutex> lock(m_mapsMutex);
        m_maps.clear();
    }
    
    m_initialized = false;
    Logger::Info("MapManager finalized");
}

bool MapManager::LoadMapList(const std::string& listFile) {
    std::string fullPath = m_mapPath + "/" + listFile;
    std::ifstream file(fullPath);
    
    if (!file.is_open()) {
        Logger::Error("Failed to open map list file: " + fullPath);
        return false;
    }
    
    std::string line;
    int lineNum = 0;
    
    while (std::getline(file, line)) {
        lineNum++;
        
        // 跳过空行和注释
        if (line.empty() || line[0] == ';' || line[0] == '#') {
            continue;
        }
        
        MapInfo info;
        if (ParseMapListLine(line, info)) {
            // 加载地图信息
            {
                std::lock_guard<std::mutex> lock(m_mapsMutex);
                
                auto mapData = std::make_unique<MapData>();
                mapData->info = info;
                mapData->loaded = false;
                
                m_maps[info.mapName] = std::move(mapData);
            }
            
            Logger::Info("Loaded map info: " + info.mapName);
        } else {
            Logger::Warning("Failed to parse map list line " + std::to_string(lineNum) + ": " + line);
        }
    }
    
    file.close();
    
    Logger::Info("Loaded " + std::to_string(m_maps.size()) + " map definitions");
    return true;
}

bool MapManager::LoadMapInfo(const std::string& mapName) {
    std::lock_guard<std::mutex> lock(m_mapsMutex);
    
    auto it = m_maps.find(mapName);
    if (it == m_maps.end()) {
        Logger::Error("Map not found: " + mapName);
        return false;
    }
    
    // 如果已经加载，返回成功
    if (it->second->loaded) {
        return true;
    }
    
    // 加载地图文件
    return LoadMapFile(it->second->info.mapFile, it->second->info);
}

const MapInfo* MapManager::GetMapInfo(const std::string& mapName) const {
    std::lock_guard<std::mutex> lock(m_mapsMutex);
    
    auto it = m_maps.find(mapName);
    if (it != m_maps.end()) {
        return &it->second->info;
    }
    
    return nullptr;
}

std::vector<std::string> MapManager::GetAllMapNames() const {
    std::lock_guard<std::mutex> lock(m_mapsMutex);
    
    std::vector<std::string> names;
    names.reserve(m_maps.size());
    
    for (const auto& pair : m_maps) {
        names.push_back(pair.first);
    }
    
    return names;
}

bool MapManager::MapExists(const std::string& mapName) const {
    std::lock_guard<std::mutex> lock(m_mapsMutex);
    return m_maps.find(mapName) != m_maps.end();
}

bool MapManager::LoadMapData(const std::string& mapName) {
    std::lock_guard<std::mutex> lock(m_mapsMutex);
    
    auto it = m_maps.find(mapName);
    if (it == m_maps.end()) {
        Logger::Error("Map not found: " + mapName);
        return false;
    }
    
    MapData* mapData = it->second.get();
    
    // 如果已经加载，返回成功
    if (mapData->loaded) {
        return true;
    }
    
    // 检查是否超过最大加载数
    size_t loadedCount = 0;
    for (const auto& pair : m_maps) {
        if (pair.second->loaded) {
            loadedCount++;
        }
    }
    
    if (loadedCount >= m_maxLoadedMaps) {
        Logger::Warning("Maximum loaded maps reached: " + std::to_string(m_maxLoadedMaps));
        
        // 如果开启自动卸载，卸载一个最久未使用的地图
        if (m_autoUnload) {
            // TODO: 实现LRU卸载策略
        } else {
            return false;
        }
    }
    
    // 加载地图单元格数据
    if (!LoadMapCells(mapData->info.mapFile, mapData->cells)) {
        Logger::Error("Failed to load map cells: " + mapName);
        return false;
    }
    
    // 生成可行走标记
    int width = mapData->info.width;
    int height = mapData->info.height;
    
    mapData->walkable.resize(height);
    for (int y = 0; y < height; y++) {
        mapData->walkable[y].resize(width);
        for (int x = 0; x < width; x++) {
            // 根据地图单元格数据判断是否可行走
            mapData->walkable[y][x] = 1; // 默认可行走
            // TODO: 根据MapCellInfo判断
        }
    }
    
    // 创建地图环境
    mapData->environment = EnvironmentManager::Instance().CreateEnvironment(mapName, width, height);
    
    // 设置地图单元格属性
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            bool canWalk = mapData->walkable[y][x] != 0;
            mapData->environment->SetCellAttribute(x, y, canWalk, canWalk, canWalk);
        }
    }
    
    // 处理地图连接
    ProcessMapConnections(mapData->info);
    
    // 处理安全区
    ProcessSafeZones(mapData->info);
    
    mapData->loaded = true;
    Logger::Info("Loaded map data: " + mapName);
    
    return true;
}

bool MapManager::UnloadMapData(const std::string& mapName) {
    std::lock_guard<std::mutex> lock(m_mapsMutex);
    
    auto it = m_maps.find(mapName);
    if (it == m_maps.end()) {
        return false;
    }
    
    MapData* mapData = it->second.get();
    
    if (!mapData->loaded) {
        return true;
    }
    
    // 清理地图数据
    mapData->cells.clear();
    mapData->walkable.clear();
    
    // 移除环境
    if (mapData->environment) {
        EnvironmentManager::Instance().RemoveEnvironment(mapName);
        mapData->environment.reset();
    }
    
    mapData->loaded = false;
    Logger::Info("Unloaded map data: " + mapName);
    
    return true;
}

bool MapManager::IsMapLoaded(const std::string& mapName) const {
    std::lock_guard<std::mutex> lock(m_mapsMutex);
    
    auto it = m_maps.find(mapName);
    if (it != m_maps.end()) {
        return it->second->loaded;
    }
    
    return false;
}

std::shared_ptr<Environment> MapManager::GetMapEnvironment(const std::string& mapName) {
    // 确保地图已加载
    if (!IsMapLoaded(mapName)) {
        if (!LoadMapData(mapName)) {
            return nullptr;
        }
    }
    
    std::lock_guard<std::mutex> lock(m_mapsMutex);
    
    auto it = m_maps.find(mapName);
    if (it != m_maps.end()) {
        return it->second->environment;
    }
    
    return nullptr;
}

bool MapManager::CreateMapEnvironment(const std::string& mapName) {
    auto info = GetMapInfo(mapName);
    if (!info) {
        return false;
    }
    
    // 创建环境
    auto env = EnvironmentManager::Instance().CreateEnvironment(mapName, info->width, info->height);
    
    std::lock_guard<std::mutex> lock(m_mapsMutex);
    
    auto it = m_maps.find(mapName);
    if (it != m_maps.end()) {
        it->second->environment = env;
        return true;
    }
    
    return false;
}

bool MapManager::CanWalk(const std::string& mapName, int x, int y) const {
    std::lock_guard<std::mutex> lock(m_mapsMutex);
    
    auto it = m_maps.find(mapName);
    if (it == m_maps.end() || !it->second->loaded) {
        return false;
    }
    
    const MapData* mapData = it->second.get();
    
    if (y < 0 || y >= mapData->info.height || x < 0 || x >= mapData->info.width) {
        return false;
    }
    
    return mapData->walkable[y][x] != 0;
}

bool MapManager::CanFly(const std::string& mapName, int x, int y) const {
    // 暂时与CanWalk相同
    return CanWalk(mapName, x, y);
}

bool MapManager::IsValidPosition(const std::string& mapName, int x, int y) const {
    auto info = GetMapInfo(mapName);
    if (!info) {
        return false;
    }
    
    return x >= 0 && x < info->width && y >= 0 && y < info->height;
}

bool MapManager::IsSafeZone(const std::string& mapName, const Point& pos) const {
    auto info = GetMapInfo(mapName);
    if (!info) {
        return false;
    }
    
    // 检查安全区
    for (const auto& zone : info->safeZones) {
        if (pos.x >= zone.startPos.x && pos.x <= zone.endPos.x &&
            pos.y >= zone.startPos.y && pos.y <= zone.endPos.y) {
            return true;
        }
    }
    
    return false;
}

Point MapManager::GetMapConnection(const std::string& fromMap, const Point& pos, std::string& toMap) const {
    auto info = GetMapInfo(fromMap);
    if (!info) {
        return Point(-1, -1);
    }
    
    // 查找连接点
    for (const auto& conn : info->connections) {
        if (conn.sourcePos.x == pos.x && conn.sourcePos.y == pos.y) {
            toMap = conn.targetMap;
            return conn.targetPos;
        }
    }
    
    return Point(-1, -1);
}

bool MapManager::AddMapConnection(const std::string& fromMap, const std::string& toMap, 
                                 const Point& fromPos, const Point& toPos) {
    std::lock_guard<std::mutex> lock(m_mapsMutex);
    
    auto it = m_maps.find(fromMap);
    if (it == m_maps.end()) {
        return false;
    }
    
    MapInfo::Connection conn;
    conn.targetMap = toMap;
    conn.sourcePos = fromPos;
    conn.targetPos = toPos;
    
    it->second->info.connections.push_back(conn);
    
    return true;
}

void MapManager::AddMapPoint(const std::string& mapName, const MapPoint& point) {
    std::lock_guard<std::mutex> lock(m_pointsMutex);
    m_mapPoints[mapName].push_back(point);
}

std::vector<MapManager::MapPoint> MapManager::GetMapPoints(const std::string& mapName) const {
    std::lock_guard<std::mutex> lock(m_pointsMutex);
    
    auto it = m_mapPoints.find(mapName);
    if (it != m_mapPoints.end()) {
        return it->second;
    }
    
    return {};
}

void MapManager::SetMapAttribute(const std::string& mapName, const std::string& attribute, bool value) {
    std::lock_guard<std::mutex> lock(m_mapsMutex);
    
    auto it = m_maps.find(mapName);
    if (it == m_maps.end()) {
        return;
    }
    
    MapInfo& info = it->second->info;
    
    // 设置属性
    if (attribute == "canRide") info.canRide = value;
    else if (attribute == "canRest") info.canRest = value;
    else if (attribute == "canThrowItem") info.canThrowItem = value;
    else if (attribute == "noReconnect") info.noReconnect = value;
    else if (attribute == "noRandMove") info.noRandMove = value;
    else if (attribute == "noDrug") info.noDrug = value;
    else if (attribute == "noPositionMove") info.noPositionMove = value;
    else if (attribute == "noRecall") info.noRecall = value;
    else if (attribute == "noReincarnation") info.noReincarnation = value;
    else if (attribute == "noFight") info.noFight = value;
    else if (attribute == "fight3") info.fight3 = value;
    else if (attribute == "fightPK") info.fightPK = value;
}

bool MapManager::GetMapAttribute(const std::string& mapName, const std::string& attribute) const {
    auto info = GetMapInfo(mapName);
    if (!info) {
        return false;
    }
    
    // 获取属性
    if (attribute == "canRide") return info->canRide;
    else if (attribute == "canRest") return info->canRest;
    else if (attribute == "canThrowItem") return info->canThrowItem;
    else if (attribute == "noReconnect") return info->noReconnect;
    else if (attribute == "noRandMove") return info->noRandMove;
    else if (attribute == "noDrug") return info->noDrug;
    else if (attribute == "noPositionMove") return info->noPositionMove;
    else if (attribute == "noRecall") return info->noRecall;
    else if (attribute == "noReincarnation") return info->noReincarnation;
    else if (attribute == "noFight") return info->noFight;
    else if (attribute == "fight3") return info->fight3;
    else if (attribute == "fightPK") return info->fightPK;
    
    return false;
}

MapManager::Statistics MapManager::GetStatistics() const {
    std::lock_guard<std::mutex> lock(m_mapsMutex);
    
    Statistics stats;
    stats.totalMaps = m_maps.size();
    
    for (const auto& pair : m_maps) {
        if (pair.second->loaded) {
            stats.loadedMaps++;
        }
        stats.totalConnections += pair.second->info.connections.size();
        stats.totalSafeZones += pair.second->info.safeZones.size();
    }
    
    {
        std::lock_guard<std::mutex> pointsLock(m_pointsMutex);
        for (const auto& pair : m_mapPoints) {
            stats.totalMapPoints += pair.second.size();
        }
    }
    
    return stats;
}

void MapManager::DumpMapInfo(const std::string& mapName) const {
    auto info = GetMapInfo(mapName);
    if (!info) {
        Logger::Error("Map not found: " + mapName);
        return;
    }
    
    Logger::Info("=== Map Info: " + mapName + " ===");
    Logger::Info("File: " + info->mapFile);
    Logger::Info("Title: " + info->title);
    Logger::Info("Size: " + std::to_string(info->width) + "x" + std::to_string(info->height));
    Logger::Info("Type: " + std::to_string(static_cast<int>(info->mapType)));
    Logger::Info("Connections: " + std::to_string(info->connections.size()));
    Logger::Info("Safe Zones: " + std::to_string(info->safeZones.size()));
    
    // 打印属性
    Logger::Info("Attributes:");
    Logger::Info("  canRide: " + std::string(info->canRide ? "true" : "false"));
    Logger::Info("  canRest: " + std::string(info->canRest ? "true" : "false"));
    Logger::Info("  canThrowItem: " + std::string(info->canThrowItem ? "true" : "false"));
    Logger::Info("  noReconnect: " + std::string(info->noReconnect ? "true" : "false"));
    Logger::Info("  noFight: " + std::string(info->noFight ? "true" : "false"));
}

void MapManager::ValidateAllMaps() const {
    auto mapNames = GetAllMapNames();
    
    for (const auto& mapName : mapNames) {
        auto info = GetMapInfo(mapName);
        if (!info) {
            Logger::Error("Invalid map: " + mapName);
            continue;
        }
        
        // 验证连接
        for (const auto& conn : info->connections) {
            if (!MapExists(conn.targetMap)) {
                Logger::Warning("Map " + mapName + " has invalid connection to " + conn.targetMap);
            }
        }
    }
}

// 内部方法实现
bool MapManager::LoadMapFile(const std::string& fileName, MapInfo& info) {
    std::string fullPath = GetMapFilePath(fileName);
    std::ifstream file(fullPath, std::ios::binary);
    
    if (!file.is_open()) {
        Logger::Error("Failed to open map file: " + fullPath);
        return false;
    }
    
    // 读取文件头
    MapFileHeader header;
    file.read(reinterpret_cast<char*>(&header), sizeof(header));
    
    if (!file.good()) {
        Logger::Error("Failed to read map header: " + fileName);
        return false;
    }
    
    // 更新地图信息
    info.width = header.width;
    info.height = header.height;
    info.title = std::string(header.title, strnlen(header.title, sizeof(header.title)));
    
    file.close();
    return true;
}

bool MapManager::LoadMapCells(const std::string& fileName, std::vector<std::vector<MapCellInfo>>& cells) {
    std::string fullPath = GetMapFilePath(fileName);
    std::ifstream file(fullPath, std::ios::binary);
    
    if (!file.is_open()) {
        Logger::Error("Failed to open map file for cells: " + fullPath);
        return false;
    }
    
    // 跳过文件头
    file.seekg(sizeof(MapFileHeader));
    
    // 读取地图单元格数据
    // TODO: 实际的地图格式可能不同，需要根据实际情况调整
    
    file.close();
    return true;
}

bool MapManager::ParseMapListLine(const std::string& line, MapInfo& info) {
    std::istringstream iss(line);
    std::string token;
    
    // 格式示例：MapFile MapName ServerIndex Width Height [Attributes...]
    if (!(iss >> info.mapFile >> info.mapName >> info.serverIndex >> info.width >> info.height)) {
        return false;
    }
    
    // 设置默认值
    info.title = info.mapName;
    info.mapType = MapType::NORMAL;
    info.miniMap = 0;
    info.bigMap = 0;
    info.canRide = true;
    info.canRest = true;
    info.canThrowItem = true;
    info.noReconnect = false;
    info.noRandMove = false;
    info.noDrug = false;
    info.noPositionMove = false;
    info.noRecall = false;
    info.noReincarnation = false;
    info.needBridge = false;
    info.needHole = false;
    info.noFight = false;
    info.fight3 = false;
    info.fightPK = false;
    info.music = false;
    info.musicID = 0;
    info.lightMode = 0;
    
    // 解析额外属性
    while (iss >> token) {
        if (token == "NORECONNECT") info.noReconnect = true;
        else if (token == "NORANDMOVE") info.noRandMove = true;
        else if (token == "NODRUG") info.noDrug = true;
        else if (token == "NOFIGHT") info.noFight = true;
        else if (token == "FIGHT3") info.fight3 = true;
        else if (token == "FIGHTPK") info.fightPK = true;
        else if (token == "SAFE") {
            // 安全区定义
            MapInfo::SafeZone zone;
            if (iss >> zone.startPos.x >> zone.startPos.y >> zone.endPos.x >> zone.endPos.y) {
                info.safeZones.push_back(zone);
            }
        }
    }
    
    return true;
}

void MapManager::ProcessMapConnections(MapInfo& info) {
    // 处理地图连接信息
    // 这里通常从配置文件或数据库读取
}

void MapManager::ProcessSafeZones(MapInfo& info) {
    // 将安全区信息添加到环境中
    auto env = EnvironmentManager::Instance().GetEnvironment(info.mapName);
    if (!env) {
        return;
    }
    
    for (const auto& zone : info.safeZones) {
        MapEvent event;
        event.type = MapEventType::SAFE_ZONE;
        event.position.x = (zone.startPos.x + zone.endPos.x) / 2;
        event.position.y = (zone.startPos.y + zone.endPos.y) / 2;
        event.range = std::max(
            std::abs(zone.endPos.x - zone.startPos.x) / 2,
            std::abs(zone.endPos.y - zone.startPos.y) / 2
        );
        
        env->AddMapEvent(event);
    }
}

std::string MapManager::GetMapFilePath(const std::string& mapFile) const {
    return m_mapPath + "/" + mapFile;
}

std::vector<uint8_t> MapManager::GetMiniMapData(const std::string& mapName) const {
    // TODO: 加载小地图数据
    return {};
}

} // namespace MirServer 
#pragma once

#include "../Common/Types.h"
#include "../Common/GameData.h"
#include <unordered_map>
#include <vector>
#include <string>
#include <functional>
#include <memory>

namespace MirServer {

// 前向声明
class PlayObject;
class BaseObject;

// 魔法信息结构（对应delphi的TMagic）
struct MagicInfo {
    WORD magicId = 0;              // 魔法ID
    std::string magicName;         // 魔法名称
    BYTE effectType = 0;           // 效果类型
    BYTE effect = 0;               // 效果
    WORD spell = 0;                // 施法消耗
    WORD power = 0;                // 威力
    WORD maxPower = 0;             // 最大威力
    BYTE job = 0;                  // 职业 (0:战士 1:法师 2:道士)
    BYTE trainLevel[4] = {0};      // 各等级需求
    WORD maxTrain[4] = {0};        // 各等级训练值
    DWORD delayTime = 0;           // 延迟时间
    BYTE defSpell = 0;             // 防御施法
    BYTE defPower = 0;             // 防御威力
    BYTE defMaxPower = 0;          // 防御最大威力
    std::string descr;             // 描述
};

// 持续性魔法效果
struct ActiveMagic {
    WORD magicId = 0;              // 魔法ID
    PlayObject* caster = nullptr;   // 施法者
    Point position{0, 0};          // 位置
    DWORD startTime = 0;           // 开始时间
    DWORD endTime = 0;             // 结束时间
    DWORD tickInterval = 0;        // tick间隔
    DWORD nextTickTime = 0;        // 下次tick时间
    int power = 0;                 // 威力
    std::unordered_map<std::string, int> params; // 额外参数
};

// 伤害类型
enum class DamageType : BYTE {
    PHYSICAL = 0,                  // 物理伤害
    MAGIC = 1,                     // 魔法伤害
    POISON = 2,                    // 毒伤害
    FIRE = 3,                      // 火伤害
    ICE = 4,                       // 冰伤害
    LIGHTNING = 5                  // 雷伤害
};

// 魔法处理器函数类型
using MagicProcessor = std::function<bool(PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos)>;

// 魔法管理器类（对应delphi的TMagicManager）
class MagicManager {
public:
    MagicManager();
    ~MagicManager();

    // 初始化和清理
    bool Initialize();
    void Finalize();
    
    // 魔法数据管理
    bool LoadMagicData();
    const MagicInfo* GetMagicInfo(WORD magicId) const;
    
    // 魔法使用
    bool CanUseMagic(PlayObject* player, WORD magicId) const;
    bool UseMagic(PlayObject* caster, WORD magicId, BaseObject* target = nullptr, const Point& targetPos = Point(0, 0));
    
    // 运行时更新
    void Run();
    
    // 魔法效果处理器
    bool ProcessFireBall(PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos);
    bool ProcessHeal(PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos);
    bool ProcessBigFireBall(PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos);
    bool ProcessResistRing(PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos);
    bool ProcessHellFire(PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos);
    bool ProcessLightning(PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos);
    bool ProcessDefaultMagic(PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos);

private:
    // 数据解析
    bool ParseMagicLine(const std::string& line, MagicInfo& magic);
    
    // 初始化
    void InitializeMagicProcessors();
    
    // 持续性魔法处理
    void ProcessActiveMagics(DWORD currentTime);
    void ProcessMagicTick(ActiveMagic& activeMagic);
    void ProcessHellFireTick(ActiveMagic& activeMagic);
    void OnMagicEnd(const ActiveMagic& activeMagic);
    
    // 冷却时间管理
    void CleanupCooldowns(DWORD currentTime);
    
    // 辅助方法
    int CalculateMagicDamage(PlayObject* caster, const MagicInfo& magic, BaseObject* target);
    int CalculateHealAmount(PlayObject* caster, const MagicInfo& magic);
    std::vector<BaseObject*> GetTargetsInRange(PlayObject* caster, const Point& center, int range);
    bool IsValidTarget(PlayObject* caster, BaseObject* target);
    DirectionType GetDirection(const Point& from, const Point& to);
    void PushObject(BaseObject* obj, DirectionType direction, int distance);
    void ApplyParalysis(BaseObject* target, DWORD duration);
    void SendMagicEffect(PlayObject* caster, WORD magicId, const Point& pos, uint32_t targetId);

private:
    bool m_initialized;
    
    // 魔法数据
    std::unordered_map<WORD, MagicInfo> m_magicInfos;
    
    // 魔法处理器
    std::unordered_map<WORD, MagicProcessor> m_magicProcessors;
    
    // 持续性魔法效果
    std::unordered_map<std::string, ActiveMagic> m_activeMagics;
    
    // 冷却时间记录
    std::unordered_map<std::string, DWORD> m_lastCastTimes; // key: playerName_magicId
};

// 全局魔法管理器实例
extern std::unique_ptr<MagicManager> g_MagicManager;

} // namespace MirServer

#include "ScriptEngine.h"
#include "GameEngine.h"
#include "MapManager.h"
#include "PKManager.h"
#include "../BaseObject/PlayObject.h"
#include "../BaseObject/NPC.h"
#include "../Common/Utils.h"
#include "../Common/Logger.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cctype>
#include <regex>
#include <functional>

namespace MirServer {

// ==================== ScriptEngine实现 ====================

ScriptEngine::ScriptEngine() {
    // 初始化条件处理器映射
    m_conditionHandlers["CHECKLEVEL"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckLevel(player, condition);
    };
    m_conditionHandlers["CHECKJOB"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckJob(player, condition);
    };
    m_conditionHandlers["CHECKGOLD"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckGold(player, condition);
    };
    m_conditionHandlers["CHECKITEM"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckItem(player, condition);
    };
    m_conditionHandlers["CHECKBAGSIZE"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckBagSize(player, condition);
    };
    m_conditionHandlers["CHECKDC"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckDC(player, condition);
    };
    m_conditionHandlers["CHECKMC"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckMC(player, condition);
    };
    m_conditionHandlers["CHECKSC"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckSC(player, condition);
    };
    m_conditionHandlers["CHECKHP"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckHP(player, condition);
    };
    m_conditionHandlers["CHECKMP"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckMP(player, condition);
    };
    m_conditionHandlers["CHECKEXP"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckExp(player, condition);
    };
    m_conditionHandlers["CHECKPKPOINT"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckPKPoint(player, condition);
    };
    m_conditionHandlers["CHECKCREDITPOINT"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckCreditPoint(player, condition);
    };
    m_conditionHandlers["CHECKSKILL"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckSkill(player, condition);
    };
    m_conditionHandlers["CHECKGENDER"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckGender(player, condition);
    };
    m_conditionHandlers["CHECKMAPNAME"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckMapName(player, condition);
    };
    m_conditionHandlers["CHECKSAFEZONE"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckSafeZone(player, condition);
    };

    // 时间相关条件
    m_conditionHandlers["CHECKTIME"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckTime(player, condition);
    };
    m_conditionHandlers["CHECKDATE"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckDate(player, condition);
    };
    m_conditionHandlers["CHECKDAY"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckDay(player, condition);
    };
    m_conditionHandlers["CHECKHOUR"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckHour(player, condition);
    };
    m_conditionHandlers["CHECKMIN"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckMin(player, condition);
    };

    // 游戏状态条件
    m_conditionHandlers["CHECKMARRY"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckMarry(player, condition);
    };
    m_conditionHandlers["CHECKMASTER"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckMaster(player, condition);
    };
    m_conditionHandlers["CHECKGUILD"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckGuild(player, condition);
    };
    m_conditionHandlers["CHECKGUILDRANK"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckGuildRank(player, condition);
    };
    m_conditionHandlers["CHECKCASTLEOWNER"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckCastleOwner(player, condition);
    };

    // 高级条件
    m_conditionHandlers["CHECKVAR"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckVar(player, condition);
    };
    m_conditionHandlers["CHECKNAMELIST"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckNameList(player, condition);
    };
    m_conditionHandlers["CHECKIPLIST"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckIPList(player, condition);
    };
    m_conditionHandlers["CHECKACCOUNTLIST"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckAccountList(player, condition);
    };
    m_conditionHandlers["CHECKSLAVECOUNT"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckSlaveCount(player, condition);
    };
    m_conditionHandlers["CHECKONLINE"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckOnline(player, condition);
    };
    m_conditionHandlers["CHECKDURAEVA"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckDuraEva(player, condition);
    };
    m_conditionHandlers["CHECKBAGCOUNT"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckBagCount(player, condition);
    };
    m_conditionHandlers["CHECKITEMW"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckItemW(player, condition);
    };
    m_conditionHandlers["CHECKITEMTYPE"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckItemType(player, condition);
    };
    m_conditionHandlers["CHECKITEMADDVALUE"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckItemAddValue(player, condition);
    };
    m_conditionHandlers["CHECKITEMLEVEL"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckItemLevel(player, condition);
    };

    // 装备条件
    m_conditionHandlers["CHECKWEARING"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckWearing(player, condition);
    };
    m_conditionHandlers["CHECKWEAPON"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckWeapon(player, condition);
    };
    m_conditionHandlers["CHECKARMOR"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckArmor(player, condition);
    };
    m_conditionHandlers["CHECKNECKLACE"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckNecklace(player, condition);
    };
    m_conditionHandlers["CHECKHELMET"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckHelmet(player, condition);
    };
    m_conditionHandlers["CHECKRING_L"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckRingL(player, condition);
    };
    m_conditionHandlers["CHECKRING_R"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckRingR(player, condition);
    };
    m_conditionHandlers["CHECKARMRING_L"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckArmRingL(player, condition);
    };
    m_conditionHandlers["CHECKARMRING_R"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckArmRingR(player, condition);
    };
    m_conditionHandlers["CHECKBELT"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckBelt(player, condition);
    };
    m_conditionHandlers["CHECKBOOTS"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckBoots(player, condition);
    };
    m_conditionHandlers["CHECKCHARM"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckCharm(player, condition);
    };

    // 特殊条件
    m_conditionHandlers["CHECKGROUPCOUNT"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckGroupCount(player, condition);
    };
    m_conditionHandlers["CHECKGROUPLEADER"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckGroupLeader(player, condition);
    };
    m_conditionHandlers["CHECKPOSEDIR"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckPoseDir(player, condition);
    };
    m_conditionHandlers["CHECKPOSELEVEL"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckPoseLevel(player, condition);
    };
    m_conditionHandlers["CHECKCONTAINSTEXT"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckContainsText(player, condition);
    };
    m_conditionHandlers["CHECKSTRINGLIST"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckStringList(player, condition);
    };
    m_conditionHandlers["CHECKRANGECOUNT"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckRangeCount(player, condition);
    };
    m_conditionHandlers["CHECKMONCOUNT"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckMonCount(player, condition);
    };
    m_conditionHandlers["CHECKHUMCOUNT"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckHumCount(player, condition);
    };
    m_conditionHandlers["CHECKISADMIN"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckIsAdmin(player, condition);
    };
    m_conditionHandlers["CHECKACCOUNTIPCOUNT"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckAccountIPCount(player, condition);
    };
    m_conditionHandlers["CHECKIPCOUNT"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckIPCount(player, condition);
    };

    // 补充的高级条件
    m_conditionHandlers["CHECKCASTLEMASTER"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckCastleMaster(player, condition);
    };
    m_conditionHandlers["CHECKGUILDMASTER"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckGuildMaster(player, condition);
    };
    m_conditionHandlers["CHECKBUILDPOINT"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckBuildPoint(player, condition);
    };
    m_conditionHandlers["CHECKPLAYERCOUNT"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckPlayerCount(player, condition);
    };
    m_conditionHandlers["CHECKGAMEGOLD"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckGameGold(player, condition);
    };
    m_conditionHandlers["CHECKGAMEPOINT"] = [this](PlayObject* player, const ScriptCondition& condition) {
        return CheckGamePoint(player, condition);
    };

    // 初始化动作处理器映射
    m_actionHandlers["GIVE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionGive(player, action);
    };
    m_actionHandlers["TAKE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionTake(player, action);
    };
    m_actionHandlers["GIVEEXP"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionGiveExp(player, action);
    };
    m_actionHandlers["TAKEGOLD"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionTakeGold(player, action);
    };
    m_actionHandlers["GIVEGOLD"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionGiveGold(player, action);
    };
    m_actionHandlers["GIVEITEM"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionGiveItem(player, action);
    };
    m_actionHandlers["TAKEITEM"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionTakeItem(player, action);
    };
    m_actionHandlers["GIVESKILL"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionGiveSkill(player, action);
    };
    m_actionHandlers["TAKESKILL"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionTakeSkill(player, action);
    };
    m_actionHandlers["CHANGELEVEL"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionChangeLevel(player, action);
    };
    m_actionHandlers["CHANGEJOB"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionChangeJob(player, action);
    };
    m_actionHandlers["CHANGEGENDER"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionChangeGender(player, action);
    };
    m_actionHandlers["CHANGEPKPOINT"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionChangePKPoint(player, action);
    };
    m_actionHandlers["MAP"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionMap(player, action);
    };
    m_actionHandlers["MAPMOVE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionMapMove(player, action);
    };
    m_actionHandlers["RECALL"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionRecall(player, action);
    };
    m_actionHandlers["OPENMERCHANT"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionOpenMerchant(player, npc, action);
    };
    m_actionHandlers["OPENREPAIR"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionOpenRepair(player, npc, action);
    };
    m_actionHandlers["OPENSTORAGE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionOpenStorage(player, npc, action);
    };
    m_actionHandlers["SENDMSG"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionSendMsg(player, action);
    };
    m_actionHandlers["MESSAGEBOX"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionMessageBox(player, action);
    };
    m_actionHandlers["GOTO"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        // GOTO需要特殊处理，这里返回false表示需要跳转
        return false;
    };
    m_actionHandlers["BREAK"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionBreak(player, action);
    };
    m_actionHandlers["EXIT"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionExit(player, action);
    };
    m_actionHandlers["CLOSE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionClose(player, action);
    };

    // 怪物和宠物相关动作
    m_actionHandlers["MONGEN"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionMonGen(player, action);
    };
    m_actionHandlers["KILLMONSTER"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionKillMonster(player, action);
    };
    m_actionHandlers["KILLSLAVE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionKillSlave(player, action);
    };
    m_actionHandlers["RECALLSLAVE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionRecallSlave(player, action);
    };
    m_actionHandlers["CLEARSLAVE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionClearSlave(player, action);
    };
    m_actionHandlers["MONGENEX"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionMonGenEx(player, action);
    };
    m_actionHandlers["MOBPLACE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionMobPlace(player, action);
    };
    m_actionHandlers["MOBCOUNT"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionMobCount(player, action);
    };
    m_actionHandlers["CLEARMON"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionClearMon(player, action);
    };
    m_actionHandlers["CLEARITEM"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionClearItem(player, action);
    };

    // 高级功能动作
    m_actionHandlers["GMEXECUTE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionGMExecute(player, action);
    };
    m_actionHandlers["ADDNAMELIST"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionAddNameList(player, action);
    };
    m_actionHandlers["DELNAMELIST"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionDelNameList(player, action);
    };
    m_actionHandlers["ADDIPLIST"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionAddIPList(player, action);
    };
    m_actionHandlers["DELIPLIST"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionDelIPList(player, action);
    };
    m_actionHandlers["ADDACCOUNTLIST"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionAddAccountList(player, action);
    };
    m_actionHandlers["DELACCOUNTLIST"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionDelAccountList(player, action);
    };
    m_actionHandlers["SETVAR"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionSetVar(player, action);
    };
    m_actionHandlers["CALCVAR"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionCalcVar(player, action);
    };
    m_actionHandlers["SAVEVAR"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionSaveVar(player, action);
    };
    m_actionHandlers["LOADVAR"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionLoadVar(player, action);
    };

    // 行会相关动作
    m_actionHandlers["ADDGUILD"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionAddGuild(player, action);
    };
    m_actionHandlers["DELGUILD"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionDelGuild(player, action);
    };
    m_actionHandlers["GUILDWAR"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionGuildWar(player, action);
    };
    m_actionHandlers["ENDGUILDWAR"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionEndGuildWar(player, action);
    };

    // 特殊功能动作
    m_actionHandlers["HAIR"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionHair(player, action);
    };
    m_actionHandlers["TAKEW"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionTakeW(player, action);
    };
    m_actionHandlers["TAKEON"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionTakeOn(player, action);
    };
    m_actionHandlers["SENDMSGUSER"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionSendMsgUser(player, action);
    };
    m_actionHandlers["SENDMSGMAP"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionSendMsgMap(player, action);
    };
    m_actionHandlers["SENDMSGALL"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionSendMsgAll(player, action);
    };
    m_actionHandlers["TIMERECALLMOB"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionTimeRecallMob(player, action);
    };

    // 补充的重要动作
    m_actionHandlers["SERVERNOTICE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionServerNotice(player, action);
    };
    m_actionHandlers["SYSTEMBROADCAST"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionSystemBroadcast(player, action);
    };
    m_actionHandlers["BUILDCASTLE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionBuildCastle(player, action);
    };
    m_actionHandlers["REPAIRCASTLE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionRepairCastle(player, action);
    };
    m_actionHandlers["UPGRADECASTLE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionUpgradeCastle(player, action);
    };
    m_actionHandlers["CHANGEGAMEGOLD"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionChangeGameGold(player, action);
    };
    m_actionHandlers["CHANGEGAMEPOINT"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionChangeGamePoint(player, action);
    };
    m_actionHandlers["PARAM1"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionParam1(player, action);
    };
    m_actionHandlers["PARAM2"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionParam2(player, action);
    };
    m_actionHandlers["PARAM3"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionParam3(player, action);
    };
    m_actionHandlers["AUTOADDPOINT"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionAutoAddPoint(player, action);
    };
    m_actionHandlers["DELAYEXECUTE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionDelayExecute(player, action);
    };
    m_actionHandlers["RANDEXECUTE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionRandExecute(player, action);
    };
    m_actionHandlers["CHECKEXECUTE"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionCheckExecute(player, action);
    };
    m_actionHandlers["RESTART"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionRestart(player, action);
    };
    m_actionHandlers["PLAYBGM"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionPlayBGM(player, action);
    };
    m_actionHandlers["PLAYWAV"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionPlayWav(player, action);
    };
    m_actionHandlers["DAYCHANGECOLOR"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionDayChangeColor(player, action);
    };
    m_actionHandlers["NIGHTCHANGECOLOR"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionNightChangeColor(player, action);
    };
    m_actionHandlers["FIREBURN"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionFireBurn(player, action);
    };
    m_actionHandlers["LIGHTING"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionLighting(player, action);
    };
    m_actionHandlers["DIGUP"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionDigUp(player, action);
    };
    m_actionHandlers["DIGDOWN"] = [this](PlayObject* player, NPC* npc, const ScriptAction& action) {
        return ActionDigDown(player, action);
    };
}

ScriptEngine::~ScriptEngine() {
}

bool ScriptEngine::ExecuteScript(PlayObject* player, NPC* npc, const NPCScript& script, const std::string& label) {
    if (!player || !npc) {
        SetLastError("玩家或NPC为空");
        return false;
    }

    const ScriptBlock* block = script.GetBlock(label);
    if (!block) {
        SetLastError("找不到脚本标签: " + label);
        return false;
    }

    return ExecuteScriptBlock(player, npc, *block);
}

bool ScriptEngine::ExecuteScriptBlock(PlayObject* player, NPC* npc, const ScriptBlock& block) {
    if (!player || !npc) {
        SetLastError("玩家或NPC为空");
        return false;
    }

    // 检查所有条件
    bool conditionsPass = CheckAllConditions(player, npc, block.conditions);

    if (conditionsPass) {
        // 条件通过，执行正常动作
        if (!ExecuteAllActions(player, npc, block.actions)) {
            return false;
        }
    } else {
        // 条件不通过，执行ELSE动作
        if (!ExecuteAllActions(player, npc, block.elseActions)) {
            return false;
        }
    }

    // 发送对话内容
    SendDialog(player, npc, block);

    return true;
}

void ScriptEngine::SetLastError(const std::string& error) {
    m_lastError = error;
    Logger::Error("脚本执行错误: " + error);
}

bool ScriptEngine::CheckCondition(PlayObject* player, NPC* npc, const ScriptCondition& condition) {
    if (!player) {
        SetLastError("玩家为空");
        return false;
    }

    auto it = m_conditionHandlers.find(condition.type);
    if (it != m_conditionHandlers.end()) {
        return it->second(player, condition);
    }

    SetLastError("未知的条件类型: " + condition.type);
    return false;
}

bool ScriptEngine::CheckAllConditions(PlayObject* player, NPC* npc, const std::vector<ScriptCondition>& conditions) {
    // 如果没有条件，默认通过
    if (conditions.empty()) {
        return true;
    }

    // 所有条件都必须通过
    for (const auto& condition : conditions) {
        if (!CheckCondition(player, npc, condition)) {
            return false;
        }
    }

    return true;
}

bool ScriptEngine::ExecuteAction(PlayObject* player, NPC* npc, const ScriptAction& action) {
    if (!player) {
        SetLastError("玩家为空");
        return false;
    }

    auto it = m_actionHandlers.find(action.type);
    if (it != m_actionHandlers.end()) {
        return it->second(player, npc, action);
    }

    SetLastError("未知的动作类型: " + action.type);
    return false;
}

bool ScriptEngine::ExecuteAllActions(PlayObject* player, NPC* npc, const std::vector<ScriptAction>& actions) {
    for (const auto& action : actions) {
        // 特殊处理GOTO动作
        if (action.type == "GOTO") {
            // GOTO动作需要在外层处理，这里只是标记
            continue;
        }

        if (!ExecuteAction(player, npc, action)) {
            return false;
        }
    }

    return true;
}

bool ScriptEngine::GotoLabel(PlayObject* player, NPC* npc, const NPCScript& script, const std::string& label) {
    return ExecuteScript(player, npc, script, label);
}

void ScriptEngine::SendDialog(PlayObject* player, NPC* npc, const ScriptBlock& block) {
    if (!player || !npc) return;

    // 替换变量
    std::string dialogText = ScriptParser::ReplaceVariables(block.text, player, npc);

    // 构建选项列表
    std::vector<std::string> options;
    for (const auto& option : block.options) {
        options.push_back(option.text);
    }

    // 发送对话到客户端
    // TODO: 实现具体的对话发送协议
    Logger::Info("发送对话给玩家 " + player->GetCharName() + ": " + dialogText);

    for (size_t i = 0; i < options.size(); ++i) {
        Logger::Info("选项 " + std::to_string(i) + ": " + options[i]);
    }
}

// ==================== 条件检查实现 ====================

bool ScriptEngine::CheckLevel(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    int playerLevel = player->GetLevel();
    int requiredLevel = condition.nParam1;
    char method = condition.method;

    return CompareValue(playerLevel, requiredLevel, method);
}

bool ScriptEngine::CheckJob(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    JobType playerJob = player->GetJob();
    int requiredJob = condition.nParam1;

    return static_cast<int>(playerJob) == requiredJob;
}

bool ScriptEngine::CheckGold(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    int playerGold = player->GetGold();
    int requiredGold = condition.nParam1;
    char method = condition.method;

    return CompareValue(playerGold, requiredGold, method);
}

bool ScriptEngine::CheckItem(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    std::string itemName = condition.param1;
    int requiredCount = condition.nParam2;

    // TODO: 实现物品检查
    // int itemCount = player->GetItemCount(itemName);
    // return itemCount >= requiredCount;

    Logger::Warning("CheckItem 尚未完全实现: " + itemName);
    return true; // 临时返回true
}

bool ScriptEngine::CheckBagSize(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现背包大小检查
    // int bagSize = player->GetBagSize();
    // int requiredSize = condition.nParam1;
    // return bagSize >= requiredSize;

    Logger::Warning("CheckBagSize 尚未完全实现");
    return true; // 临时返回true
}

bool ScriptEngine::CheckDC(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    int playerDC = player->GetAbility().DC.min;
    int requiredDC = condition.nParam1;
    char method = condition.method;

    return CompareValue(playerDC, requiredDC, method);
}

bool ScriptEngine::CheckMC(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    int playerMC = player->GetAbility().MC.min;
    int requiredMC = condition.nParam1;
    char method = condition.method;

    return CompareValue(playerMC, requiredMC, method);
}

bool ScriptEngine::CheckSC(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    int playerSC = player->GetAbility().SC.min;
    int requiredSC = condition.nParam1;
    char method = condition.method;

    return CompareValue(playerSC, requiredSC, method);
}

bool ScriptEngine::CheckHP(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    int playerHP = player->GetHP();
    int requiredHP = condition.nParam1;
    char method = condition.method;

    return CompareValue(playerHP, requiredHP, method);
}

bool ScriptEngine::CheckMP(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    int playerMP = player->GetMP();
    int requiredMP = condition.nParam1;
    char method = condition.method;

    return CompareValue(playerMP, requiredMP, method);
}

bool ScriptEngine::CheckExp(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    DWORD playerExp = player->GetAbility().Exp;
    DWORD requiredExp = static_cast<DWORD>(condition.nParam1);
    char method = condition.method;

    return CompareValue(static_cast<int>(playerExp), static_cast<int>(requiredExp), method);
}

bool ScriptEngine::CheckPKPoint(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    auto& pkManager = PKManager::GetInstance();
    DWORD playerPKPoint = pkManager.GetPKValue(player);
    DWORD requiredPKPoint = static_cast<DWORD>(condition.nParam1);
    char method = condition.method;

    return CompareValue(static_cast<int>(playerPKPoint), static_cast<int>(requiredPKPoint), method);
}

bool ScriptEngine::CheckCreditPoint(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现声望点检查
    // int playerCreditPoint = player->GetCreditPoint();
    // int requiredCreditPoint = condition.nParam1;
    // char method = condition.method;
    // return CompareValue(playerCreditPoint, requiredCreditPoint, method);

    Logger::Warning("CheckCreditPoint 尚未完全实现");
    return true; // 临时返回true
}

bool ScriptEngine::CheckSkill(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    std::string skillName = condition.param1;

    // TODO: 实现技能检查
    // bool hasSkill = player->HasSkill(skillName);
    // return hasSkill;

    Logger::Warning("CheckSkill 尚未完全实现: " + skillName);
    return true; // 临时返回true
}

bool ScriptEngine::CheckGender(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    GenderType playerGender = player->GetGender();
    int requiredGender = condition.nParam1;

    return static_cast<int>(playerGender) == requiredGender;
}

bool ScriptEngine::CheckMapName(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    std::string playerMap = player->GetMapName();
    std::string requiredMap = condition.param1;

    return playerMap == requiredMap;
}

bool ScriptEngine::CheckSafeZone(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现安全区检查
    // bool inSafeZone = player->IsInSafeZone();
    // return inSafeZone;

    Logger::Warning("CheckSafeZone 尚未完全实现");
    return true; // 临时返回true
}

// ==================== 时间相关条件检查实现 ====================

bool ScriptEngine::CheckTime(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    time_t now = time(nullptr);
    struct tm* timeinfo = localtime(&now);

    int currentTime = timeinfo->tm_hour * 100 + timeinfo->tm_min; // HHMM格式
    int requiredTime = condition.nParam1;
    char method = condition.method;

    return CompareValue(currentTime, requiredTime, method);
}

bool ScriptEngine::CheckDate(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    time_t now = time(nullptr);
    struct tm* timeinfo = localtime(&now);

    int currentDate = (timeinfo->tm_year + 1900) * 10000 + (timeinfo->tm_mon + 1) * 100 + timeinfo->tm_mday; // YYYYMMDD格式
    int requiredDate = condition.nParam1;
    char method = condition.method;

    return CompareValue(currentDate, requiredDate, method);
}

bool ScriptEngine::CheckDay(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    time_t now = time(nullptr);
    struct tm* timeinfo = localtime(&now);

    int currentDay = timeinfo->tm_mday;
    int requiredDay = condition.nParam1;
    char method = condition.method;

    return CompareValue(currentDay, requiredDay, method);
}

bool ScriptEngine::CheckHour(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    time_t now = time(nullptr);
    struct tm* timeinfo = localtime(&now);

    int currentHour = timeinfo->tm_hour;
    int requiredHour = condition.nParam1;
    char method = condition.method;

    return CompareValue(currentHour, requiredHour, method);
}

bool ScriptEngine::CheckMin(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    time_t now = time(nullptr);
    struct tm* timeinfo = localtime(&now);

    int currentMin = timeinfo->tm_min;
    int requiredMin = condition.nParam1;
    char method = condition.method;

    return CompareValue(currentMin, requiredMin, method);
}

// ==================== 游戏状态条件检查实现 ====================

bool ScriptEngine::CheckMarry(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现结婚状态检查
    // bool isMarried = player->IsMarried();
    // return isMarried;

    Logger::Warning("CheckMarry 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckMaster(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现师父检查
    // std::string masterName = player->GetMasterName();
    // std::string requiredMaster = condition.param1;
    // return masterName == requiredMaster;

    Logger::Warning("CheckMaster 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckGuild(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现行会检查
    // std::string guildName = player->GetGuildName();
    // std::string requiredGuild = condition.param1;
    // return guildName == requiredGuild;

    Logger::Warning("CheckGuild 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckGuildRank(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现行会等级检查
    // int guildRank = player->GetGuildRank();
    // int requiredRank = condition.nParam1;
    // char method = condition.method;
    // return CompareValue(guildRank, requiredRank, method);

    Logger::Warning("CheckGuildRank 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckCastleOwner(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现城堡主人检查
    // bool isCastleOwner = player->IsCastleOwner();
    // return isCastleOwner;

    Logger::Warning("CheckCastleOwner 尚未完全实现");
    return false; // 临时返回false
}

// ==================== 高级条件检查实现 ====================

bool ScriptEngine::CheckVar(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    std::string varName = condition.param1;
    int varValue = GetScriptVar(varName);
    int requiredValue = condition.nParam2;
    char method = condition.method;

    return CompareValue(varValue, requiredValue, method);
}

bool ScriptEngine::CheckNameList(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    std::string listName = condition.param1;
    std::string playerName = player->GetCharName();
    return m_listManager.IsInNameList(listName, playerName);
}

bool ScriptEngine::CheckIPList(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    std::string listName = condition.param1;
    std::string playerIP = player->GetIPAddress();
    return m_listManager.IsInIPList(listName, playerIP);
}

bool ScriptEngine::CheckAccountList(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    std::string listName = condition.param1;
    std::string accountName = player->GetAccountName();
    return m_listManager.IsInAccountList(listName, accountName);
}

bool ScriptEngine::CheckSlaveCount(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现宠物数量检查
    // int slaveCount = player->GetSlaveCount();
    // int requiredCount = condition.nParam1;
    // char method = condition.method;
    // return CompareValue(slaveCount, requiredCount, method);

    Logger::Warning("CheckSlaveCount 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckOnline(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现在线检查
    // std::string targetName = condition.param1;
    // bool isOnline = IsPlayerOnline(targetName);
    // return isOnline;

    Logger::Warning("CheckOnline 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckDuraEva(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现装备耐久度检查
    // int itemPos = condition.nParam1;
    // int durability = player->GetItemDurability(itemPos);
    // int requiredDura = condition.nParam2;
    // char method = condition.method;
    // return CompareValue(durability, requiredDura, method);

    Logger::Warning("CheckDuraEva 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckBagCount(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现背包物品数量检查
    // int itemCount = player->GetBagItemCount();
    // int requiredCount = condition.nParam1;
    // char method = condition.method;
    // return CompareValue(itemCount, requiredCount, method);

    Logger::Warning("CheckBagCount 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckItemW(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现装备重量检查
    // std::string itemName = condition.param1;
    // int itemWeight = player->GetItemWeight(itemName);
    // int requiredWeight = condition.nParam2;
    // char method = condition.method;
    // return CompareValue(itemWeight, requiredWeight, method);

    Logger::Warning("CheckItemW 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckItemType(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现物品类型检查
    // std::string itemName = condition.param1;
    // int itemType = player->GetItemType(itemName);
    // int requiredType = condition.nParam2;
    // return itemType == requiredType;

    Logger::Warning("CheckItemType 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckItemAddValue(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现物品附加值检查
    // std::string itemName = condition.param1;
    // int addValue = player->GetItemAddValue(itemName);
    // int requiredValue = condition.nParam2;
    // char method = condition.method;
    // return CompareValue(addValue, requiredValue, method);

    Logger::Warning("CheckItemAddValue 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckItemLevel(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现物品等级检查
    // std::string itemName = condition.param1;
    // int itemLevel = player->GetItemLevel(itemName);
    // int requiredLevel = condition.nParam2;
    // char method = condition.method;
    // return CompareValue(itemLevel, requiredLevel, method);

    Logger::Warning("CheckItemLevel 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

// ==================== 装备条件检查实现 ====================

bool ScriptEngine::CheckWearing(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现装备检查
    // std::string itemName = condition.param1;
    // bool isWearing = player->IsWearing(itemName);
    // return isWearing;

    Logger::Warning("CheckWearing 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckWeapon(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现武器检查
    // std::string weaponName = condition.param1;
    // std::string currentWeapon = player->GetWeaponName();
    // return currentWeapon == weaponName;

    Logger::Warning("CheckWeapon 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckArmor(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现盔甲检查
    // std::string armorName = condition.param1;
    // std::string currentArmor = player->GetArmorName();
    // return currentArmor == armorName;

    Logger::Warning("CheckArmor 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckNecklace(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现项链检查
    // std::string necklaceName = condition.param1;
    // std::string currentNecklace = player->GetNecklaceName();
    // return currentNecklace == necklaceName;

    Logger::Warning("CheckNecklace 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckHelmet(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现头盔检查
    // std::string helmetName = condition.param1;
    // std::string currentHelmet = player->GetHelmetName();
    // return currentHelmet == helmetName;

    Logger::Warning("CheckHelmet 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckRingL(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现左戒指检查
    // std::string ringName = condition.param1;
    // std::string currentRing = player->GetLeftRingName();
    // return currentRing == ringName;

    Logger::Warning("CheckRingL 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckRingR(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现右戒指检查
    // std::string ringName = condition.param1;
    // std::string currentRing = player->GetRightRingName();
    // return currentRing == ringName;

    Logger::Warning("CheckRingR 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckArmRingL(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现左手镯检查
    // std::string armRingName = condition.param1;
    // std::string currentArmRing = player->GetLeftArmRingName();
    // return currentArmRing == armRingName;

    Logger::Warning("CheckArmRingL 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckArmRingR(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现右手镯检查
    // std::string armRingName = condition.param1;
    // std::string currentArmRing = player->GetRightArmRingName();
    // return currentArmRing == armRingName;

    Logger::Warning("CheckArmRingR 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckBelt(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现腰带检查
    // std::string beltName = condition.param1;
    // std::string currentBelt = player->GetBeltName();
    // return currentBelt == beltName;

    Logger::Warning("CheckBelt 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckBoots(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现靴子检查
    // std::string bootsName = condition.param1;
    // std::string currentBoots = player->GetBootsName();
    // return currentBoots == bootsName;

    Logger::Warning("CheckBoots 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckCharm(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现护身符检查
    // std::string charmName = condition.param1;
    // std::string currentCharm = player->GetCharmName();
    // return currentCharm == charmName;

    Logger::Warning("CheckCharm 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

// ==================== 特殊条件检查实现 ====================

bool ScriptEngine::CheckGroupCount(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现组队人数检查
    // int groupCount = player->GetGroupCount();
    // int requiredCount = condition.nParam1;
    // char method = condition.method;
    // return CompareValue(groupCount, requiredCount, method);

    Logger::Warning("CheckGroupCount 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckGroupLeader(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现队长检查
    // bool isLeader = player->IsGroupLeader();
    // return isLeader;

    Logger::Warning("CheckGroupLeader 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckPoseDir(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现方向检查
    // DirectionType currentDir = player->GetDirection();
    // int requiredDir = condition.nParam1;
    // return static_cast<int>(currentDir) == requiredDir;

    Logger::Warning("CheckPoseDir 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckPoseLevel(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现位置等级检查
    // Point currentPos = player->GetCurrentPos();
    // int posLevel = GetPositionLevel(currentPos);
    // int requiredLevel = condition.nParam1;
    // char method = condition.method;
    // return CompareValue(posLevel, requiredLevel, method);

    Logger::Warning("CheckPoseLevel 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckContainsText(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现文本包含检查
    // std::string text = condition.param1;
    // std::string searchText = condition.param2;
    // return text.find(searchText) != std::string::npos;

    Logger::Warning("CheckContainsText 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckStringList(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现字符串列表检查
    // std::string listName = condition.param1;
    // std::string searchText = condition.param2;
    // return IsInStringList(listName, searchText);

    Logger::Warning("CheckStringList 尚未完全实现: " + condition.param1);
    return false; // 临时返回false
}

bool ScriptEngine::CheckRangeCount(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现范围内对象数量检查
    // int range = condition.nParam1;
    // int objectCount = GetObjectCountInRange(player->GetCurrentPos(), range);
    // int requiredCount = condition.nParam2;
    // char method = condition.method;
    // return CompareValue(objectCount, requiredCount, method);

    Logger::Warning("CheckRangeCount 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckMonCount(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现怪物数量检查
    // std::string mapName = player->GetMapName();
    // int monsterCount = GetMonsterCountInMap(mapName);
    // int requiredCount = condition.nParam1;
    // char method = condition.method;
    // return CompareValue(monsterCount, requiredCount, method);

    Logger::Warning("CheckMonCount 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckHumCount(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现玩家数量检查
    // std::string mapName = player->GetMapName();
    // int playerCount = GetPlayerCountInMap(mapName);
    // int requiredCount = condition.nParam1;
    // char method = condition.method;
    // return CompareValue(playerCount, requiredCount, method);

    Logger::Warning("CheckHumCount 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckIsAdmin(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现管理员权限检查
    // bool isAdmin = player->IsAdmin();
    // return isAdmin;

    Logger::Warning("CheckIsAdmin 尚未完全实现");
    return false; // 临时返回false，实际应该检查玩家权限
}

bool ScriptEngine::CheckAccountIPCount(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现账号IP数量检查
    // std::string accountName = player->GetAccountName();
    // int ipCount = GetAccountIPCount(accountName);
    // int requiredCount = condition.nParam1;
    // char method = condition.method;
    // return CompareValue(ipCount, requiredCount, method);

    Logger::Warning("CheckAccountIPCount 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckIPCount(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现IP连接数检查
    // std::string ipAddress = player->GetIPAddress();
    // int connectionCount = GetIPConnectionCount(ipAddress);
    // int requiredCount = condition.nParam1;
    // char method = condition.method;
    // return CompareValue(connectionCount, requiredCount, method);

    Logger::Warning("CheckIPCount 尚未完全实现");
    return false; // 临时返回false
}

// ==================== 动作执行实现 ====================

bool ScriptEngine::ActionGive(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    // GIVE 动作通常用于给予物品或经验
    std::string itemName = action.param1;
    int count = action.nParam2;

    // TODO: 实现物品给予
    // player->GiveItem(itemName, count);

    Logger::Info("给予玩家 " + player->GetCharName() + " 物品: " + itemName + " x" + std::to_string(count));
    return true;
}

bool ScriptEngine::ActionTake(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string itemName = action.param1;
    int count = action.nParam2;

    // TODO: 实现物品取走
    // bool success = player->TakeItem(itemName, count);
    // return success;

    Logger::Info("从玩家 " + player->GetCharName() + " 取走物品: " + itemName + " x" + std::to_string(count));
    return true;
}

bool ScriptEngine::ActionGiveExp(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    int exp = action.nParam1;

    // TODO: 实现经验给予
    // player->GiveExp(exp);

    Logger::Info("给予玩家 " + player->GetCharName() + " 经验: " + std::to_string(exp));
    return true;
}

bool ScriptEngine::ActionTakeGold(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    int gold = action.nParam1;

    // TODO: 实现金币扣除
    // bool success = player->TakeGold(gold);
    // return success;

    Logger::Info("从玩家 " + player->GetCharName() + " 扣除金币: " + std::to_string(gold));
    return true;
}

bool ScriptEngine::ActionGiveGold(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    int gold = action.nParam1;

    // TODO: 实现金币给予
    // player->GiveGold(gold);

    Logger::Info("给予玩家 " + player->GetCharName() + " 金币: " + std::to_string(gold));
    return true;
}

bool ScriptEngine::ActionGiveItem(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string itemName = action.param1;
    int count = action.nParam2;

    // TODO: 实现物品给予
    // player->GiveItem(itemName, count);

    Logger::Info("给予玩家 " + player->GetCharName() + " 物品: " + itemName + " x" + std::to_string(count));
    return true;
}

bool ScriptEngine::ActionTakeItem(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string itemName = action.param1;
    int count = action.nParam2;

    // TODO: 实现物品取走
    // bool success = player->TakeItem(itemName, count);
    // return success;

    Logger::Info("从玩家 " + player->GetCharName() + " 取走物品: " + itemName + " x" + std::to_string(count));
    return true;
}

bool ScriptEngine::ActionGiveSkill(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string skillName = action.param1;

    // TODO: 实现技能给予
    // player->GiveSkill(skillName);

    Logger::Info("给予玩家 " + player->GetCharName() + " 技能: " + skillName);
    return true;
}

bool ScriptEngine::ActionTakeSkill(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string skillName = action.param1;

    // TODO: 实现技能移除
    // bool success = player->TakeSkill(skillName);
    // return success;

    Logger::Info("从玩家 " + player->GetCharName() + " 移除技能: " + skillName);
    return true;
}

bool ScriptEngine::ActionChangeLevel(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    char method = action.param1.empty() ? '=' : action.param1[0];
    int level = action.nParam2;

    // TODO: 实现等级修改
    // int currentLevel = player->GetLevel();
    // int newLevel = currentLevel;
    //
    // switch (method) {
    //     case '=': newLevel = level; break;
    //     case '+': newLevel = currentLevel + level; break;
    //     case '-': newLevel = currentLevel - level; break;
    // }
    //
    // player->SetLevel(newLevel);

    Logger::Info("修改玩家 " + player->GetCharName() + " 等级: " + std::string(1, method) + std::to_string(level));
    return true;
}

bool ScriptEngine::ActionChangeJob(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    int job = action.nParam1;

    // TODO: 实现职业修改
    // player->SetJob(job);

    Logger::Info("修改玩家 " + player->GetCharName() + " 职业: " + std::to_string(job));
    return true;
}

bool ScriptEngine::ActionChangeGender(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    int gender = action.nParam1;

    // TODO: 实现性别修改
    // player->SetGender(gender);

    Logger::Info("修改玩家 " + player->GetCharName() + " 性别: " + std::to_string(gender));
    return true;
}

bool ScriptEngine::ActionChangePKPoint(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    char method = action.param1.empty() ? '=' : action.param1[0];
    int pkPoint = action.nParam2;

    auto& pkManager = PKManager::GetInstance();
    DWORD currentPKPoint = pkManager.GetPKValue(player);
    DWORD newPKPoint = currentPKPoint;

    switch (method) {
        case '=':
            newPKPoint = static_cast<DWORD>(pkPoint);
            break;
        case '+':
            newPKPoint = currentPKPoint + static_cast<DWORD>(pkPoint);
            break;
        case '-':
            if (static_cast<DWORD>(pkPoint) <= currentPKPoint) {
                newPKPoint = currentPKPoint - static_cast<DWORD>(pkPoint);
            } else {
                newPKPoint = 0;
            }
            break;
        default:
            newPKPoint = static_cast<DWORD>(pkPoint);
            break;
    }

    pkManager.SetPKValue(player, newPKPoint);

    Logger::Info("修改玩家 " + player->GetCharName() + " PK点: " + std::string(1, method) + std::to_string(pkPoint) +
                " (从 " + std::to_string(currentPKPoint) + " 到 " + std::to_string(newPKPoint) + ")");
    return true;
}

bool ScriptEngine::ActionMap(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string mapName = action.param1;

    // 获取地图管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* mapManager = gameEngine.GetMapManager();

    if (!mapManager) {
        Logger::Error("MapManager not available for script teleport");
        return false;
    }

    // 检查地图是否存在
    const MapInfo* mapInfo = mapManager->GetMapInfo(mapName);
    if (!mapInfo) {
        Logger::Error("Map not found for script teleport: " + mapName);
        player->SendMessage("目标地图不存在: " + mapName, 0);
        return false;
    }

    // 使用地图的默认传送点（通常是地图中心）
    int x = mapInfo->width / 2;
    int y = mapInfo->height / 2;

    // 查找安全的传送点
    Point safePos = FindSafeTeleportPosition(mapName, Point(x, y));

    // 执行传送
    if (player->SpaceMove(mapName, safePos.x, safePos.y)) {
        Logger::Info("传送玩家 " + player->GetCharName() + " 到地图: " + mapName +
                    " 坐标: (" + std::to_string(safePos.x) + "," + std::to_string(safePos.y) + ")");
        player->SendMessage("传送到 " + mapName, 0);
        return true;
    } else {
        Logger::Error("Failed to teleport player " + player->GetCharName() + " to map: " + mapName);
        player->SendMessage("传送失败", 0);
        return false;
    }
}

bool ScriptEngine::ActionMapMove(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string mapName = action.param1;
    int x = action.nParam2;
    int y = action.nParam3;

    // 获取地图管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* mapManager = gameEngine.GetMapManager();

    if (!mapManager) {
        Logger::Error("MapManager not available for script teleport");
        return false;
    }

    // 检查地图是否存在
    const MapInfo* mapInfo = mapManager->GetMapInfo(mapName);
    if (!mapInfo) {
        Logger::Error("Map not found for script teleport: " + mapName);
        player->SendMessage("目标地图不存在: " + mapName, 0);
        return false;
    }

    // 验证坐标是否有效
    if (!mapManager->IsValidPosition(mapName, x, y)) {
        Logger::Warning("Invalid position for script teleport: " + mapName +
                       " (" + std::to_string(x) + "," + std::to_string(y) + ")");
        // 查找附近的安全位置
        Point safePos = FindSafeTeleportPosition(mapName, Point(x, y));
        x = safePos.x;
        y = safePos.y;
    }

    // 检查是否可以行走
    if (!mapManager->CanWalk(mapName, x, y)) {
        Logger::Warning("Cannot walk to script teleport position: " + mapName +
                       " (" + std::to_string(x) + "," + std::to_string(y) + ")");
        // 查找附近的安全位置
        Point safePos = FindSafeTeleportPosition(mapName, Point(x, y));
        x = safePos.x;
        y = safePos.y;
    }

    // 执行传送
    if (player->SpaceMove(mapName, x, y)) {
        Logger::Info("传送玩家 " + player->GetCharName() + " 到地图: " + mapName +
                    " 坐标: (" + std::to_string(x) + "," + std::to_string(y) + ")");
        player->SendMessage("传送到 " + mapName + " (" + std::to_string(x) + "," + std::to_string(y) + ")", 0);
        return true;
    } else {
        Logger::Error("Failed to teleport player " + player->GetCharName() + " to map: " + mapName +
                     " (" + std::to_string(x) + "," + std::to_string(y) + ")");
        player->SendMessage("传送失败", 0);
        return false;
    }
}

bool ScriptEngine::ActionRecall(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    // TODO: 实现召回功能
    // player->RecallToSafeZone();

    Logger::Info("召回玩家 " + player->GetCharName() + " 到安全区");
    return true;
}

bool ScriptEngine::ActionOpenMerchant(PlayObject* player, NPC* npc, const ScriptAction& action) {
    if (!player || !npc) return false;

    // TODO: 实现商店打开
    // player->OpenMerchant(npc);

    Logger::Info("为玩家 " + player->GetCharName() + " 打开商店");
    return true;
}

bool ScriptEngine::ActionOpenRepair(PlayObject* player, NPC* npc, const ScriptAction& action) {
    if (!player || !npc) return false;

    // TODO: 实现修理界面打开
    // player->OpenRepair(npc);

    Logger::Info("为玩家 " + player->GetCharName() + " 打开修理界面");
    return true;
}

bool ScriptEngine::ActionOpenStorage(PlayObject* player, NPC* npc, const ScriptAction& action) {
    if (!player || !npc) return false;

    // TODO: 实现仓库打开
    // player->OpenStorage();

    Logger::Info("为玩家 " + player->GetCharName() + " 打开仓库");
    return true;
}

bool ScriptEngine::ActionSendMsg(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    int msgType = action.nParam1;
    std::string message = action.param2;

    // TODO: 实现消息发送
    // player->SendMessage(message, msgType);

    Logger::Info("向玩家 " + player->GetCharName() + " 发送消息(类型:" + std::to_string(msgType) + "): " + message);
    return true;
}

bool ScriptEngine::ActionMessageBox(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string message = action.param1;

    // TODO: 实现消息框显示
    // player->ShowMessageBox(message);

    Logger::Info("向玩家 " + player->GetCharName() + " 显示消息框: " + message);
    return true;
}

bool ScriptEngine::ActionGoto(PlayObject* player, NPC* npc, const NPCScript& script, const ScriptAction& action) {
    if (!player || !npc) return false;

    std::string label = action.param1;

    // 执行脚本跳转
    return GotoLabel(player, npc, script, label);
}

bool ScriptEngine::ActionBreak(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    // BREAK 动作用于中断脚本执行
    Logger::Info("中断脚本执行 - 玩家: " + player->GetCharName());
    return false; // 返回false表示中断
}

bool ScriptEngine::ActionExit(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    // EXIT 动作用于退出对话
    // TODO: 实现对话退出
    // player->ExitDialog();

    Logger::Info("玩家 " + player->GetCharName() + " 退出对话");
    return true;
}

bool ScriptEngine::ActionClose(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    // CLOSE 动作用于关闭对话
    // TODO: 实现对话关闭
    // player->CloseDialog();

    Logger::Info("关闭玩家 " + player->GetCharName() + " 的对话");
    return true;
}

// ==================== 新增动作执行实现 ====================

bool ScriptEngine::ActionMonGen(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string monsterName = action.param1;
    int count = action.nParam2;
    int range = action.nParam3;

    // TODO: 实现怪物生成
    // Point playerPos = player->GetCurrentPos();
    // std::string mapName = player->GetMapName();
    // for (int i = 0; i < count; i++) {
    //     Point spawnPos = GetRandomPosInRange(playerPos, range);
    //     SpawnMonster(monsterName, mapName, spawnPos);
    // }

    Logger::Info("在玩家 " + player->GetCharName() + " 周围生成怪物: " + monsterName + " x" + std::to_string(count));
    return true;
}

bool ScriptEngine::ActionKillMonster(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string monsterName = action.param1;

    // TODO: 实现怪物击杀
    // std::string mapName = player->GetMapName();
    // KillMonstersInMap(mapName, monsterName);

    Logger::Info("击杀地图中的怪物: " + monsterName);
    return true;
}

bool ScriptEngine::ActionKillSlave(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    // TODO: 实现宠物击杀
    // player->KillAllSlaves();

    Logger::Info("击杀玩家 " + player->GetCharName() + " 的所有宠物");
    return true;
}

bool ScriptEngine::ActionRecallSlave(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    // TODO: 实现宠物召回
    // player->RecallAllSlaves();

    Logger::Info("召回玩家 " + player->GetCharName() + " 的所有宠物");
    return true;
}

bool ScriptEngine::ActionClearSlave(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    // TODO: 实现宠物清除
    // player->ClearAllSlaves();

    Logger::Info("清除玩家 " + player->GetCharName() + " 的所有宠物");
    return true;
}

bool ScriptEngine::ActionMonGenEx(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string monsterName = action.param1;
    std::string mapName = action.param2;
    int x = action.nParam3;
    int y = action.nParam4;
    int count = action.nParam5;

    // TODO: 实现扩展怪物生成
    // Point spawnPos(x, y);
    // for (int i = 0; i < count; i++) {
    //     SpawnMonster(monsterName, mapName, spawnPos);
    // }

    Logger::Info("在地图 " + mapName + " 坐标(" + std::to_string(x) + "," + std::to_string(y) + ") 生成怪物: " + monsterName + " x" + std::to_string(count));
    return true;
}

bool ScriptEngine::ActionMobPlace(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string monsterName = action.param1;
    int x = action.nParam2;
    int y = action.nParam3;

    // TODO: 实现怪物放置
    // std::string mapName = player->GetMapName();
    // Point spawnPos(x, y);
    // SpawnMonster(monsterName, mapName, spawnPos);

    Logger::Info("在坐标(" + std::to_string(x) + "," + std::to_string(y) + ") 放置怪物: " + monsterName);
    return true;
}

bool ScriptEngine::ActionMobCount(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string mapName = action.param1;

    // TODO: 实现怪物数量统计
    // int monsterCount = GetMonsterCountInMap(mapName);
    // Logger::Info("地图 " + mapName + " 中的怪物数量: " + std::to_string(monsterCount));

    Logger::Info("统计地图 " + mapName + " 中的怪物数量");
    return true;
}

bool ScriptEngine::ActionClearMon(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string mapName = action.param1;

    // TODO: 实现怪物清除
    // ClearAllMonstersInMap(mapName);

    Logger::Info("清除地图 " + mapName + " 中的所有怪物");
    return true;
}

bool ScriptEngine::ActionClearItem(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string mapName = action.param1;

    // TODO: 实现物品清除
    // ClearAllItemsInMap(mapName);

    Logger::Info("清除地图 " + mapName + " 中的所有物品");
    return true;
}

bool ScriptEngine::ActionGMExecute(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string command = action.param1;

    // TODO: 实现GM命令执行
    // ExecuteGMCommand(player, command);

    Logger::Info("执行GM命令: " + command);
    return true;
}

bool ScriptEngine::ActionAddNameList(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string listName = action.param1;
    std::string playerName = action.param2.empty() ? player->GetCharName() : action.param2;

    bool result = m_listManager.AddToNameList(listName, playerName);
    if (result) {
        Logger::Info("将玩家 " + playerName + " 添加到名单: " + listName);
    }
    return result;
}

bool ScriptEngine::ActionDelNameList(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string listName = action.param1;
    std::string playerName = action.param2.empty() ? player->GetCharName() : action.param2;

    bool result = m_listManager.RemoveFromNameList(listName, playerName);
    if (result) {
        Logger::Info("从名单 " + listName + " 中删除玩家: " + playerName);
    }
    return result;
}

bool ScriptEngine::ActionAddIPList(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string listName = action.param1;
    std::string ipAddress = action.param2;

    if (ipAddress.empty()) {
        ipAddress = player->GetIPAddress();
    }

    bool result = m_listManager.AddToIPList(listName, ipAddress);
    if (result) {
        Logger::Info("将IP " + ipAddress + " 添加到IP列表: " + listName);
    }
    return result;
}

bool ScriptEngine::ActionDelIPList(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string listName = action.param1;
    std::string ipAddress = action.param2;

    if (ipAddress.empty()) {
        ipAddress = player->GetIPAddress();
    }

    bool result = m_listManager.RemoveFromIPList(listName, ipAddress);
    if (result) {
        Logger::Info("从IP列表 " + listName + " 中删除IP: " + ipAddress);
    }
    return result;
}

bool ScriptEngine::ActionAddAccountList(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string listName = action.param1;
    std::string accountName = action.param2;

    if (accountName.empty()) {
        accountName = player->GetAccountName();
    }

    bool result = m_listManager.AddToAccountList(listName, accountName);
    if (result) {
        Logger::Info("将账号 " + accountName + " 添加到账号列表: " + listName);
    }
    return result;
}

bool ScriptEngine::ActionDelAccountList(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string listName = action.param1;
    std::string accountName = action.param2;

    if (accountName.empty()) {
        accountName = player->GetAccountName();
    }

    bool result = m_listManager.RemoveFromAccountList(listName, accountName);
    if (result) {
        Logger::Info("从账号列表 " + listName + " 中删除账号: " + accountName);
    }
    return result;
}

bool ScriptEngine::ActionSetVar(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string varName = action.param1;
    int value = action.nParam2;

    bool result = SetScriptVar(varName, value);
    if (result) {
        Logger::Info("设置变量 " + varName + " = " + std::to_string(value));
    }
    return result;
}

bool ScriptEngine::ActionCalcVar(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string varName = action.param1;
    char operation = action.param2.empty() ? '+' : action.param2[0];
    int value = action.nParam3;

    int currentValue = GetScriptVar(varName);
    int newValue = currentValue;

    switch (operation) {
        case '+': newValue = currentValue + value; break;
        case '-': newValue = currentValue - value; break;
        case '*': newValue = currentValue * value; break;
        case '/': newValue = (value != 0) ? currentValue / value : currentValue; break;
        case '=': newValue = value; break;
        default: newValue = currentValue + value; break; // 默认为加法
    }

    bool result = SetScriptVar(varName, newValue);
    if (result) {
        Logger::Info("计算变量 " + varName + " " + std::string(1, operation) + " " + std::to_string(value) +
                    " = " + std::to_string(newValue));
    }
    return result;
}

bool ScriptEngine::ActionSaveVar(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string varName = action.param1;
    bool result = SaveScriptVar(varName);

    if (result) {
        Logger::Info("保存变量: " + varName);
    }
    return result;
}

bool ScriptEngine::ActionLoadVar(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string varName = action.param1;
    bool result = LoadScriptVar(varName);

    if (result) {
        Logger::Info("加载变量: " + varName);
    }
    return result;
}

bool ScriptEngine::ActionAddGuild(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string guildName = action.param1;

    // TODO: 实现行会创建
    // CreateGuild(guildName, player);

    Logger::Info("创建行会: " + guildName);
    return true;
}

bool ScriptEngine::ActionDelGuild(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string guildName = action.param1;

    // TODO: 实现行会删除
    // DeleteGuild(guildName);

    Logger::Info("删除行会: " + guildName);
    return true;
}

bool ScriptEngine::ActionGuildWar(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string guild1 = action.param1;
    std::string guild2 = action.param2;
    DWORD duration = action.nParam1 > 0 ? static_cast<DWORD>(action.nParam1) : 30; // 默认30分钟

    auto& pkManager = PKManager::GetInstance();
    bool success = pkManager.StartGuildWar(guild1, guild2, duration);

    if (success) {
        Logger::Info("开始行会战争: " + guild1 + " vs " + guild2 + " (持续 " + std::to_string(duration) + " 分钟)");
    } else {
        Logger::Warning("无法开始行会战争: " + guild1 + " vs " + guild2 + " (可能已经在战争中)");
    }

    return success;
}

bool ScriptEngine::ActionEndGuildWar(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string guild1 = action.param1;
    std::string guild2 = action.param2;

    auto& pkManager = PKManager::GetInstance();
    bool success = pkManager.EndGuildWar(guild1, guild2);

    if (success) {
        Logger::Info("结束行会战争: " + guild1 + " vs " + guild2);
    } else {
        Logger::Warning("无法结束行会战争: " + guild1 + " vs " + guild2 + " (可能不在战争中)");
    }

    return success;
}

bool ScriptEngine::ActionHair(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    int hairStyle = action.nParam1;

    // TODO: 实现发型修改
    // player->SetHairStyle(hairStyle);

    Logger::Info("修改玩家 " + player->GetCharName() + " 发型: " + std::to_string(hairStyle));
    return true;
}

bool ScriptEngine::ActionTakeW(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    int position = action.nParam1;

    // TODO: 实现装备卸下
    // player->TakeOffEquipment(position);

    Logger::Info("卸下玩家 " + player->GetCharName() + " 位置 " + std::to_string(position) + " 的装备");
    return true;
}

bool ScriptEngine::ActionTakeOn(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string itemName = action.param1;

    // TODO: 实现装备穿戴
    // player->WearEquipment(itemName);

    Logger::Info("让玩家 " + player->GetCharName() + " 穿戴装备: " + itemName);
    return true;
}

bool ScriptEngine::ActionSendMsgUser(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string targetName = action.param1;
    std::string message = action.param2;

    // TODO: 实现向指定玩家发送消息
    // SendMessageToPlayer(targetName, message);

    Logger::Info("向玩家 " + targetName + " 发送消息: " + message);
    return true;
}

bool ScriptEngine::ActionSendMsgMap(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string mapName = action.param1;
    std::string message = action.param2;

    // TODO: 实现向地图所有玩家发送消息
    // SendMessageToMap(mapName, message);

    Logger::Info("向地图 " + mapName + " 所有玩家发送消息: " + message);
    return true;
}

bool ScriptEngine::ActionSendMsgAll(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string message = action.param1;

    // TODO: 实现向所有在线玩家发送消息
    // SendMessageToAllPlayers(message);

    Logger::Info("向所有在线玩家发送消息: " + message);
    return true;
}

bool ScriptEngine::ActionTimeRecallMob(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    int delay = action.nParam1;
    std::string monsterName = action.param2;

    // TODO: 实现定时召回怪物
    // ScheduleMonsterRecall(delay, monsterName);

    Logger::Info("定时 " + std::to_string(delay) + " 秒后召回怪物: " + monsterName);
    return true;
}

bool ScriptEngine::ActionParam1(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    // TODO: 实现参数1处理
    // ProcessParam1(action.param1);

    Logger::Info("处理参数1: " + action.param1);
    return true;
}

bool ScriptEngine::ActionParam2(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    // TODO: 实现参数2处理
    // ProcessParam2(action.param1);

    Logger::Info("处理参数2: " + action.param1);
    return true;
}

bool ScriptEngine::ActionParam3(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    // TODO: 实现参数3处理
    // ProcessParam3(action.param1);

    Logger::Info("处理参数3: " + action.param1);
    return true;
}

bool ScriptEngine::ActionAutoAddPoint(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    // TODO: 实现自动加点
    // player->AutoAddAttributePoints();

    Logger::Info("为玩家 " + player->GetCharName() + " 自动加点");
    return true;
}

bool ScriptEngine::ActionDelayExecute(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    int delay = action.nParam1;
    std::string command = action.param2;

    // TODO: 实现延迟执行
    // ScheduleDelayedExecution(delay, command);

    Logger::Info("延迟 " + std::to_string(delay) + " 秒执行命令: " + command);
    return true;
}

bool ScriptEngine::ActionRandExecute(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    int probability = action.nParam1;
    std::string command = action.param2;

    // TODO: 实现随机执行
    // if (GenerateRandom(1, 100) <= probability) {
    //     ExecuteCommand(command);
    // }

    Logger::Info("以 " + std::to_string(probability) + "% 概率执行命令: " + command);
    return true;
}

bool ScriptEngine::ActionCheckExecute(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string condition = action.param1;
    std::string command = action.param2;

    // TODO: 实现条件执行
    // if (CheckCondition(condition)) {
    //     ExecuteCommand(command);
    // }

    Logger::Info("检查条件 " + condition + " 后执行命令: " + command);
    return true;
}

bool ScriptEngine::ActionRestart(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    // TODO: 实现重启功能
    // RestartServer();

    Logger::Info("重启服务器");
    return true;
}

bool ScriptEngine::ActionPlayBGM(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string bgmFile = action.param1;

    // TODO: 实现背景音乐播放
    // player->PlayBackgroundMusic(bgmFile);

    Logger::Info("为玩家 " + player->GetCharName() + " 播放背景音乐: " + bgmFile);
    return true;
}

bool ScriptEngine::ActionPlayWav(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string wavFile = action.param1;

    // TODO: 实现音效播放
    // player->PlaySoundEffect(wavFile);

    Logger::Info("为玩家 " + player->GetCharName() + " 播放音效: " + wavFile);
    return true;
}

bool ScriptEngine::ActionDayChangeColor(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    int color = action.nParam1;

    // TODO: 实现白天颜色变化
    // ChangeDayColor(color);

    Logger::Info("改变白天颜色: " + std::to_string(color));
    return true;
}

bool ScriptEngine::ActionNightChangeColor(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    int color = action.nParam1;

    // TODO: 实现夜晚颜色变化
    // ChangeNightColor(color);

    Logger::Info("改变夜晚颜色: " + std::to_string(color));
    return true;
}

bool ScriptEngine::ActionFireBurn(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    int x = action.nParam1;
    int y = action.nParam2;
    int duration = action.nParam3;

    // TODO: 实现火焰燃烧效果
    // CreateFireEffect(x, y, duration);

    Logger::Info("在坐标(" + std::to_string(x) + "," + std::to_string(y) + ") 创建火焰效果，持续 " + std::to_string(duration) + " 秒");
    return true;
}

bool ScriptEngine::ActionLighting(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    int x = action.nParam1;
    int y = action.nParam2;
    int duration = action.nParam3;

    // TODO: 实现闪电效果
    // CreateLightningEffect(x, y, duration);

    Logger::Info("在坐标(" + std::to_string(x) + "," + std::to_string(y) + ") 创建闪电效果，持续 " + std::to_string(duration) + " 秒");
    return true;
}

bool ScriptEngine::ActionDigUp(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    // TODO: 实现挖掘向上
    // player->DigUp();

    Logger::Info("玩家 " + player->GetCharName() + " 向上挖掘");
    return true;
}

bool ScriptEngine::ActionDigDown(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    // TODO: 实现挖掘向下
    // player->DigDown();

    Logger::Info("玩家 " + player->GetCharName() + " 向下挖掘");
    return true;
}

// ==================== 工具方法实现 ====================

bool ScriptEngine::CompareValue(int value1, int value2, char method) {
    switch (method) {
        case '=': return value1 == value2;
        case '>': return value1 > value2;
        case '<': return value1 < value2;
        case '!': return value1 != value2;
        default:  return value1 >= value2; // 默认为 >=
    }
}

int ScriptEngine::ParseIntParam(const std::string& param, int defaultValue) {
    if (param.empty()) {
        return defaultValue;
    }

    try {
        return std::stoi(param);
    } catch (const std::exception&) {
        return defaultValue;
    }
}

std::string ScriptEngine::ParseStringParam(const std::string& param) {
    std::string result = param;

    // 移除引号
    if (result.length() >= 2 &&
        ((result.front() == '"' && result.back() == '"') ||
         (result.front() == '\'' && result.back() == '\''))) {
        result = result.substr(1, result.length() - 2);
    }

    return result;
}

Point ScriptEngine::FindSafeTeleportPosition(const std::string& mapName, const Point& targetPos) {
    // 获取地图管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* mapManager = gameEngine.GetMapManager();

    if (!mapManager) {
        Logger::Error("MapManager not available for safe teleport position search");
        return targetPos;
    }

    // 检查目标位置是否安全
    if (mapManager->IsValidPosition(mapName, targetPos.x, targetPos.y) &&
        mapManager->CanWalk(mapName, targetPos.x, targetPos.y)) {
        return targetPos;
    }

    // 在目标位置周围搜索安全位置
    const int maxSearchRange = 10;

    for (int range = 1; range <= maxSearchRange; range++) {
        // 搜索以目标位置为中心的正方形区域
        for (int dx = -range; dx <= range; dx++) {
            for (int dy = -range; dy <= range; dy++) {
                // 只检查边界上的点，避免重复检查内部点
                if (abs(dx) != range && abs(dy) != range) {
                    continue;
                }

                Point checkPos(targetPos.x + dx, targetPos.y + dy);

                if (mapManager->IsValidPosition(mapName, checkPos.x, checkPos.y) &&
                    mapManager->CanWalk(mapName, checkPos.x, checkPos.y)) {
                    Logger::Debug("Found safe teleport position: (" +
                                std::to_string(checkPos.x) + "," + std::to_string(checkPos.y) +
                                ") for target: (" + std::to_string(targetPos.x) + "," + std::to_string(targetPos.y) + ")");
                    return checkPos;
                }
            }
        }
    }

    // 如果找不到安全位置，返回地图中心
    const MapInfo* mapInfo = mapManager->GetMapInfo(mapName);
    if (mapInfo) {
        Point centerPos(mapInfo->width / 2, mapInfo->height / 2);

        // 检查地图中心是否安全
        if (mapManager->CanWalk(mapName, centerPos.x, centerPos.y)) {
            Logger::Warning("Using map center as safe teleport position: (" +
                          std::to_string(centerPos.x) + "," + std::to_string(centerPos.y) + ")");
            return centerPos;
        }

        // 如果地图中心也不安全，在中心周围搜索
        for (int range = 1; range <= maxSearchRange; range++) {
            for (int dx = -range; dx <= range; dx++) {
                for (int dy = -range; dy <= range; dy++) {
                    if (abs(dx) != range && abs(dy) != range) {
                        continue;
                    }

                    Point checkPos(centerPos.x + dx, centerPos.y + dy);

                    if (mapManager->IsValidPosition(mapName, checkPos.x, checkPos.y) &&
                        mapManager->CanWalk(mapName, checkPos.x, checkPos.y)) {
                        Logger::Warning("Found safe position near map center: (" +
                                      std::to_string(checkPos.x) + "," + std::to_string(checkPos.y) + ")");
                        return checkPos;
                    }
                }
            }
        }
    }

    // 最后的备选方案：返回原始目标位置
    Logger::Error("Could not find safe teleport position for map: " + mapName +
                 ", using original position: (" + std::to_string(targetPos.x) + "," + std::to_string(targetPos.y) + ")");
    return targetPos;
}

// ==================== ScriptParser实现 ====================

ScriptParser::ScriptParser() : m_lastErrorLine(-1) {
}

ScriptParser::~ScriptParser() {
}

bool ScriptParser::ParseScriptFile(const std::string& scriptFile, NPCScript& script) {
    std::ifstream file(scriptFile);
    if (!file.is_open()) {
        SetLastError("无法打开脚本文件: " + scriptFile);
        return false;
    }

    // 清空现有脚本
    script.Clear();
    script.scriptFile = scriptFile;

    // 读取所有行
    std::vector<std::string> lines;
    std::string line;
    while (std::getline(file, line)) {
        lines.push_back(line);
    }
    file.close();

    if (lines.empty()) {
        SetLastError("脚本文件为空: " + scriptFile);
        return false;
    }

    // 解析脚本块
    int lineIndex = 0;
    while (lineIndex < static_cast<int>(lines.size())) {
        std::string currentLine = Trim(lines[lineIndex]);

        // 跳过空行和注释
        if (currentLine.empty() || IsComment(currentLine)) {
            lineIndex++;
            continue;
        }

        // 检查是否是标签行
        if (IsLabel(currentLine)) {
            std::string label = ExtractLabel(currentLine);
            if (label.empty()) {
                SetLastError("无效的标签格式", lineIndex + 1);
                return false;
            }

            ScriptBlock block(label);
            lineIndex++; // 跳过标签行

            if (!ParseScriptBlock(lines, lineIndex, block)) {
                return false; // 错误信息已在ParseScriptBlock中设置
            }

            script.AddBlock(label, block);
        } else {
            SetLastError("脚本必须以标签开始", lineIndex + 1);
            return false;
        }
    }

    // 检查是否有@main标签
    if (script.GetBlock("main") == nullptr) {
        SetLastError("脚本必须包含@main标签");
        return false;
    }

    Logger::Info("成功解析脚本文件: " + scriptFile +
                 ", 共" + std::to_string(script.blocks.size()) + "个脚本块");
    return true;
}

bool ScriptParser::ParseScriptBlock(const std::vector<std::string>& lines, int& lineIndex, ScriptBlock& block) {
    enum ParseState {
        PARSE_TEXT,         // 解析对话文本
        PARSE_CONDITIONS,   // 解析条件
        PARSE_ACTIONS,      // 解析动作
        PARSE_ELSE_ACTIONS  // 解析ELSE动作
    };

    ParseState state = PARSE_TEXT;
    std::ostringstream textBuilder;

    while (lineIndex < static_cast<int>(lines.size())) {
        std::string currentLine = Trim(lines[lineIndex]);

        // 遇到下一个标签，结束当前块的解析
        if (IsLabel(currentLine)) {
            break;
        }

        // 跳过空行和注释
        if (currentLine.empty() || IsComment(currentLine)) {
            lineIndex++;
            continue;
        }

        // 检查状态转换
        if (IsConditionSection(currentLine)) {
            // 保存之前的文本
            if (state == PARSE_TEXT) {
                block.text = textBuilder.str();
                textBuilder.str("");
                textBuilder.clear();
            }
            state = PARSE_CONDITIONS;
            lineIndex++;
            continue;
        } else if (IsActionSection(currentLine)) {
            state = PARSE_ACTIONS;
            lineIndex++;
            continue;
        } else if (IsElseActionSection(currentLine)) {
            state = PARSE_ELSE_ACTIONS;
            lineIndex++;
            continue;
        }

        // 根据当前状态解析内容
        switch (state) {
            case PARSE_TEXT: {
                // 检查是否是选项
                if (IsOption(currentLine)) {
                    ScriptOption option;
                    if (ParseOptionLine(currentLine, option)) {
                        block.options.push_back(option);
                    } else {
                        SetLastError("无效的选项格式", lineIndex + 1);
                        return false;
                    }
                } else {
                    // 普通文本行
                    if (!textBuilder.str().empty()) {
                        textBuilder << "\n";
                    }
                    textBuilder << currentLine;
                }
                break;
            }
            case PARSE_CONDITIONS: {
                ScriptCondition condition;
                if (ParseConditionLine(currentLine, condition)) {
                    block.conditions.push_back(condition);
                } else {
                    SetLastError("无效的条件格式", lineIndex + 1);
                    return false;
                }
                break;
            }
            case PARSE_ACTIONS: {
                ScriptAction action;
                if (ParseActionLine(currentLine, action)) {
                    block.actions.push_back(action);
                } else {
                    SetLastError("无效的动作格式", lineIndex + 1);
                    return false;
                }
                break;
            }
            case PARSE_ELSE_ACTIONS: {
                ScriptAction action;
                if (ParseActionLine(currentLine, action)) {
                    block.elseActions.push_back(action);
                } else {
                    SetLastError("无效的ELSE动作格式", lineIndex + 1);
                    return false;
                }
                break;
            }
        }

        lineIndex++;
    }

    // 如果最后状态是PARSE_TEXT，保存文本
    if (state == PARSE_TEXT) {
        block.text = textBuilder.str();
    }

    return true;
}

bool ScriptParser::ParseConditionLine(const std::string& line, ScriptCondition& condition) {
    std::vector<std::string> params = SplitParams(line);
    if (params.empty()) {
        return false;
    }

    condition.type = params[0];

    // 根据参数数量填充参数
    if (params.size() > 1) condition.param1 = params[1];
    if (params.size() > 2) condition.param2 = params[2];
    if (params.size() > 3) condition.param3 = params[3];
    if (params.size() > 4) condition.param4 = params[4];
    if (params.size() > 5) condition.param5 = params[5];
    if (params.size() > 6) condition.param6 = params[6];

    // 解析数值参数
    if (!condition.param1.empty()) {
        try {
            condition.nParam1 = std::stoi(condition.param1);
        } catch (...) {
            condition.nParam1 = 0;
        }
    }
    if (!condition.param2.empty()) {
        try {
            condition.nParam2 = std::stoi(condition.param2);
        } catch (...) {
            condition.nParam2 = 0;
        }
    }
    if (!condition.param3.empty()) {
        try {
            condition.nParam3 = std::stoi(condition.param3);
        } catch (...) {
            condition.nParam3 = 0;
        }
    }

    // 检查比较方法
    if (!condition.param1.empty() && condition.param1.length() > 0) {
        char firstChar = condition.param1[0];
        if (firstChar == '=' || firstChar == '>' || firstChar == '<') {
            condition.method = firstChar;
        }
    }

    return ValidateCondition(condition);
}

bool ScriptParser::ParseActionLine(const std::string& line, ScriptAction& action) {
    std::vector<std::string> params = SplitParams(line);
    if (params.empty()) {
        return false;
    }

    action.type = params[0];

    // 根据参数数量填充参数
    if (params.size() > 1) action.param1 = params[1];
    if (params.size() > 2) action.param2 = params[2];
    if (params.size() > 3) action.param3 = params[3];
    if (params.size() > 4) action.param4 = params[4];
    if (params.size() > 5) action.param5 = params[5];
    if (params.size() > 6) action.param6 = params[6];

    // 解析数值参数
    if (!action.param1.empty()) {
        try {
            action.nParam1 = std::stoi(action.param1);
        } catch (...) {
            action.nParam1 = 0;
        }
    }
    if (!action.param2.empty()) {
        try {
            action.nParam2 = std::stoi(action.param2);
        } catch (...) {
            action.nParam2 = 0;
        }
    }
    if (!action.param3.empty()) {
        try {
            action.nParam3 = std::stoi(action.param3);
        } catch (...) {
            action.nParam3 = 0;
        }
    }

    return ValidateAction(action);
}

bool ScriptParser::ParseOptionLine(const std::string& line, ScriptOption& option) {
    option.text = ExtractOptionText(line);
    option.gotoLabel = ExtractOptionGoto(line);

    return !option.text.empty() && !option.gotoLabel.empty();
}

std::string ScriptParser::ParseDialogText(const std::vector<std::string>& lines, int& lineIndex) {
    std::ostringstream textBuilder;

    while (lineIndex < static_cast<int>(lines.size())) {
        std::string currentLine = Trim(lines[lineIndex]);

        // 遇到标签、条件、动作或选项，停止解析文本
        if (IsLabel(currentLine) || IsConditionSection(currentLine) ||
            IsActionSection(currentLine) || IsElseActionSection(currentLine) ||
            IsOption(currentLine)) {
            break;
        }

        // 跳过空行和注释
        if (currentLine.empty() || IsComment(currentLine)) {
            lineIndex++;
            continue;
        }

        if (!textBuilder.str().empty()) {
            textBuilder << "\n";
        }
        textBuilder << currentLine;
        lineIndex++;
    }

    return textBuilder.str();
}

// ==================== 工具方法实现 ====================

std::string ScriptParser::Trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) {
        return "";
    }
    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

std::vector<std::string> ScriptParser::Split(const std::string& str, char delimiter) {
    std::vector<std::string> result;
    std::stringstream ss(str);
    std::string item;

    while (std::getline(ss, item, delimiter)) {
        result.push_back(Trim(item));
    }

    return result;
}

std::vector<std::string> ScriptParser::SplitParams(const std::string& str) {
    std::vector<std::string> result;
    std::string current;
    bool inQuotes = false;

    for (size_t i = 0; i < str.length(); i++) {
        char c = str[i];

        if (c == '"' || c == '\'') {
            inQuotes = !inQuotes;
        } else if (c == ' ' && !inQuotes) {
            if (!current.empty()) {
                result.push_back(Trim(current));
                current.clear();
            }
        } else {
            current += c;
        }
    }

    if (!current.empty()) {
        result.push_back(Trim(current));
    }

    return result;
}

bool ScriptParser::IsLabel(const std::string& line) {
    std::string trimmed = Trim(line);
    return trimmed.length() > 2 && trimmed[0] == '[' && trimmed[1] == '@' &&
           trimmed.back() == ']';
}

bool ScriptParser::IsConditionSection(const std::string& line) {
    std::string trimmed = Trim(line);
    return trimmed == "#IF" || trimmed == "#if";
}

bool ScriptParser::IsActionSection(const std::string& line) {
    std::string trimmed = Trim(line);
    return trimmed == "#ACT" || trimmed == "#act";
}

bool ScriptParser::IsElseActionSection(const std::string& line) {
    std::string trimmed = Trim(line);
    return trimmed == "#ELSEACT" || trimmed == "#elseact";
}

bool ScriptParser::IsOption(const std::string& line) {
    std::string trimmed = Trim(line);
    return trimmed.length() > 0 && trimmed[0] == '<' &&
           trimmed.find('>') != std::string::npos;
}

bool ScriptParser::IsComment(const std::string& line) {
    std::string trimmed = Trim(line);
    return trimmed.length() > 0 && (trimmed[0] == ';' || trimmed[0] == '/' ||
           (trimmed.length() > 1 && trimmed[0] == '/' && trimmed[1] == '/'));
}

std::string ScriptParser::ExtractLabel(const std::string& line) {
    std::string trimmed = Trim(line);
    if (trimmed.length() > 3 && trimmed[0] == '[' && trimmed[1] == '@' &&
        trimmed.back() == ']') {
        return trimmed.substr(2, trimmed.length() - 3);
    }
    return "";
}

std::string ScriptParser::ExtractOptionText(const std::string& line) {
    std::string trimmed = Trim(line);
    size_t start = trimmed.find('<');
    size_t end = trimmed.find('>', start);

    if (start != std::string::npos && end != std::string::npos && end > start) {
        std::string optionPart = trimmed.substr(start + 1, end - start - 1);
        size_t slashPos = optionPart.find('/');
        if (slashPos != std::string::npos) {
            return Trim(optionPart.substr(0, slashPos));
        }
        return Trim(optionPart);
    }

    return "";
}

std::string ScriptParser::ExtractOptionGoto(const std::string& line) {
    std::string trimmed = Trim(line);
    size_t start = trimmed.find('<');
    size_t end = trimmed.find('>', start);

    if (start != std::string::npos && end != std::string::npos && end > start) {
        std::string optionPart = trimmed.substr(start + 1, end - start - 1);
        size_t slashPos = optionPart.find('/');
        if (slashPos != std::string::npos) {
            std::string gotoLabel = Trim(optionPart.substr(slashPos + 1));
            // 移除@前缀（如果有）
            if (!gotoLabel.empty() && gotoLabel[0] == '@') {
                gotoLabel = gotoLabel.substr(1);
            }
            return gotoLabel;
        }
    }

    return "";
}

void ScriptParser::SetLastError(const std::string& error, int lineNumber) {
    m_lastError = error;
    m_lastErrorLine = lineNumber;
    Logger::Error("脚本解析错误: " + error +
                  (lineNumber > 0 ? " (行号: " + std::to_string(lineNumber) + ")" : ""));
}

// ==================== 验证方法实现 ====================

bool ScriptParser::ValidateCondition(const ScriptCondition& condition) {
    // 基础条件类型验证
    static const std::vector<std::string> validConditions = {
        // 玩家属性条件
        "CHECKLEVEL", "CHECKJOB", "CHECKGOLD", "CHECKITEM", "CHECKBAGSIZE",
        "CHECKDC", "CHECKMC", "CHECKSC", "CHECKHP", "CHECKMP", "CHECKEXP",
        "CHECKPKPOINT", "CHECKCREDITPOINT", "CHECKSKILL", "CHECKGENDER",

        // 游戏状态条件
        "CHECKMARRY", "CHECKMASTER", "CHECKGUILD", "CHECKGUILDRANK",
        "CHECKCASTLEOWNER", "CHECKSAFEZONE", "CHECKMAPNAME", "CHECKTIME",
        "CHECKDATE", "CHECKDAY", "CHECKHOUR", "CHECKMIN",

        // 高级条件
        "CHECKVAR", "CHECKNAMELIST", "CHECKIPLIST", "CHECKACCOUNTLIST",
        "CHECKSLAVECOUNT", "CHECKONLINE", "CHECKDURAEVA", "CHECKBAGCOUNT",
        "CHECKITEMW", "CHECKITEMTYPE", "CHECKITEMADDVALUE", "CHECKITEMLEVEL",

        // 装备条件
        "CHECKWEARING", "CHECKWEAPON", "CHECKARMOR", "CHECKNECKLACE",
        "CHECKHELMET", "CHECKRING_L", "CHECKRING_R", "CHECKARMRING_L",
        "CHECKARMRING_R", "CHECKBELT", "CHECKBOOTS", "CHECKCHARM",

        // 特殊条件
        "CHECKGROUPCOUNT", "CHECKGROUPLEADER", "CHECKPOSEDIR", "CHECKPOSELEVEL",
        "CHECKCONTAINSTEXT", "CHECKSTRINGLIST", "CHECKRANGECOUNT", "CHECKMONCOUNT",
        "CHECKHUMCOUNT", "CHECKISADMIN", "CHECKACCOUNTIPCOUNT", "CHECKIPCOUNT",

        // 补充的高级条件
        "CHECKCASTLEMASTER", "CHECKGUILDMASTER", "CHECKBUILDPOINT", "CHECKPLAYERCOUNT",
        "CHECKGAMEGOLD", "CHECKGAMEPOINT"
    };

    // 检查条件类型是否有效
    auto it = std::find(validConditions.begin(), validConditions.end(), condition.type);
    if (it == validConditions.end()) {
        Logger::Warning("未知的条件类型: " + condition.type);
        return false; // 暂时允许未知条件，便于扩展
    }

    return true;
}

bool ScriptParser::ValidateAction(const ScriptAction& action) {
    // 基础动作类型验证
    static const std::vector<std::string> validActions = {
        // 物品操作
        "GIVE", "TAKE", "GIVEEXP", "TAKEGOLD", "GIVEGOLD", "GIVEITEM",
        "TAKEITEM", "GIVESKILL", "TAKESKILL", "GIVECREDITPOINT", "TAKECREDITPOINT",

        // 玩家属性操作
        "CHANGELEVEL", "CHANGEJOB", "CHANGEGENDER", "CHANGEPKPOINT",
        "CHANGEMODE", "CHANGEPERMISSION", "CHANGEEXP", "CHANGEHP", "CHANGEMP",
        "CHANGEDC", "CHANGEMC", "CHANGESC", "CHANGENAME", "CHANGEPASSWORD",

        // 传送和移动
        "MAP", "MAPMOVE", "RECALL", "REGOTO", "TIMERECALL", "BREAKTIMERECALL",
        "RECALLMOB", "KICKUSER", "KILLUSER", "CHANGEPOS", "RANDOMPOS",

        // 游戏功能
        "OPENMERCHANT", "OPENREPAIR", "OPENSTORAGE", "OPENGUILD", "SENDMSG",
        "MESSAGEBOX", "PLAYDICE", "OPENBOX", "CLOSEBOX", "OPENBOOK",
        "CLOSEBOOK", "OPENBIGDIALOGBOX", "CLOSEBIGDIALOGBOX",

        // 怪物和宠物
        "MONGEN", "KILLMONSTER", "KILLSLAVE", "RECALLSLAVE", "CLEARSLAVE",
        "MONGENEX", "MOBPLACE", "MOBCOUNT", "CLEARMON", "CLEARITEM",

        // 高级功能
        "GMEXECUTE", "ADDNAMELIST", "DELNAMELIST", "ADDIPLIST", "DELIPLIST",
        "ADDACCOUNTLIST", "DELACCOUNTLIST", "SETVAR", "CALCVAR", "SAVEVAR",
        "LOADVAR", "ADDGUILD", "DELGUILD", "GUILDWAR", "ENDGUILDWAR",

        // 特殊功能
        "HAIR", "TAKEW", "TAKEON", "SENDMSGUSER", "SENDMSGMAP", "SENDMSGALL",
        "TIMERECALLMOB", "PARAM1", "PARAM2", "PARAM3", "AUTOADDPOINT",
        "DELAYEXECUTE", "RANDEXECUTE", "CHECKEXECUTE", "GOTO", "BREAK",
        "EXIT", "CLOSE", "RESTART", "PLAYBGM", "PLAYWAV", "DAYCHANGECOLOR",
        "NIGHTCHANGECOLOR", "FIREBURN", "LIGHTING", "DIGUP", "DIGDOWN"
    };

    // 检查动作类型是否有效
    auto it = std::find(validActions.begin(), validActions.end(), action.type);
    if (it == validActions.end()) {
        Logger::Warning("未知的动作类型: " + action.type);
        return false; // 暂时允许未知动作，便于扩展
    }

    return true;
}

// ==================== 变量替换实现 ====================

std::string ScriptParser::ReplaceVariables(const std::string& text, PlayObject* player, NPC* npc) {
    std::string result = text;

    if (!player) {
        return result;
    }

    // 替换玩家相关变量
    size_t pos = 0;
    while ((pos = result.find("<$USERNAME>", pos)) != std::string::npos) {
        result.replace(pos, 11, player->GetCharName());
        pos += player->GetCharName().length();
    }

    pos = 0;
    while ((pos = result.find("<$LEVEL>", pos)) != std::string::npos) {
        result.replace(pos, 8, std::to_string(player->GetLevel()));
        pos += std::to_string(player->GetLevel()).length();
    }

    pos = 0;
    while ((pos = result.find("<$JOB>", pos)) != std::string::npos) {
        std::string jobName = "战士";
        switch (player->GetJob()) {
            case JobType::WARRIOR: jobName = "战士"; break;
            case JobType::WIZARD: jobName = "法师"; break;
            case JobType::TAOIST: jobName = "道士"; break;
            default: jobName = "未知"; break;
        }
        result.replace(pos, 6, jobName);
        pos += jobName.length();
    }

    pos = 0;
    while ((pos = result.find("<$GOLD>", pos)) != std::string::npos) {
        result.replace(pos, 7, std::to_string(player->GetGold()));
        pos += std::to_string(player->GetGold()).length();
    }

    pos = 0;
    while ((pos = result.find("<$GUILDNAME>", pos)) != std::string::npos) {
        std::string guildName = "无"; // 暂时硬编码，等待行会系统实现
        result.replace(pos, 12, guildName);
        pos += guildName.length();
    }

    // 替换NPC相关变量
    if (npc) {
        pos = 0;
        while ((pos = result.find("<$NPCNAME>", pos)) != std::string::npos) {
            result.replace(pos, 10, npc->GetCharName());
            pos += npc->GetCharName().length();
        }
    }

    // 替换时间变量
    time_t now = time(nullptr);
    struct tm* timeinfo = localtime(&now);

    pos = 0;
    while ((pos = result.find("<$HOUR>", pos)) != std::string::npos) {
        result.replace(pos, 7, std::to_string(timeinfo->tm_hour));
        pos += std::to_string(timeinfo->tm_hour).length();
    }

    pos = 0;
    while ((pos = result.find("<$MIN>", pos)) != std::string::npos) {
        result.replace(pos, 6, std::to_string(timeinfo->tm_min));
        pos += std::to_string(timeinfo->tm_min).length();
    }

    pos = 0;
    while ((pos = result.find("<$DAY>", pos)) != std::string::npos) {
        result.replace(pos, 6, std::to_string(timeinfo->tm_mday));
        pos += std::to_string(timeinfo->tm_mday).length();
    }

    // 替换换行符
    pos = 0;
    while ((pos = result.find("\\", pos)) != std::string::npos) {
        if (pos + 1 < result.length() && result[pos + 1] == '\\') {
            result.replace(pos, 2, "\n");
            pos += 1;
        } else {
            pos += 1;
        }
    }

    return result;
}

// ==================== 变量管理实现 ====================

bool ScriptEngine::SetScriptVar(const std::string& varName, int value, bool persistent) {
    if (varName.empty()) {
        SetLastError("变量名不能为空");
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_varsMutex);

    ScriptVar& var = m_scriptVars[varName];
    var.name = varName;
    var.value = value;
    var.persistent = persistent;
    var.lastModified = GetCurrentTime();

    return true;
}

int ScriptEngine::GetScriptVar(const std::string& varName) const {
    if (varName.empty()) {
        return 0;
    }

    std::shared_lock<std::shared_mutex> lock(m_varsMutex);

    auto it = m_scriptVars.find(varName);
    if (it != m_scriptVars.end()) {
        return it->second.value;
    }

    return 0; // 变量不存在时返回0
}

bool ScriptEngine::SaveScriptVar(const std::string& varName) {
    if (varName.empty()) {
        SetLastError("变量名不能为空");
        return false;
    }

    std::shared_lock<std::shared_mutex> lock(m_varsMutex);

    auto it = m_scriptVars.find(varName);
    if (it == m_scriptVars.end()) {
        SetLastError("变量不存在: " + varName);
        return false;
    }

    // TODO: 实现变量持久化到文件或数据库
    // 这里可以将变量保存到配置文件或数据库中
    Logger::Debug("保存变量到持久化存储: " + varName + " = " + std::to_string(it->second.value));

    return true;
}

bool ScriptEngine::LoadScriptVar(const std::string& varName) {
    if (varName.empty()) {
        SetLastError("变量名不能为空");
        return false;
    }

    // TODO: 实现从文件或数据库加载变量
    // 这里可以从配置文件或数据库中加载变量值
    Logger::Debug("从持久化存储加载变量: " + varName);

    // 临时实现：设置默认值
    SetScriptVar(varName, 0, true);

    return true;
}

void ScriptEngine::ClearScriptVars() {
    std::unique_lock<std::shared_mutex> lock(m_varsMutex);
    m_scriptVars.clear();
    Logger::Info("清空所有脚本变量");
}

// ==================== 列表管理实现 ====================

bool ListManager::AddToNameList(const std::string& listName, const std::string& playerName) {
    if (listName.empty() || playerName.empty()) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_listsMutex);
    m_nameLists[listName].insert(playerName);

    Logger::Debug("添加玩家到名单: " + playerName + " -> " + listName);
    return true;
}

bool ListManager::RemoveFromNameList(const std::string& listName, const std::string& playerName) {
    if (listName.empty() || playerName.empty()) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_listsMutex);

    auto listIt = m_nameLists.find(listName);
    if (listIt != m_nameLists.end()) {
        auto playerIt = listIt->second.find(playerName);
        if (playerIt != listIt->second.end()) {
            listIt->second.erase(playerIt);
            Logger::Debug("从名单删除玩家: " + playerName + " <- " + listName);
            return true;
        }
    }

    return false;
}

bool ListManager::IsInNameList(const std::string& listName, const std::string& playerName) const {
    if (listName.empty() || playerName.empty()) {
        return false;
    }

    std::shared_lock<std::shared_mutex> lock(m_listsMutex);

    auto listIt = m_nameLists.find(listName);
    if (listIt != m_nameLists.end()) {
        return listIt->second.find(playerName) != listIt->second.end();
    }

    return false;
}

bool ListManager::AddToIPList(const std::string& listName, const std::string& ip) {
    if (listName.empty() || ip.empty()) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_listsMutex);
    m_ipLists[listName].insert(ip);

    Logger::Debug("添加IP到列表: " + ip + " -> " + listName);
    return true;
}

bool ListManager::RemoveFromIPList(const std::string& listName, const std::string& ip) {
    if (listName.empty() || ip.empty()) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_listsMutex);

    auto listIt = m_ipLists.find(listName);
    if (listIt != m_ipLists.end()) {
        auto ipIt = listIt->second.find(ip);
        if (ipIt != listIt->second.end()) {
            listIt->second.erase(ipIt);
            Logger::Debug("从IP列表删除: " + ip + " <- " + listName);
            return true;
        }
    }

    return false;
}

bool ListManager::IsInIPList(const std::string& listName, const std::string& ip) const {
    if (listName.empty() || ip.empty()) {
        return false;
    }

    std::shared_lock<std::shared_mutex> lock(m_listsMutex);

    auto listIt = m_ipLists.find(listName);
    if (listIt != m_ipLists.end()) {
        return listIt->second.find(ip) != listIt->second.end();
    }

    return false;
}

bool ListManager::AddToAccountList(const std::string& listName, const std::string& account) {
    if (listName.empty() || account.empty()) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_listsMutex);
    m_accountLists[listName].insert(account);

    Logger::Debug("添加账号到列表: " + account + " -> " + listName);
    return true;
}

bool ListManager::RemoveFromAccountList(const std::string& listName, const std::string& account) {
    if (listName.empty() || account.empty()) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_listsMutex);

    auto listIt = m_accountLists.find(listName);
    if (listIt != m_accountLists.end()) {
        auto accountIt = listIt->second.find(account);
        if (accountIt != listIt->second.end()) {
            listIt->second.erase(accountIt);
            Logger::Debug("从账号列表删除: " + account + " <- " + listName);
            return true;
        }
    }

    return false;
}

bool ListManager::IsInAccountList(const std::string& listName, const std::string& account) const {
    if (listName.empty() || account.empty()) {
        return false;
    }

    std::shared_lock<std::shared_mutex> lock(m_listsMutex);

    auto listIt = m_accountLists.find(listName);
    if (listIt != m_accountLists.end()) {
        return listIt->second.find(account) != listIt->second.end();
    }

    return false;
}

bool ListManager::SaveLists() {
    std::shared_lock<std::shared_mutex> lock(m_listsMutex);

    // TODO: 实现列表持久化到文件
    // 可以保存为JSON或INI格式的配置文件
    Logger::Info("保存所有列表到持久化存储");

    return true;
}

bool ListManager::LoadLists() {
    std::unique_lock<std::shared_mutex> lock(m_listsMutex);

    // TODO: 实现从文件加载列表
    // 从配置文件中加载各种列表数据
    Logger::Info("从持久化存储加载所有列表");

    return true;
}

// ==================== 补充的高级条件检查实现 ====================

bool ScriptEngine::CheckCastleMaster(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    std::string castleName = condition.param1;

    // 如果没有指定城堡名称，检查是否是任意城堡的主人
    if (castleName.empty()) {
        // TODO: 检查玩家是否是任意城堡的主人
        // 需要与CastleManager集成
        return false;
    }

    // 检查指定城堡的主人
    // TODO: 实现城堡主人检查
    // Castle* castle = CastleManager::GetInstance().FindCastle(castleName);
    // if (castle) {
    //     Guild* playerGuild = player->GetGuild();
    //     return castle->IsMasterGuild(playerGuild);
    // }

    Logger::Info("CheckCastleMaster: " + castleName + " for player: " + player->GetCharName());
    return false; // 临时返回false，等待与CastleManager集成
}

bool ScriptEngine::CheckGuildMaster(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现行会会长检查
    // return player->IsGuildMaster();

    Logger::Warning("CheckGuildMaster 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckBuildPoint(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现建筑点数检查
    // int buildPoint = player->GetBuildPoint();
    // int requiredPoint = condition.nParam1;
    // char method = condition.method;
    // return CompareValue(buildPoint, requiredPoint, method);

    Logger::Warning("CheckBuildPoint 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckPlayerCount(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现在线玩家数量检查
    // int playerCount = GetOnlinePlayerCount();
    // int requiredCount = condition.nParam1;
    // char method = condition.method;
    // return CompareValue(playerCount, requiredCount, method);

    Logger::Warning("CheckPlayerCount 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckGameGold(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现游戏币检查
    // int gameGold = player->GetGameGold();
    // int requiredGold = condition.nParam1;
    // char method = condition.method;
    // return CompareValue(gameGold, requiredGold, method);

    Logger::Warning("CheckGameGold 尚未完全实现");
    return false; // 临时返回false
}

bool ScriptEngine::CheckGamePoint(PlayObject* player, const ScriptCondition& condition) {
    if (!player) return false;

    // TODO: 实现游戏点数检查
    // int gamePoint = player->GetGamePoint();
    // int requiredPoint = condition.nParam1;
    // char method = condition.method;
    // return CompareValue(gamePoint, requiredPoint, method);

    Logger::Warning("CheckGamePoint 尚未完全实现");
    return false; // 临时返回false
}

// ==================== 补充的重要动作实现 ====================

bool ScriptEngine::ActionServerNotice(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string message = action.param1;
    int noticeType = action.nParam2; // 0=普通公告, 1=滚动公告, 2=弹窗公告

    // TODO: 实现服务器公告
    // SendServerNotice(message, noticeType);

    Logger::Info("发送服务器公告: " + message + " (类型: " + std::to_string(noticeType) + ")");
    return true;
}

bool ScriptEngine::ActionSystemBroadcast(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string message = action.param1;
    int broadcastType = action.nParam2; // 0=系统广播, 1=GM广播

    // TODO: 实现系统广播
    // SendSystemBroadcast(message, broadcastType);

    Logger::Info("发送系统广播: " + message + " (类型: " + std::to_string(broadcastType) + ")");
    return true;
}

bool ScriptEngine::ActionBuildCastle(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string castleName = action.param1;
    std::string mapName = action.param2;
    int x = action.nParam3;
    int y = action.nParam4;

    // TODO: 实现城堡建造逻辑
    // 这需要与CastleManager和地图系统集成
    // CastleManager& castleManager = CastleManager::GetInstance();
    // if (castleManager.FindCastle(castleName)) {
    //     Logger::Warning("Castle already exists: " + castleName);
    //     return false;
    // }

    // 创建新城堡的逻辑
    // auto newCastle = std::make_unique<Castle>(castleName);
    // newCastle->SetMapName(mapName);
    // newCastle->SetPosition(x, y);
    // castleManager.AddCastle(std::move(newCastle));

    Logger::Info("建造城堡: " + castleName + " 在地图 " + mapName + " (" + std::to_string(x) + "," + std::to_string(y) + ")");

    // 发送系统消息
    std::string message = "新城堡 " + castleName + " 已在 " + mapName + " 建造完成！";
    // TODO: 发送全服消息
    // SendSystemMessage(message);

    return true;
}

bool ScriptEngine::ActionRepairCastle(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string castleName = action.param1;
    int repairType = action.nParam2; // 0=修复城门, 1=修复城墙, 2=全部修复

    // TODO: 实现城堡修复逻辑
    // Castle* castle = CastleManager::GetInstance().FindCastle(castleName);
    // if (!castle) {
    //     Logger::Warning("Castle not found for repair: " + castleName);
    //     return false;
    // }

    // bool repairResult = false;
    // switch (repairType) {
    //     case 0: // 修复城门
    //         repairResult = castle->RepairDoor();
    //         break;
    //     case 1: // 修复城墙
    //         repairResult = castle->RepairWall(0) && castle->RepairWall(1) && castle->RepairWall(2);
    //         break;
    //     case 2: // 全部修复
    //         repairResult = castle->RepairDoor();
    //         repairResult &= castle->RepairWall(0) && castle->RepairWall(1) && castle->RepairWall(2);
    //         // 修复守卫和弓箭手
    //         for (int i = 0; i < 12; i++) {
    //             castle->RepairUnit(DefenseUnitType::ARCHER, i);
    //         }
    //         for (int i = 0; i < 4; i++) {
    //             castle->RepairUnit(DefenseUnitType::GUARD, i);
    //         }
    //         break;
    //     default:
    //         Logger::Warning("Invalid repair type: " + std::to_string(repairType));
    //         return false;
    // }

    std::string repairTypeStr;
    switch (repairType) {
        case 0: repairTypeStr = "城门"; break;
        case 1: repairTypeStr = "城墙"; break;
        case 2: repairTypeStr = "全部"; break;
        default: repairTypeStr = "未知"; break;
    }

    Logger::Info("修复城堡: " + castleName + " (类型: " + repairTypeStr + ")");

    // 发送修复完成消息
    std::string message = "城堡 " + castleName + " 的 " + repairTypeStr + " 修复完成！";
    // TODO: 发送消息给相关玩家

    return true;
}

bool ScriptEngine::ActionUpgradeCastle(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    std::string castleName = action.param1;
    int upgradeLevel = action.nParam2;

    // TODO: 实现城堡升级逻辑
    // Castle* castle = CastleManager::GetInstance().FindCastle(castleName);
    // if (!castle) {
    //     Logger::Warning("Castle not found for upgrade: " + castleName);
    //     return false;
    // }

    // // 检查升级条件
    // int currentLevel = castle->GetTechLevel();
    // if (upgradeLevel <= currentLevel) {
    //     Logger::Warning("Invalid upgrade level: " + std::to_string(upgradeLevel) +
    //                    " (current: " + std::to_string(currentLevel) + ")");
    //     return false;
    // }

    // // 检查升级所需资源
    // int requiredGold = (upgradeLevel - currentLevel) * 10000; // 每级需要10000金币
    // if (castle->GetTotalGold() < requiredGold) {
    //     Logger::Warning("Insufficient gold for castle upgrade");
    //     return false;
    // }

    // // 执行升级
    // castle->SetTechLevel(upgradeLevel);
    // castle->WithdrawGold(nullptr, requiredGold); // 扣除金币

    // // 升级可能带来的好处
    // int powerIncrease = (upgradeLevel - currentLevel) * 100;
    // castle->SetPower(castle->GetPower() + powerIncrease);

    Logger::Info("升级城堡: " + castleName + " 到等级 " + std::to_string(upgradeLevel));

    // 发送升级完成消息
    std::string message = "城堡 " + castleName + " 已升级到等级 " + std::to_string(upgradeLevel) + "！";
    // TODO: 发送全服消息
    // SendSystemMessage(message);

    return true;
}

bool ScriptEngine::ActionChangeGameGold(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    char operation = action.param1.empty() ? '+' : action.param1[0];
    int amount = action.nParam2;

    // TODO: 实现游戏币修改
    // int currentGameGold = player->GetGameGold();
    // int newGameGold = currentGameGold;
    // switch (operation) {
    //     case '+': newGameGold = currentGameGold + amount; break;
    //     case '-': newGameGold = currentGameGold - amount; break;
    //     case '=': newGameGold = amount; break;
    // }
    // player->SetGameGold(newGameGold);

    Logger::Info("修改游戏币: " + std::string(1, operation) + std::to_string(amount));
    return true;
}

bool ScriptEngine::ActionChangeGamePoint(PlayObject* player, const ScriptAction& action) {
    if (!player) return false;

    char operation = action.param1.empty() ? '+' : action.param1[0];
    int amount = action.nParam2;

    // TODO: 实现游戏点数修改
    // int currentGamePoint = player->GetGamePoint();
    // int newGamePoint = currentGamePoint;
    // switch (operation) {
    //     case '+': newGamePoint = currentGamePoint + amount; break;
    //     case '-': newGamePoint = currentGamePoint - amount; break;
    //     case '=': newGamePoint = amount; break;
    // }
    // player->SetGamePoint(newGamePoint);

    Logger::Info("修改游戏点数: " + std::string(1, operation) + std::to_string(amount));
    return true;
}

} // namespace MirServer

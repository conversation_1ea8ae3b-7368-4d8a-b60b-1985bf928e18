// DBServer.cpp - 数据库服务器实现
// TODO: 实现数据库服务器

#include "DBServer.h"
#include "../Common/Logger.h"
#include "../Common/Types.h"
#include "../Protocol/MessageConverter.h"
#include "../Protocol/NetworkManager.h"
#include <fstream>
#include <filesystem>
#include <chrono>
#include <cstring>
#include <algorithm>
#include <atomic>
#include <set>

#ifdef _WIN32
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#else
#include <sys/stat.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <fcntl.h>
#include <unistd.h>
#endif

// 使用MirServer命名空间
using namespace MirServer;
using namespace MirServer::Protocol;  // 添加Protocol命名空间

// 日志宏定义
#define LOG_INFO(msg) MirServer::Logger::Info(msg)
#define LOG_ERROR(msg) MirServer::Logger::Error(msg)
#define LOG_WARNING(msg) MirServer::Logger::Warning(msg)
#define LOG_DEBUG(msg) MirServer::Logger::Debug(msg)

namespace DBServer {

// 数据库文件描述
static const char* DB_HEADER_DESC = "Mir2 Game Database File 2024/01/01";
static const char* DB_IDX_HEADER_DESC = "Mir2 Game Database Index File 2024/01/01";

// 辅助函数：发送数据到客户端
static void SendToClient(std::shared_ptr<ClientConnection> conn, const std::string& data) {
    if (conn) {
        conn->Send(data.data(), data.size());
    }
}

// ==================== HumanDB 实现 ====================

HumanDB::HumanDB(const std::string& sFileName) 
    : m_sDBFileName(sFileName), m_boOpened(false) {
    m_sIdxFileName = sFileName + ".idx";
    memset(&m_Header, 0, sizeof(DBHeader));
}

HumanDB::~HumanDB() {
    if (m_boOpened) {
        Close();
    }
}

bool HumanDB::Open() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_boOpened) return true;
    
    // 检查数据库文件是否存在
    std::ifstream dbFile(m_sDBFileName, std::ios::binary);
    if (!dbFile.is_open()) {
        // 创建新数据库
        std::ofstream newFile(m_sDBFileName, std::ios::binary);
        if (!newFile.is_open()) {
            LOG_ERROR("Failed to create database file: " + m_sDBFileName);
            return false;
        }
        
        // 初始化文件头
        strcpy(m_Header.sDesc, DB_HEADER_DESC);
        m_Header.nHumCount = 0;
        m_Header.nLastIndex = 0;
        m_Header.dLastDate = std::chrono::system_clock::now().time_since_epoch().count();
        m_Header.dUpdateDate = m_Header.dLastDate;
        
        newFile.write(reinterpret_cast<char*>(&m_Header), sizeof(DBHeader));
        newFile.close();
        
        LOG_INFO("Created new database file: " + m_sDBFileName);
    } else {
        // 读取文件头
        dbFile.read(reinterpret_cast<char*>(&m_Header), sizeof(DBHeader));
        dbFile.close();
    }
    
    // 加载索引
    if (!LoadIndex()) {
        LoadQuickList();
        SaveIndex();
    }
    
    m_boOpened = true;
    LOG_INFO("Database opened successfully: " + m_sDBFileName);
    return true;
}

void HumanDB::Close() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_boOpened) return;
    
    SaveIndex();
    m_QuickList.clear();
    m_QuickIDList.clear();
    m_DeletedList.clear();
    m_boOpened = false;
    
    LOG_INFO("Database closed: " + m_sDBFileName);
}

bool HumanDB::LoadIndex() {
    std::ifstream idxFile(m_sIdxFileName, std::ios::binary);
    if (!idxFile.is_open()) return false;
    
    // 读取索引文件头
    char desc[40];
    idxFile.read(desc, sizeof(desc));
    
    // 读取快速列表
    int32_t nCount;
    idxFile.read(reinterpret_cast<char*>(&nCount), sizeof(nCount));
    
    for (int i = 0; i < nCount; i++) {
        char name[15];
        int32_t index;
        idxFile.read(name, sizeof(name));
        idxFile.read(reinterpret_cast<char*>(&index), sizeof(index));
        m_QuickList[std::string(name)] = index;
    }
    
    // 读取ID列表
    idxFile.read(reinterpret_cast<char*>(&nCount), sizeof(nCount));
    
    for (int i = 0; i < nCount; i++) {
        char account[11];
        int32_t chrCount;
        idxFile.read(account, sizeof(account));
        idxFile.read(reinterpret_cast<char*>(&chrCount), sizeof(chrCount));
        
        std::vector<QuickIndex> chrList;
        for (int j = 0; j < chrCount; j++) {
            QuickIndex idx;
            char chrName[15];
            idxFile.read(chrName, sizeof(chrName));
            idxFile.read(reinterpret_cast<char*>(&idx.nIndex), sizeof(idx.nIndex));
            idxFile.read(reinterpret_cast<char*>(&idx.nSelectID), sizeof(idx.nSelectID));
            idx.sChrName = std::string(chrName);
            chrList.push_back(idx);
        }
        m_QuickIDList[std::string(account)] = chrList;
    }
    
    // 读取删除列表
    idxFile.read(reinterpret_cast<char*>(&nCount), sizeof(nCount));
    for (int i = 0; i < nCount; i++) {
        int32_t index;
        idxFile.read(reinterpret_cast<char*>(&index), sizeof(index));
        m_DeletedList.push_back(index);
    }
    
    idxFile.close();
    return true;
}

bool HumanDB::SaveIndex() {
    std::ofstream idxFile(m_sIdxFileName, std::ios::binary);
    if (!idxFile.is_open()) return false;
    
    // 写入索引文件头
    idxFile.write(DB_IDX_HEADER_DESC, 40);
    
    // 写入快速列表
    int32_t nCount = static_cast<int32_t>(m_QuickList.size());
    idxFile.write(reinterpret_cast<char*>(&nCount), sizeof(nCount));
    
    for (const auto& pair : m_QuickList) {
        char name[15] = {0};
        strncpy(name, pair.first.c_str(), sizeof(name) - 1);
        idxFile.write(name, sizeof(name));
        idxFile.write(reinterpret_cast<const char*>(&pair.second), sizeof(pair.second));
    }
    
    // 写入ID列表
    nCount = static_cast<int32_t>(m_QuickIDList.size());
    idxFile.write(reinterpret_cast<char*>(&nCount), sizeof(nCount));
    
    for (const auto& pair : m_QuickIDList) {
        char account[11] = {0};
        strncpy(account, pair.first.c_str(), sizeof(account) - 1);
        int32_t chrCount = static_cast<int32_t>(pair.second.size());
        
        idxFile.write(account, sizeof(account));
        idxFile.write(reinterpret_cast<char*>(&chrCount), sizeof(chrCount));
        
        for (const auto& idx : pair.second) {
            char chrName[15] = {0};
            strncpy(chrName, idx.sChrName.c_str(), sizeof(chrName) - 1);
            idxFile.write(chrName, sizeof(chrName));
            idxFile.write(reinterpret_cast<const char*>(&idx.nIndex), sizeof(idx.nIndex));
            idxFile.write(reinterpret_cast<const char*>(&idx.nSelectID), sizeof(idx.nSelectID));
        }
    }
    
    // 写入删除列表
    nCount = static_cast<int32_t>(m_DeletedList.size());
    idxFile.write(reinterpret_cast<char*>(&nCount), sizeof(nCount));
    for (int32_t index : m_DeletedList) {
        idxFile.write(reinterpret_cast<char*>(&index), sizeof(index));
    }
    
    idxFile.close();
    return true;
}

bool HumanDB::LoadQuickList() {
    std::ifstream dbFile(m_sDBFileName, std::ios::binary);
    if (!dbFile.is_open()) return false;
    
    // 跳过文件头
    dbFile.seekg(sizeof(DBHeader));
    
    m_QuickList.clear();
    m_QuickIDList.clear();
    m_DeletedList.clear();
    
    for (int32_t i = 0; i < m_Header.nHumCount; i++) {
        HumDataInfo record;
        dbFile.read(reinterpret_cast<char*>(&record), sizeof(HumDataInfo));
        
        if (!record.isDead) {
            // 添加到快速列表
            m_QuickList[record.charName] = i;
            
            // 添加到ID列表
            QuickIndex idx;
            idx.sChrName = record.charName;
            idx.nIndex = i;
            idx.nSelectID = i;
            m_QuickIDList[record.account].push_back(idx);
        } else {
            // 添加到删除列表
            m_DeletedList.push_back(i);
        }
    }
    
    dbFile.close();
    return true;
}

int32_t HumanDB::Index(const std::string& sName) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_QuickList.find(sName);
    if (it != m_QuickList.end()) {
        return it->second;
    }
    return -1;
}

bool HumanDB::Get(int32_t nIndex, HumDataInfo& rcd) {
    std::lock_guard<std::mutex> lock(m_mutex);
    return GetRecord(nIndex, rcd);
}

bool HumanDB::GetRecord(int32_t nIndex, HumDataInfo& rcd) {
    if (nIndex < 0 || nIndex >= m_Header.nHumCount) return false;
    
    std::ifstream dbFile(m_sDBFileName, std::ios::binary);
    if (!dbFile.is_open()) return false;
    
    // 定位到记录位置
    dbFile.seekg(sizeof(DBHeader) + nIndex * sizeof(HumDataInfo));
    dbFile.read(reinterpret_cast<char*>(&rcd), sizeof(HumDataInfo));
    dbFile.close();
    
    return !rcd.isDead;
}

bool HumanDB::Add(HumDataInfo& rcd) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 检查角色名是否已存在
    if (m_QuickList.find(rcd.charName) != m_QuickList.end()) {
        return false;
    }
    
    int32_t nIndex;
    if (!m_DeletedList.empty()) {
        // 使用已删除的位置
        nIndex = m_DeletedList.back();
        m_DeletedList.pop_back();
    } else {
        // 使用新位置
        nIndex = m_Header.nHumCount++;
    }
    
    // 设置记录信息
    rcd.isDead = false;
    
    // 更新记录
    if (UpdateRecord(nIndex, rcd, true)) {
        // 添加到快速列表
        m_QuickList[rcd.charName] = nIndex;
        
        // 添加到ID列表
        QuickIndex idx;
        idx.sChrName = rcd.charName;
        idx.nIndex = nIndex;
        idx.nSelectID = nIndex;
        m_QuickIDList[rcd.account].push_back(idx);
        
        return true;
    }
    
    return false;
}

bool HumanDB::Update(int32_t nIndex, const HumDataInfo& rcd) {
    std::lock_guard<std::mutex> lock(m_mutex);
    return UpdateRecord(nIndex, rcd, false);
}

bool HumanDB::UpdateRecord(int32_t nIndex, const HumDataInfo& rcd, bool boNew) {
    std::fstream dbFile(m_sDBFileName, std::ios::binary | std::ios::in | std::ios::out);
    if (!dbFile.is_open()) return false;
    
    // 更新文件头
    m_Header.dUpdateDate = std::chrono::system_clock::now().time_since_epoch().count();
    dbFile.seekp(0);
    dbFile.write(reinterpret_cast<char*>(&m_Header), sizeof(DBHeader));
    
    // 定位到记录位置
    dbFile.seekp(sizeof(DBHeader) + nIndex * sizeof(HumDataInfo));
    dbFile.write(reinterpret_cast<const char*>(&rcd), sizeof(HumDataInfo));
    dbFile.close();
    
    return true;
}

bool HumanDB::Delete(const std::string& sName) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_QuickList.find(sName);
    if (it == m_QuickList.end()) return false;
    
    int32_t nIndex = it->second;
    
    // 获取记录信息
    HumDataInfo rcd;
    if (!GetRecord(nIndex, rcd)) return false;
    
    // 标记为删除
    if (DeleteRecord(nIndex)) {
        // 从快速列表删除
        m_QuickList.erase(it);
        
        // 从ID列表删除
        auto& chrList = m_QuickIDList[rcd.account];
        chrList.erase(
            std::remove_if(chrList.begin(), chrList.end(),
                [&sName](const QuickIndex& idx) { return idx.sChrName == sName; }),
            chrList.end()
        );
        
        // 添加到删除列表
        m_DeletedList.push_back(nIndex);
        
        return true;
    }
    
    return false;
}

bool HumanDB::DeleteRecord(int32_t nIndex) {
    std::fstream dbFile(m_sDBFileName, std::ios::binary | std::ios::in | std::ios::out);
    if (!dbFile.is_open()) return false;
    
    // 定位到记录位置的isDead字段
    dbFile.seekp(sizeof(DBHeader) + nIndex * sizeof(HumDataInfo) + offsetof(HumDataInfo, isDead));
    
    // 只更新删除标记
    bool isDead = true;
    dbFile.write(reinterpret_cast<char*>(&isDead), sizeof(isDead));
    dbFile.close();
    
    return true;
}

int32_t HumanDB::FindByAccount(const std::string& sAccount, std::vector<std::string>& chrList) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    chrList.clear();
    auto it = m_QuickIDList.find(sAccount);
    if (it != m_QuickIDList.end()) {
        for (const auto& idx : it->second) {
            chrList.push_back(idx.sChrName);
        }
    }
    
    return static_cast<int32_t>(chrList.size());
}

int32_t HumanDB::ChrCountOfAccount(const std::string& sAccount) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_QuickIDList.find(sAccount);
    if (it != m_QuickIDList.end()) {
        return static_cast<int32_t>(it->second.size());
    }
    return 0;
}

// ==================== DBServerMain 实现 ====================

DBServerMain::DBServerMain() 
    : m_nServerPort(6000), m_nIDServerPort(5600),
      m_nQueryCount(0), m_nSaveCount(0), m_nLoadCount(0),
      m_boIDServerConnected(false), m_boRunning(false),
      m_nNextRequestID(1) {
    m_sServerAddr = "0.0.0.0";
    m_sIDServerAddr = "127.0.0.1";
    m_sDBPath = "./FDB/";
    m_sBackupPath = "./Backup/";
    m_sGameDBPath = "./GameData/GameData.db";  // 默认游戏数据库路径
}

DBServerMain::~DBServerMain() {
    Stop();
}

bool DBServerMain::Initialize() {
    LoadConfig();
    
    // 创建必要的目录
    std::filesystem::create_directories(m_sDBPath);
    std::filesystem::create_directories(m_sBackupPath);
    std::filesystem::create_directories(std::filesystem::path(m_sGameDBPath).parent_path());
    
    // 初始化人物数据库
    std::string sDBFile = GetDBFileName(m_sDBPath, "Hum.DB");
    m_pHumanDB = std::make_unique<HumanDB>(sDBFile);
    if (!m_pHumanDB->Open()) {
        LOG_ERROR("Failed to open human database");
        return false;
    }
    
    // 初始化游戏数据库
    if (!InitializeGameDatabase()) {
        LOG_ERROR("Failed to initialize game database");
        return false;
    }
    
    // 初始化网络管理器
    m_pNetworkManager = std::make_unique<NetworkManager>();
    if (!m_pNetworkManager->Initialize()) {
        LOG_ERROR("Failed to initialize network manager");
        return false;
    }
    
    // 启动服务器
    if (!m_pNetworkManager->StartServer(m_nServerPort, 100)) {
        LOG_ERROR("Failed to start server on port " + std::to_string(m_nServerPort));
        return false;
    }
    
    m_pNetworkManager->SetPacketHandler(std::shared_ptr<PacketHandler>(this, [](PacketHandler*){}));
    
    // 连接到ID服务器
    m_IDServerThread = std::thread(&DBServerMain::ProcessIDServerConnection, this);
    
    LoadAllowedIPs();
    
    LOG_INFO("DBServer initialized successfully");
    return true;
}

void DBServerMain::Run() {
    LOG_INFO("DBServer starting on " + m_sServerAddr + ":" + std::to_string(m_nServerPort));
    
    m_boRunning = true;
    
    // 主循环
    while (m_boRunning) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // 检查是否需要重连ID服务器
        if (!m_boIDServerConnected) {
            ConnectToIDServer();
        }
        
        // 清理超时的会话验证请求
        {
            std::lock_guard<std::mutex> lock(m_PendingChecksMutex);
            uint32_t now = MirServer::GetTickCount();
            auto it = m_PendingChecks.begin();
            while (it != m_PendingChecks.end()) {
                if (now - it->second.dwRequestTime > 5000) { // 5秒超时
                    // 发送超时响应
                    DefaultMessage defMsg = MakeDefaultMsg(DBR_LOADHUMANRCD, -1, 0, 0, 0);
                    it->second.clientConn->Send(MessageConverter::EncodeMessage(defMsg).data(), 
                                              MessageConverter::EncodeMessage(defMsg).size());
                    it = m_PendingChecks.erase(it);
                } else {
                    ++it;
                }
            }
        }
    }
}

void DBServerMain::Stop() {
    m_boRunning = false;
    
    if (m_pNetworkManager) {
        m_pNetworkManager->Shutdown();
    }
    
    if (m_pHumanDB) {
        // 备份数据库
        BackupDatabase(m_sDBPath + "Hum.DB", m_sBackupPath);
        m_pHumanDB->Close();
    }
    
    if (m_pGameDataManager) {
        m_pGameDataManager->Shutdown();
    }
    
    m_boIDServerConnected = false;
    if (m_IDServerThread.joinable()) {
        m_IDServerThread.join();
    }
    
    LOG_INFO("DBServer stopped");
}

void DBServerMain::LoadConfig() {
    // TODO: 从配置文件加载配置
    LOG_INFO("Loading configuration...");
    
    // 配置文件路径
    std::string configFile = "config/DBServer.ini";
    
    // 检查配置文件是否存在
    if (!std::filesystem::exists(configFile)) {
        LOG_WARNING("Config file not found, using default settings: " + configFile);
        return;
    }
    
    // 使用简单的INI文件解析
    std::ifstream file(configFile);
    if (!file.is_open()) {
        LOG_ERROR("Failed to open config file: " + configFile);
        return;
    }
    
    std::string line;
    std::string section;
    
    while (std::getline(file, line)) {
        // 移除空白字符
        line.erase(0, line.find_first_not_of(" \t"));
        line.erase(line.find_last_not_of(" \t") + 1);
        
        // 跳过空行和注释
        if (line.empty() || line[0] == ';') continue;
        
        // 检查是否是节
        if (line[0] == '[' && line[line.length() - 1] == ']') {
            section = line.substr(1, line.length() - 2);
            continue;
        }
        
        // 解析键值对
        size_t pos = line.find('=');
        if (pos != std::string::npos) {
            std::string key = line.substr(0, pos);
            std::string value = line.substr(pos + 1);
            
            // 移除键值的空白
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);
            
            // 根据节和键设置配置
            if (section == "DB") {
                if (key == "Dir") m_sDBPath = value;
                else if (key == "Backup") m_sBackupPath = value;
                else if (key == "GameDB") m_sGameDBPath = value;  // 游戏数据库路径
            } else if (section == "Setup") {
                if (key == "ServerPort") m_nServerPort = std::stoi(value);
                else if (key == "ServerAddr") m_sServerAddr = value;
            } else if (section == "Server") {
                if (key == "IDSAddr") m_sIDServerAddr = value;
                else if (key == "IDSPort") m_nIDServerPort = std::stoi(value);
            }
        }
    }
    
    file.close();
    
    LOG_INFO("Configuration loaded successfully");
    LOG_INFO("DB Path: " + m_sDBPath);
    LOG_INFO("Game DB Path: " + m_sGameDBPath);
    LOG_INFO("Server: " + m_sServerAddr + ":" + std::to_string(m_nServerPort));
    LOG_INFO("ID Server: " + m_sIDServerAddr + ":" + std::to_string(m_nIDServerPort));
}

void DBServerMain::OnClientConnected(std::shared_ptr<ClientConnection> connection) {
    std::string remoteAddr = connection->GetRemoteIP() + ":" + std::to_string(connection->GetRemotePort());
    
    // 检查IP白名单
    if (!IsIPAllowed(remoteAddr)) {
        LOG_WARNING("Connection rejected from unauthorized IP: " + remoteAddr);
        connection->Disconnect();
        return;
    }
    
    auto serverInfo = std::make_shared<ServerInfo>();
    serverInfo->connection = connection;
    serverInfo->sIPAddr = remoteAddr;
    serverInfo->boActive = true;
    serverInfo->dwLastKeepAlive = MirServer::GetTickCount();
    
    {
        std::lock_guard<std::mutex> lock(m_ServerListMutex);
        m_ServerList[connection->GetId()] = serverInfo;
    }
    
    LOG_INFO("GameServer connected from: " + serverInfo->sIPAddr);
}

void DBServerMain::OnClientDisconnected(std::shared_ptr<ClientConnection> connection) {
    {
        std::lock_guard<std::mutex> lock(m_ServerListMutex);
        m_ServerList.erase(connection->GetId());
    }
    
    LOG_INFO("GameServer disconnected");
}

void DBServerMain::HandlePacket(std::shared_ptr<ClientConnection> connection, const MirServer::Protocol::PacketHeader& header, const uint8_t* data, size_t dataSize) {
    ProcessServerPacket(connection, header, data, dataSize);
}

void DBServerMain::ProcessServerPacket(std::shared_ptr<ClientConnection> conn, const MirServer::Protocol::PacketHeader& header, const uint8_t* data, size_t dataSize) {
    switch (header.packetType) {
        case MirServer::Protocol::DB_LOADHUMANRCD:
            ProcessLoadHumanRcd(conn, std::string(reinterpret_cast<const char*>(data), dataSize));
            break;
            
        case MirServer::Protocol::DB_SAVEHUMANRCD:
            ProcessSaveHumanRcd(conn, header.sequence, std::string(reinterpret_cast<const char*>(data), dataSize));
            break;
            
        case MirServer::Protocol::DB_QUERYCHR:
            ProcessQueryChr(conn, std::string(reinterpret_cast<const char*>(data), dataSize));
            break;
            
        case MirServer::Protocol::DB_NEWCHR:
            ProcessNewChr(conn, std::string(reinterpret_cast<const char*>(data), dataSize));
            break;
            
        case MirServer::Protocol::DB_DELCHR:
            ProcessDelChr(conn, std::string(reinterpret_cast<const char*>(data), dataSize));
            break;
            
        default:
            LOG_WARNING("Unknown packet type: " + std::to_string(header.packetType));
            break;
    }
}

void DBServerMain::ProcessLoadHumanRcd(std::shared_ptr<ClientConnection> conn, const std::string& sData) {
    m_nLoadCount++;
    
    LoadHumanRequest request;
    if (sData.size() >= sizeof(LoadHumanRequest)) {
        memcpy(&request, sData.data(), sizeof(LoadHumanRequest));
    } else {
        LOG_ERROR("Invalid LoadHumanRequest data size");
        return;
    }
    
    std::string sAccount(request.sAccount);
    std::string sChrName(request.sChrName);
    std::string sIPAddr(request.sUserAddr);
    int32_t nSessionID = request.nSessionID;
    
    LOG_INFO("LoadHumanRcd - Account: " + sAccount + ", Character: " + sChrName);
    
    // 如果ID服务器已连接，进行异步验证
    if (m_boIDServerConnected && m_pIDServerConnection) {
        // 生成请求ID
        int32_t requestID = m_nNextRequestID++;
        
        // 保存待处理请求
        PendingSessionCheck pending;
        pending.clientConn = conn;
        pending.sAccount = sAccount;
        pending.sChrName = sChrName;
        pending.sIPAddr = sIPAddr;
        pending.nSessionID = nSessionID;
        pending.dwRequestTime = MirServer::GetTickCount();
        
        {
            std::lock_guard<std::mutex> lock(m_PendingChecksMutex);
            m_PendingChecks[requestID] = pending;
        }
        
        // 发送会话检查请求
        
        SessionCheckRequest checkReq;
        strcpy(checkReq.sAccount, sAccount.c_str());
        strcpy(checkReq.sIPAddr, sIPAddr.c_str());
        checkReq.nSessionID = nSessionID;
        checkReq.nRequestID = requestID;
        
        PacketHeader header;
        header.packetType = ID_CHECK_SESSION;
        header.length = sizeof(PacketHeader) + sizeof(SessionCheckRequest);
        header.sequence = requestID;
        
        std::vector<uint8_t> packet(header.length);
        memcpy(packet.data(), &header, sizeof(PacketHeader));
        memcpy(packet.data() + sizeof(PacketHeader), &checkReq, sizeof(SessionCheckRequest));
        
        {
            std::lock_guard<std::mutex> lock(m_IDServerMutex);
            if (m_pIDServerConnection) {
                m_pIDServerConnection->Send(packet.data(), packet.size());
            }
        }
        
        // 异步验证，等待响应
        return;
    }
    
    // ID服务器未连接，直接处理（仅用于测试）
    LOG_WARNING("ID server not connected, processing without session verification");
    
    // 查找角色
    int32_t nIndex = m_pHumanDB->Index(sChrName);
    if (nIndex < 0) {
        // 角色不存在
        DefaultMessage defMsg = MakeDefaultMsg(DBR_LOADHUMANRCD, -3, 0, 0, 0);
        SendToClient(conn, MessageConverter::EncodeMessage(defMsg));
        return;
    }
    
    // 加载角色数据
    HumDataInfo humanRcd;
    if (!m_pHumanDB->Get(nIndex, humanRcd)) {
        // 加载失败
        DefaultMessage defMsg = MakeDefaultMsg(DBR_LOADHUMANRCD, -2, 0, 0, 0);
        SendToClient(conn, MessageConverter::EncodeMessage(defMsg));
        return;
    }
    
    // 发送成功响应
    DefaultMessage defMsg = MakeDefaultMsg(DBR_LOADHUMANRCD, 1, 0, 0, 1);
    std::string sResponse = MessageConverter::EncodeMessage(defMsg);
    sResponse += MessageConverter::EncodeString(sChrName) + "/";
    sResponse += MessageConverter::EncodeBuffer(&humanRcd, sizeof(HumDataInfo));
    
    SendToClient(conn, sResponse);
}

void DBServerMain::ProcessSaveHumanRcd(std::shared_ptr<ClientConnection> conn, int32_t nRecog, const std::string& sData) {
    m_nSaveCount++;
    
    // 解析数据
    size_t pos1 = sData.find('/');
    if (pos1 == std::string::npos) return;
    
    size_t pos2 = sData.find('/', pos1 + 1);
    if (pos2 == std::string::npos) return;
    
    std::string sUserId = MessageConverter::DecodeString(sData.substr(0, pos1));
    std::string sChrName = MessageConverter::DecodeString(sData.substr(pos1 + 1, pos2 - pos1 - 1));
    std::string sHumanRCD = sData.substr(pos2 + 1);
    
    LOG_INFO("SaveHumanRcd - Account: " + sUserId + ", Character: " + sChrName);
    
    // 解码角色数据
    HumDataInfo humanRcd;
    if (!MessageConverter::DecodeBuffer(sHumanRCD, &humanRcd, sizeof(HumDataInfo))) {
        // 发送失败响应
        DefaultMessage defMsg = MakeDefaultMsg(DBR_SAVEHUMANRCD, 0, 0, 0, 0);
        SendToClient(conn, MessageConverter::EncodeMessage(defMsg));
        return;
    }
    
    // 查找角色索引
    int32_t nIndex = m_pHumanDB->Index(sChrName);
    bool bSuccess = false;
    
    if (nIndex >= 0) {
        // 更新现有角色
        bSuccess = m_pHumanDB->Update(nIndex, humanRcd);
    } else {
        // 新建角色
        humanRcd.charName = sChrName;
        bSuccess = m_pHumanDB->Add(humanRcd);
    }
    
    if (bSuccess) {
        // 通知ID服务器保存成功
        SetSessionSaveRcd(sUserId);
        
        // 发送成功响应
        DefaultMessage defMsg = MakeDefaultMsg(DBR_SAVEHUMANRCD, 1, 0, 0, 0);
        SendToClient(conn, MessageConverter::EncodeMessage(defMsg));
    } else {
        // 发送失败响应
        DefaultMessage defMsg = MakeDefaultMsg(DBR_SAVEHUMANRCD, 0, 0, 0, 0);
        SendToClient(conn, MessageConverter::EncodeMessage(defMsg));
    }
}

void DBServerMain::ProcessQueryChr(std::shared_ptr<ClientConnection> conn, const std::string& sData) {
    m_nQueryCount++;
    
    // 解析账号信息
    std::string sAccount = MessageConverter::DecodeString(sData);
    
    LOG_INFO("QueryChr - Account: " + sAccount);
    
    // 查找账号下的角色
    std::vector<std::string> chrList;
    int32_t nCount = m_pHumanDB->FindByAccount(sAccount, chrList);
    
    // 构建响应
    std::string sResponse;
    for (const auto& sChrName : chrList) {
        int32_t nIndex = m_pHumanDB->Index(sChrName);
        if (nIndex >= 0) {
            HumDataInfo humanRcd;
            if (m_pHumanDB->Get(nIndex, humanRcd)) {
                // 构建角色信息
                QueryChr queryChr;
                strcpy(queryChr.sName, humanRcd.charName.c_str());
                queryChr.btJob = static_cast<BYTE>(humanRcd.job);
                queryChr.btHair = humanRcd.hair;
                queryChr.btLevel = humanRcd.level;
                queryChr.btSex = static_cast<BYTE>(humanRcd.gender);
                
                sResponse += MessageConverter::EncodeBuffer(&queryChr, sizeof(QueryChr)) + "/";
            }
        }
    }
    
    // 发送响应
    DefaultMessage defMsg = MakeDefaultMsg(DBR_QUERYCHR, nCount, 0, 0, 0);
    SendToClient(conn, MessageConverter::EncodeMessage(defMsg) + sResponse);
}

void DBServerMain::ProcessNewChr(std::shared_ptr<ClientConnection> conn, const std::string& sData) {
    // 解析创建角色请求
    size_t pos = sData.find('/');
    if (pos == std::string::npos) return;
    
    std::string sAccount = MessageConverter::DecodeString(sData.substr(0, pos));
    std::string sChrData = sData.substr(pos + 1);
    
    // 解码角色数据
    HumDataInfo humanRcd;
    if (!MessageConverter::DecodeBuffer(sChrData, &humanRcd, sizeof(HumDataInfo))) {
        DefaultMessage defMsg = MakeDefaultMsg(DBR_NEWCHR, 0, 0, 0, 0);
        SendToClient(conn, MessageConverter::EncodeMessage(defMsg));
        return;
    }
    
    LOG_INFO("NewChr - Account: " + sAccount + ", Character: " + humanRcd.charName);
    
    // 检查角色数量限制
    if (m_pHumanDB->ChrCountOfAccount(sAccount) >= 3) {
        DefaultMessage defMsg = MakeDefaultMsg(DBR_NEWCHR, 3, 0, 0, 0); // 角色数量已满
        SendToClient(conn, MessageConverter::EncodeMessage(defMsg));
        return;
    }
    
    // 检查角色名是否已存在
    if (m_pHumanDB->Index(humanRcd.charName) >= 0) {
        DefaultMessage defMsg = MakeDefaultMsg(DBR_NEWCHR, 2, 0, 0, 0); // 角色名已存在
        SendToClient(conn, MessageConverter::EncodeMessage(defMsg));
        return;
    }
    
    // 设置账号信息
    humanRcd.account = sAccount;
    
    // 添加角色
    if (m_pHumanDB->Add(humanRcd)) {
        DefaultMessage defMsg = MakeDefaultMsg(DBR_NEWCHR, 1, 0, 0, 0); // 成功
        SendToClient(conn, MessageConverter::EncodeMessage(defMsg));
    } else {
        DefaultMessage defMsg = MakeDefaultMsg(DBR_NEWCHR, 0, 0, 0, 0); // 失败
        SendToClient(conn, MessageConverter::EncodeMessage(defMsg));
    }
}

void DBServerMain::ProcessDelChr(std::shared_ptr<ClientConnection> conn, const std::string& sData) {
    // 解析删除角色请求
    size_t pos = sData.find('/');
    if (pos == std::string::npos) return;
    
    std::string sAccount = MessageConverter::DecodeString(sData.substr(0, pos));
    std::string sChrName = MessageConverter::DecodeString(sData.substr(pos + 1));
    
    LOG_INFO("DelChr - Account: " + sAccount + ", Character: " + sChrName);
    
    // 验证角色所有权
    int32_t nIndex = m_pHumanDB->Index(sChrName);
    if (nIndex < 0) {
        DefaultMessage defMsg = MakeDefaultMsg(DBR_DELCHR, 0, 0, 0, 0);
        SendToClient(conn, MessageConverter::EncodeMessage(defMsg));
        return;
    }
    
    HumDataInfo humanRcd;
    if (!m_pHumanDB->Get(nIndex, humanRcd)) {
        DefaultMessage defMsg = MakeDefaultMsg(DBR_DELCHR, 0, 0, 0, 0);
        SendToClient(conn, MessageConverter::EncodeMessage(defMsg));
        return;
    }
    
    // 检查账号是否匹配
    if (humanRcd.account != sAccount) {
        DefaultMessage defMsg = MakeDefaultMsg(DBR_DELCHR, -1, 0, 0, 0);
        SendToClient(conn, MessageConverter::EncodeMessage(defMsg));
        return;
    }
    
    // 删除角色
    if (m_pHumanDB->Delete(sChrName)) {
        DefaultMessage defMsg = MakeDefaultMsg(DBR_DELCHR, 1, 0, 0, 0);
        SendToClient(conn, MessageConverter::EncodeMessage(defMsg));
    } else {
        DefaultMessage defMsg = MakeDefaultMsg(DBR_DELCHR, 0, 0, 0, 0);
        SendToClient(conn, MessageConverter::EncodeMessage(defMsg));
    }
}

void DBServerMain::ConnectToIDServer() {
    // TODO: 实现连接到ID服务器
    LOG_INFO("Connecting to ID server: " + m_sIDServerAddr + ":" + std::to_string(m_nIDServerPort));
    
    std::lock_guard<std::mutex> lock(m_IDServerMutex);
    
    // 如果已经连接，先断开
    if (m_pIDServerConnection && m_pIDServerConnection->IsConnected()) {
        m_pIDServerConnection->Disconnect();
        m_pIDServerConnection.reset();
    }
    
    try {
        // 创建socket
        SOCKET_TYPE sock = socket(AF_INET, SOCK_STREAM, 0);
        if (sock == INVALID_SOCKET) {
            LOG_ERROR("Failed to create socket for ID server connection");
            return;
        }
        
        // 设置非阻塞模式
        #ifdef _WIN32
        u_long mode = 1;
        ioctlsocket(sock, FIONBIO, &mode);
        #else
        int flags = fcntl(sock, F_GETFL, 0);
        fcntl(sock, F_SETFL, flags | O_NONBLOCK);
        #endif
        
        // 连接到ID服务器
        sockaddr_in serverAddr;
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_port = htons(m_nIDServerPort);
        inet_pton(AF_INET, m_sIDServerAddr.c_str(), &serverAddr.sin_addr);
        
        int result = connect(sock, (sockaddr*)&serverAddr, sizeof(serverAddr));
        if (result == SOCKET_ERROR) {
            #ifdef _WIN32
            int error = WSAGetLastError();
            if (error != WSAEWOULDBLOCK) {
            #else
            if (errno != EINPROGRESS) {
            #endif
                closesocket(sock);
                LOG_ERROR("Failed to connect to ID server");
                return;
            }
        }
        
        // 等待连接完成
        fd_set writefds;
        FD_ZERO(&writefds);
        FD_SET(sock, &writefds);
        
        timeval timeout;
        timeout.tv_sec = 5;
        timeout.tv_usec = 0;
        
        if (select(sock + 1, nullptr, &writefds, nullptr, &timeout) > 0) {
            // 创建连接对象
            static std::atomic<uint32_t> s_connectionId{1};
            std::string serverIP = m_sIDServerAddr;
            m_pIDServerConnection = std::make_shared<ClientConnection>(
                s_connectionId++, sock, serverIP, m_nIDServerPort);
            m_boIDServerConnected = true;
            
            LOG_INFO("Connected to ID server successfully");
            
            // 发送注册消息
            SendRegisterToIDServer();
        } else {
            closesocket(sock);
            LOG_ERROR("Connection to ID server timed out");
        }
    } catch (const std::exception& e) {
        LOG_ERROR("Exception while connecting to ID server: " + std::string(e.what()));
    }
}

void DBServerMain::ProcessIDServerConnection() {
    // TODO: 实现ID服务器连接处理
    while (m_boRunning) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        std::lock_guard<std::mutex> lock(m_IDServerMutex);
        
        if (!m_boIDServerConnected || !m_pIDServerConnection || !m_pIDServerConnection->IsConnected()) {
            m_boIDServerConnected = false;
            std::this_thread::sleep_for(std::chrono::seconds(5));
            continue;
        }
        
        // 接收数据
        m_pIDServerConnection->Receive();
        
        // 处理接收到的数据包
        std::vector<uint8_t> packet;
        while (m_pIDServerConnection->HasCompletePacket()) {
            if (m_pIDServerConnection->GetPacket(packet)) {
                if (packet.size() >= sizeof(PacketHeader)) {
                    PacketHeader* header = reinterpret_cast<PacketHeader*>(packet.data());
                    size_t dataSize = header->length - sizeof(PacketHeader);
                    if (packet.size() >= header->length) {
                        ProcessIDServerPacket(*header, packet.data() + sizeof(PacketHeader), dataSize);
                    }
                }
            }
        }
        
        // 定期发送心跳
        static uint32_t lastKeepAlive = 0;
        uint32_t now = MirServer::GetTickCount();
        if (now - lastKeepAlive > 30000) { // 30秒
            SendKeepAliveToIDServer();
            lastKeepAlive = now;
        }
    }
}

void DBServerMain::ProcessIDServerPacket(const MirServer::Protocol::PacketHeader& header, const uint8_t* data, size_t dataSize) {
    using namespace MirServer::Protocol;
    
    switch (header.packetType) {
        case ID_SESSION_CHECKED: {
            if (dataSize >= sizeof(SessionCheckResponse)) {
                SessionCheckResponse* response = (SessionCheckResponse*)data;
                
                std::lock_guard<std::mutex> lock(m_PendingChecksMutex);
                auto it = m_PendingChecks.find(response->nRequestID);
                if (it != m_PendingChecks.end()) {
                    auto& pending = it->second;
                    
                    if (response->boValid) {
                        // 会话有效，继续加载角色
                        int32_t nIndex = m_pHumanDB->Index(pending.sChrName);
                        if (nIndex >= 0) {
                            HumDataInfo humanRcd;
                            if (m_pHumanDB->Get(nIndex, humanRcd)) {
                                // 发送成功响应
                                DefaultMessage defMsg = MakeDefaultMsg(DBR_LOADHUMANRCD, 1, 0, 0, 1);
                                std::string sResponse = MessageConverter::EncodeMessage(defMsg);
                                sResponse += MessageConverter::EncodeString(pending.sChrName) + "/";
                                sResponse += MessageConverter::EncodeBuffer(&humanRcd, sizeof(HumDataInfo));
                                SendToClient(pending.clientConn, sResponse);
                            } else {
                                DefaultMessage defMsg = MakeDefaultMsg(DBR_LOADHUMANRCD, -2, 0, 0, 0);
                                SendToClient(pending.clientConn, MessageConverter::EncodeMessage(defMsg));
                            }
                        } else {
                            DefaultMessage defMsg = MakeDefaultMsg(DBR_LOADHUMANRCD, -3, 0, 0, 0);
                            SendToClient(pending.clientConn, MessageConverter::EncodeMessage(defMsg));
                        }
                    } else {
                        // 会话无效
                        DefaultMessage defMsg = MakeDefaultMsg(DBR_LOADHUMANRCD, -1, 0, 0, 0);
                        SendToClient(pending.clientConn, MessageConverter::EncodeMessage(defMsg));
                    }
                    
                    m_PendingChecks.erase(it);
                }
            }
            break;
        }
    }
}

void DBServerMain::SendRegisterToIDServer() {
    // using namespace MirServer::Protocol;
    
    DBServerRegisterMsg regMsg;
    strcpy(regMsg.sServerName, "DBServer01");
    strcpy(regMsg.sServerAddr, m_sServerAddr.c_str());
    regMsg.nServerPort = m_nServerPort;
    regMsg.nMaxConnections = 100;
    
    PacketHeader header;
    header.packetType = ID_REGISTER_DBSERVER;
    header.length = sizeof(PacketHeader) + sizeof(DBServerRegisterMsg);
    header.sequence = 0;
    
    std::vector<uint8_t> packet(header.length);
    memcpy(packet.data(), &header, sizeof(PacketHeader));
    memcpy(packet.data() + sizeof(PacketHeader), &regMsg, sizeof(DBServerRegisterMsg));
    
    if (m_pIDServerConnection) {
        m_pIDServerConnection->Send(packet.data(), packet.size());
        LOG_INFO("Sent registration to ID server");
    }
}

void DBServerMain::SendKeepAliveToIDServer() {
    // using namespace MirServer::Protocol;
    
    KeepAliveMsg keepAlive;
    keepAlive.nTimestamp = std::chrono::system_clock::now().time_since_epoch().count();
    keepAlive.nLoadCount = m_nLoadCount.load();
    keepAlive.nSaveCount = m_nSaveCount.load();
    keepAlive.nQueryCount = m_nQueryCount.load();
    
    PacketHeader header;
    header.packetType = ID_KEEPALIVE;
    header.length = sizeof(PacketHeader) + sizeof(KeepAliveMsg);
    header.sequence = 0;
    
    std::vector<uint8_t> packet(header.length);
    memcpy(packet.data(), &header, sizeof(PacketHeader));
    memcpy(packet.data() + sizeof(PacketHeader), &keepAlive, sizeof(KeepAliveMsg));
    
    if (m_pIDServerConnection) {
        m_pIDServerConnection->Send(packet.data(), packet.size());
    }
}

bool DBServerMain::CheckSessionLoadRcd(const std::string& sAccount, const std::string& sIPAddr, int32_t nSessionID) {
    // TODO: 实现会话验证
    // 暂时返回true，实际应该与ID服务器通信验证
    
    if (!m_boIDServerConnected || !m_pIDServerConnection) {
        LOG_WARNING("ID server not connected, allowing session without verification");
        return true;
    }
    
    // using namespace MirServer::Protocol;
    
    // 生成请求ID
    int32_t requestID = m_nNextRequestID++;
    
    // 发送会话检查请求
    SessionCheckRequest request;
    strcpy(request.sAccount, sAccount.c_str());
    strcpy(request.sIPAddr, sIPAddr.c_str());
    request.nSessionID = nSessionID;
    request.nRequestID = requestID;
    
    PacketHeader header;
    header.packetType = ID_CHECK_SESSION;
    header.length = sizeof(PacketHeader) + sizeof(SessionCheckRequest);
    header.sequence = requestID;
    
    std::vector<uint8_t> packet(header.length);
    memcpy(packet.data(), &header, sizeof(PacketHeader));
    memcpy(packet.data() + sizeof(PacketHeader), &request, sizeof(SessionCheckRequest));
    
    {
        std::lock_guard<std::mutex> lock(m_IDServerMutex);
        if (m_pIDServerConnection) {
            m_pIDServerConnection->Send(packet.data(), packet.size());
        }
    }
    
    // 由于是异步验证，这里先返回false，等待ID服务器响应后再处理
    return false;
}

void DBServerMain::SetSessionSaveRcd(const std::string& sAccount) {
    // TODO: 通知ID服务器角色保存成功
    if (!m_boIDServerConnected || !m_pIDServerConnection) {
        return;
    }
    
    using namespace MirServer::Protocol;
    
    SaveNotifyMsg saveMsg;
    strcpy(saveMsg.sAccount, sAccount.c_str());
    strcpy(saveMsg.sChrName, "");  // 这里可以传入角色名
    saveMsg.nSaveTime = std::chrono::system_clock::now().time_since_epoch().count();
    
    PacketHeader header;
    header.packetType = ID_NOTIFY_SAVE;
    header.length = sizeof(PacketHeader) + sizeof(SaveNotifyMsg);
    header.sequence = 0;
    
    std::vector<uint8_t> packet(header.length);
    memcpy(packet.data(), &header, sizeof(PacketHeader));
    memcpy(packet.data() + sizeof(PacketHeader), &saveMsg, sizeof(SaveNotifyMsg));
    
    {
        std::lock_guard<std::mutex> lock(m_IDServerMutex);
        if (m_pIDServerConnection) {
            m_pIDServerConnection->Send(packet.data(), packet.size());
        }
    }
}

std::string DBServerMain::GetDBFileName(const std::string& sPath, const std::string& sName) {
    return sPath + sName;
}

bool DBServerMain::BackupDatabase(const std::string& sSourceFile, const std::string& sBackupPath) {
    try {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        
        char buffer[80];
        strftime(buffer, sizeof(buffer), "%Y%m%d_%H%M%S", localtime(&time_t));
        
        std::string sBackupFile = sBackupPath + "/" + 
            std::filesystem::path(sSourceFile).filename().string() + 
            "." + buffer;
            
        std::filesystem::copy_file(sSourceFile, sBackupFile, 
            std::filesystem::copy_options::overwrite_existing);
            
        LOG_INFO("Database backed up to: " + sBackupFile);
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to backup database: " + std::string(e.what()));
        return false;
    }
}

void DBServerMain::LoadAllowedIPs() {
    std::lock_guard<std::mutex> lock(m_AllowedIPsMutex);
    m_AllowedIPs.clear();
    
    // 默认允许本地连接
    m_AllowedIPs.insert("127.0.0.1");
    m_AllowedIPs.insert("::1");
    
    // 从文件加载IP白名单
    std::string ipFile = "config/!AddrTable.txt";
    std::ifstream file(ipFile);
    
    if (!file.is_open()) {
        LOG_WARNING("IP whitelist file not found: " + ipFile);
        return;
    }
    
    std::string line;
    while (std::getline(file, line)) {
        // 移除空白字符
        line.erase(0, line.find_first_not_of(" \t\r\n"));
        line.erase(line.find_last_not_of(" \t\r\n") + 1);
        
        // 跳过空行和注释
        if (line.empty() || line[0] == '#' || line[0] == ';') continue;
        
        m_AllowedIPs.insert(line);
        LOG_INFO("Allowed IP: " + line);
    }
    
    file.close();
    LOG_INFO("Loaded " + std::to_string(m_AllowedIPs.size()) + " allowed IPs");
}

bool DBServerMain::IsIPAllowed(const std::string& sIPAddr) {
    std::lock_guard<std::mutex> lock(m_AllowedIPsMutex);
    
    // 如果白名单为空，允许所有连接
    if (m_AllowedIPs.empty()) {
        return true;
    }
    
    // 提取IP地址（去除端口）
    std::string ip = sIPAddr;
    size_t pos = ip.find(':');
    if (pos != std::string::npos) {
        ip = ip.substr(0, pos);
    }
    
    return m_AllowedIPs.find(ip) != m_AllowedIPs.end();
}

bool DBServerMain::InitializeGameDatabase() {
    // 创建游戏数据管理器
    m_pGameDataManager = std::make_unique<GameDataManager>();
    
    // 初始化数据库连接
    if (!m_pGameDataManager->Initialize(m_sGameDBPath)) {
        LOG_ERROR("Failed to initialize game data manager");
        return false;
    }
    
    // 加载所有游戏数据到内存
    if (!m_pGameDataManager->LoadAllData()) {
        LOG_INFO("No game data loaded (database might be empty)");
        // 这不是错误，数据库可能是空的
    }
    
    LOG_INFO("Game database initialized successfully");
    return true;
}

} // namespace DBServer 
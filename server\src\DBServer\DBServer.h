#ifndef DBSERVER_H
#define DBSERVER_H

#include <string>
#include <vector>
#include <map>
#include <set>
#include <memory>
#include <thread>
#include <mutex>
#include <queue>
#include "../Protocol/NetworkManager.h"
#include "../Protocol/PacketTypes.h"
#include "../Protocol/IDServerProtocol.h"
#include "../Protocol/MessageConverter.h"
#include "../Common/Types.h"
#include "GameDataDAO.h"  // 添加游戏数据DAO

// 添加必要的using声明
using MirServer::HumDataInfo;
using MirServer::Protocol::QueryChr;
using MirServer::Protocol::DefaultMessage;
using MirServer::Protocol::MakeDefaultMsg;
using MirServer::Protocol::PacketHeader;
using MirServer::Network::ClientConnection;
using MirServer::Network::PacketHandler;
using MirServer::Network::NetworkManager;

// 添加DBServer协议常量
namespace MirServer {
namespace Protocol {
    // DBServer协议响应 - 删除这些，因为它们已经在PacketTypes.h中定义了
    // const uint16_t DBR_LOADHUMANRCD = 100;
    // const uint16_t DBR_SAVEHUMANRCD = 101;
    // const uint16_t DBR_QUERYCHR = 102;
    // const uint16_t DBR_NEWCHR = 103;
    // const uint16_t DBR_DELCHR = 104;
}
}

namespace DBServer {

// 数据库文件头
struct DBHeader {
    char sDesc[36];          // 描述信息
    int32_t nHumCount;       // 角色总数
    int32_t nLastIndex;      // 最后索引
    int64_t dLastDate;       // 最后更新日期
    int64_t dUpdateDate;     // 更新日期
    char reserved[64];       // 保留字段
};

// 角色记录头
struct RecordHeader {
    bool boDeleted;          // 是否删除
    int64_t dCreateDate;     // 创建日期
    int32_t nSelectID;       // 选择ID
    char sName[15];          // 角色名称
};

// 角色信息记录
struct HumInfo {
    RecordHeader Header;     // 记录头
    char sChrName[15];      // 角色名称
    char sAccount[11];      // 账号
    bool boDeleted;         // 是否删除
    int64_t dModDate;       // 修改日期
    uint8_t btCount;        // 操作计数
    bool boSelected;        // 是否选中
    char reserved[6];       // 保留
};

// 快速查找索引
struct QuickIndex {
    std::string sChrName;
    int32_t nIndex;
    int32_t nSelectID;
};

// 加载角色请求
struct LoadHumanRequest {
    char sAccount[13];
    char sChrName[15];
    char sUserAddr[16];
    int32_t nSessionID;
};

// 服务器信息
struct ServerInfo {
    std::shared_ptr<ClientConnection> connection;
    std::string sIPAddr;
    bool boActive;
    uint32_t dwLastKeepAlive;
};

// 人物数据库类
class HumanDB {
private:
    std::string m_sDBFileName;
    std::string m_sIdxFileName;
    DBHeader m_Header;
    std::map<std::string, int32_t> m_QuickList;      // 角色名->索引
    std::map<std::string, std::vector<QuickIndex>> m_QuickIDList; // 账号->角色列表
    std::vector<int32_t> m_DeletedList;              // 已删除记录
    std::mutex m_mutex;
    bool m_boOpened;
    
    bool LoadIndex();
    bool SaveIndex();
    bool LoadQuickList();
    bool GetRecord(int32_t nIndex, HumDataInfo& rcd);
    bool UpdateRecord(int32_t nIndex, const HumDataInfo& rcd, bool boNew);
    bool DeleteRecord(int32_t nIndex);
    
public:
    HumanDB(const std::string& sFileName);
    ~HumanDB();
    
    bool Open();
    void Close();
    int32_t Index(const std::string& sName);
    bool Get(int32_t nIndex, HumDataInfo& rcd);
    bool Add(HumDataInfo& rcd);
    bool Update(int32_t nIndex, const HumDataInfo& rcd);
    bool Delete(const std::string& sName);
    int32_t FindByAccount(const std::string& sAccount, std::vector<std::string>& chrList);
    int32_t ChrCountOfAccount(const std::string& sAccount);
};

// 数据库服务器主类
class DBServerMain : public PacketHandler {
private:
    std::unique_ptr<NetworkManager> m_pNetworkManager;
    std::unique_ptr<HumanDB> m_pHumanDB;
    std::unique_ptr<GameDataManager> m_pGameDataManager;  // 添加游戏数据管理器
    std::map<int, std::shared_ptr<ServerInfo>> m_ServerList;
    std::mutex m_ServerListMutex;
    
    // 配置
    std::string m_sDBPath;
    std::string m_sBackupPath;
    std::string m_sServerAddr;
    int32_t m_nServerPort;
    std::string m_sIDServerAddr;
    int32_t m_nIDServerPort;
    std::string m_sGameDBPath;  // 游戏数据库路径
    
    // 统计
    std::atomic<int32_t> m_nQueryCount;
    std::atomic<int32_t> m_nSaveCount;
    std::atomic<int32_t> m_nLoadCount;
    
    // ID服务器连接
    std::shared_ptr<ClientConnection> m_pIDServerConnection;
    std::thread m_IDServerThread;
    std::atomic<bool> m_boIDServerConnected;
    std::atomic<bool> m_boRunning;
    std::mutex m_IDServerMutex;
    
    // 会话验证请求映射
    struct PendingSessionCheck {
        std::shared_ptr<ClientConnection> clientConn;
        std::string sAccount;
        std::string sChrName;
        std::string sIPAddr;
        int32_t nSessionID;
        uint32_t dwRequestTime;
    };
    std::map<int32_t, PendingSessionCheck> m_PendingChecks;
    std::atomic<int32_t> m_nNextRequestID;
    std::mutex m_PendingChecksMutex;
    
    // IP白名单
    std::set<std::string> m_AllowedIPs;
    std::mutex m_AllowedIPsMutex;
    
    void LoadConfig();
    void LoadAllowedIPs();
    bool IsIPAllowed(const std::string& sIPAddr);
    void ConnectToIDServer();
    void ProcessIDServerConnection();
    void ProcessIDServerPacket(const MirServer::Protocol::PacketHeader& header, const uint8_t* data, size_t dataSize);
    void SendRegisterToIDServer();
    void SendKeepAliveToIDServer();
    bool CheckSessionLoadRcd(const std::string& sAccount, const std::string& sIPAddr, 
                           int32_t nSessionID);
    void SetSessionSaveRcd(const std::string& sAccount);
    
    // 数据包处理
    void ProcessServerPacket(std::shared_ptr<ClientConnection> conn, const MirServer::Protocol::PacketHeader& header, const uint8_t* data, size_t dataSize);
    void ProcessLoadHumanRcd(std::shared_ptr<ClientConnection> conn, const std::string& sData);
    void ProcessSaveHumanRcd(std::shared_ptr<ClientConnection> conn, int32_t nRecog, const std::string& sData);
    void ProcessQueryChr(std::shared_ptr<ClientConnection> conn, const std::string& sData);
    void ProcessNewChr(std::shared_ptr<ClientConnection> conn, const std::string& sData);
    void ProcessDelChr(std::shared_ptr<ClientConnection> conn, const std::string& sData);
    
    // 游戏数据库初始化
    bool InitializeGameDatabase();
    
public:
    DBServerMain();
    ~DBServerMain();
    
    bool Initialize();
    void Run();
    void Stop();
    
    // PacketHandler接口实现
    void OnClientConnected(std::shared_ptr<ClientConnection> connection) override;
    void OnClientDisconnected(std::shared_ptr<ClientConnection> connection) override;
    void HandlePacket(std::shared_ptr<ClientConnection> connection, const MirServer::Protocol::PacketHeader& header, const uint8_t* data, size_t dataSize) override;
    
    // 获取游戏数据管理器
    GameDataManager* GetGameDataManager() { return m_pGameDataManager.get(); }
    
    // 工具函数
    static std::string GetDBFileName(const std::string& sPath, const std::string& sName);
    static bool BackupDatabase(const std::string& sSourceFile, const std::string& sBackupPath);
};

} // namespace DBServer

#endif // DBSERVER_H 
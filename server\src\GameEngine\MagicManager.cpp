// MagicManager.cpp - 魔法管理器实现
#include "MagicManager.h"
#include "../Common/Logger.h"
#include "../Common/Utils.h"
#include "../BaseObject/PlayObject.h"
#include "../BaseObject/BaseObject.h"
#include "Environment.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cmath>

namespace MirServer {

MagicManager::MagicManager() {
    m_initialized = false;
}

MagicManager::~MagicManager() {
    Finalize();
}

bool MagicManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    // 加载魔法数据
    if (!LoadMagicData()) {
        Logger::Error("Failed to load magic data");
        return false;
    }

    // 初始化魔法效果处理器
    InitializeMagicProcessors();

    m_initialized = true;
    Logger::Info("MagicManager initialized successfully");
    return true;
}

void MagicManager::Finalize() {
    if (!m_initialized) {
        return;
    }

    m_magicInfos.clear();
    m_magicProcessors.clear();
    m_activeMagics.clear();

    m_initialized = false;
    Logger::Info("MagicManager finalized");
}

bool MagicManager::LoadMagicData() {
    std::ifstream file("GameData/Import/Magic.csv");
    if (!file.is_open()) {
        Logger::Error("Cannot open Magic.csv file");
        return false;
    }

    std::string line;
    bool isFirstLine = true;

    while (std::getline(file, line)) {
        // 跳过标题行
        if (isFirstLine) {
            isFirstLine = false;
            continue;
        }

        if (line.empty() || line[0] == '#') {
            continue;
        }

        MagicInfo magic;
        if (ParseMagicLine(line, magic)) {
            m_magicInfos[magic.magicId] = magic;
        }
    }

    file.close();
    Logger::Info("Loaded " + std::to_string(m_magicInfos.size()) + " magic spells");
    return true;
}

bool MagicManager::ParseMagicLine(const std::string& line, MagicInfo& magic) {
    std::vector<std::string> tokens;
    std::stringstream ss(line);
    std::string token;

    while (std::getline(ss, token, ',')) {
        tokens.push_back(token);
    }

    if (tokens.size() < 19) {
        return false;
    }

    try {
        magic.magicId = static_cast<WORD>(std::stoi(tokens[0]));
        magic.magicName = tokens[1];
        magic.effectType = static_cast<BYTE>(std::stoi(tokens[2]));
        magic.effect = static_cast<BYTE>(std::stoi(tokens[3]));
        magic.spell = static_cast<WORD>(std::stoi(tokens[4]));
        magic.power = static_cast<WORD>(std::stoi(tokens[5]));
        magic.maxPower = static_cast<WORD>(std::stoi(tokens[6]));
        magic.job = static_cast<BYTE>(std::stoi(tokens[7]));

        // 训练等级需求
        magic.trainLevel[0] = static_cast<BYTE>(std::stoi(tokens[8]));
        magic.trainLevel[1] = static_cast<BYTE>(std::stoi(tokens[9]));
        magic.trainLevel[2] = static_cast<BYTE>(std::stoi(tokens[10]));
        magic.trainLevel[3] = 0; // 预留

        // 训练值需求
        magic.maxTrain[0] = static_cast<WORD>(std::stoi(tokens[11]));
        magic.maxTrain[1] = static_cast<WORD>(std::stoi(tokens[12]));
        magic.maxTrain[2] = static_cast<WORD>(std::stoi(tokens[13]));
        magic.maxTrain[3] = 0; // 预留

        magic.delayTime = static_cast<DWORD>(std::stoi(tokens[14]));
        magic.defSpell = static_cast<BYTE>(std::stoi(tokens[15]));
        magic.defPower = static_cast<BYTE>(std::stoi(tokens[16]));
        magic.defMaxPower = static_cast<BYTE>(std::stoi(tokens[17]));
        magic.descr = tokens[18];

        return true;
    } catch (const std::exception& e) {
        Logger::Error("Error parsing magic line: " + std::string(e.what()));
        return false;
    }
}

void MagicManager::InitializeMagicProcessors() {
    // 注册魔法处理器
    m_magicProcessors[1] = [this](PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos) {
        return ProcessFireBall(caster, magic, target, targetPos);
    };

    m_magicProcessors[2] = [this](PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos) {
        return ProcessHeal(caster, magic, target, targetPos);
    };

    m_magicProcessors[5] = [this](PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos) {
        return ProcessBigFireBall(caster, magic, target, targetPos);
    };

    m_magicProcessors[8] = [this](PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos) {
        return ProcessResistRing(caster, magic, target, targetPos);
    };

    m_magicProcessors[9] = [this](PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos) {
        return ProcessHellFire(caster, magic, target, targetPos);
    };

    m_magicProcessors[10] = [this](PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos) {
        return ProcessLightning(caster, magic, target, targetPos);
    };
}

const MagicInfo* MagicManager::GetMagicInfo(WORD magicId) const {
    auto it = m_magicInfos.find(magicId);
    return (it != m_magicInfos.end()) ? &it->second : nullptr;
}

bool MagicManager::CanUseMagic(PlayObject* player, WORD magicId) const {
    if (!player) return false;

    const MagicInfo* magic = GetMagicInfo(magicId);
    if (!magic) return false;

    // 检查职业
    if (magic->job != 255 && magic->job != static_cast<BYTE>(player->GetJob())) {
        return false;
    }

    // 检查等级
    if (player->GetLevel() < magic->trainLevel[0]) {
        return false;
    }

    // 检查魔法值
    if (player->GetMP() < magic->spell) {
        return false;
    }

    // 检查冷却时间
    DWORD currentTime = GetCurrentTime();
    auto it = m_lastCastTimes.find(player->GetCharName() + "_" + std::to_string(magicId));
    if (it != m_lastCastTimes.end()) {
        if (currentTime - it->second < magic->delayTime) {
            return false;
        }
    }

    return true;
}

bool MagicManager::UseMagic(PlayObject* caster, WORD magicId, BaseObject* target, const Point& targetPos) {
    if (!caster) return false;

    const MagicInfo* magic = GetMagicInfo(magicId);
    if (!magic) return false;

    if (!CanUseMagic(caster, magicId)) {
        return false;
    }

    // 消耗魔法值
    caster->DecMP(magic->spell);

    // 记录施法时间
    std::string key = caster->GetCharName() + "_" + std::to_string(magicId);
    m_lastCastTimes[key] = GetCurrentTime();

    // 执行魔法效果
    auto processor = m_magicProcessors.find(magicId);
    if (processor != m_magicProcessors.end()) {
        return processor->second(caster, *magic, target, targetPos);
    }

    // 默认处理
    return ProcessDefaultMagic(caster, *magic, target, targetPos);
}

void MagicManager::Run() {
    DWORD currentTime = GetCurrentTime();

    // 处理持续性魔法效果
    ProcessActiveMagics(currentTime);

    // 清理过期的冷却时间记录
    CleanupCooldowns(currentTime);
}

void MagicManager::ProcessActiveMagics(DWORD currentTime) {
    auto it = m_activeMagics.begin();
    while (it != m_activeMagics.end()) {
        ActiveMagic& activeMagic = it->second;

        if (currentTime >= activeMagic.endTime) {
            // 魔法效果结束
            OnMagicEnd(activeMagic);
            it = m_activeMagics.erase(it);
        } else {
            // 处理持续效果
            if (currentTime >= activeMagic.nextTickTime) {
                ProcessMagicTick(activeMagic);
                activeMagic.nextTickTime = currentTime + activeMagic.tickInterval;
            }
            ++it;
        }
    }
}

void MagicManager::CleanupCooldowns(DWORD currentTime) {
    auto it = m_lastCastTimes.begin();
    while (it != m_lastCastTimes.end()) {
        if (currentTime - it->second > 300000) { // 5分钟后清理
            it = m_lastCastTimes.erase(it);
        } else {
            ++it;
        }
    }
}

// ==================== 魔法效果处理器实现 ====================

bool MagicManager::ProcessFireBall(PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos) {
    if (!caster || !target) return false;

    // 计算伤害
    int damage = CalculateMagicDamage(caster, magic, target);

    // 应用伤害
    target->TakeDamage(caster, damage, static_cast<int>(DamageType::MAGIC));

    // 发送魔法效果
    SendMagicEffect(caster, magic.magicId, target->GetCurrentPos(), target->GetObjectId());

    return true;
}

bool MagicManager::ProcessHeal(PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos) {
    if (!caster) return false;

    // 如果没有指定目标，治疗自己
    BaseObject* healTarget = target ? target : caster;

    // 计算治疗量
    int healAmount = CalculateHealAmount(caster, magic);

    // 应用治疗
    healTarget->Heal(healAmount);

    // 发送治疗效果
    SendMagicEffect(caster, magic.magicId, healTarget->GetCurrentPos(), healTarget->GetObjectId());

    return true;
}

bool MagicManager::ProcessBigFireBall(PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos) {
    if (!caster) return false;

    Point castPos = target ? target->GetCurrentPos() : targetPos;

    // 大火球有范围伤害
    std::vector<BaseObject*> targets = GetTargetsInRange(caster, castPos, 2);

    for (BaseObject* obj : targets) {
        if (obj && obj != caster && IsValidTarget(caster, obj)) {
            int damage = CalculateMagicDamage(caster, magic, obj);
            obj->TakeDamage(caster, damage, static_cast<int>(DamageType::MAGIC));
        }
    }

    // 发送范围魔法效果
    SendMagicEffect(caster, magic.magicId, castPos, 0);

    return true;
}

bool MagicManager::ProcessResistRing(PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos) {
    if (!caster) return false;

    Point casterPos = caster->GetCurrentPos();

    // 抗拒火环推开周围的敌人
    std::vector<BaseObject*> targets = GetTargetsInRange(caster, casterPos, 1);

    for (BaseObject* obj : targets) {
        if (obj && obj != caster && IsValidTarget(caster, obj)) {
            // 计算推开方向
            Point objPos = obj->GetCurrentPos();
            DirectionType pushDir = GetDirection(casterPos, objPos);

            // 推开目标
            PushObject(obj, pushDir, 2);

            // 造成少量伤害
            int damage = CalculateMagicDamage(caster, magic, obj) / 2;
            obj->TakeDamage(caster, damage, static_cast<int>(DamageType::MAGIC));
        }
    }

    // 发送抗拒火环效果
    SendMagicEffect(caster, magic.magicId, casterPos, 0);

    return true;
}

bool MagicManager::ProcessHellFire(PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos) {
    if (!caster) return false;

    Point castPos = target ? target->GetCurrentPos() : targetPos;

    // 地狱火创建持续伤害区域
    ActiveMagic activeMagic;
    activeMagic.magicId = magic.magicId;
    activeMagic.caster = caster;
    activeMagic.position = castPos;
    activeMagic.startTime = GetCurrentTime();
    activeMagic.endTime = activeMagic.startTime + 10000; // 持续10秒
    activeMagic.tickInterval = 1000; // 每秒一次
    activeMagic.nextTickTime = activeMagic.startTime + activeMagic.tickInterval;
    activeMagic.power = CalculateMagicDamage(caster, magic, nullptr);

    std::string key = std::to_string(caster->GetObjectId()) + "_" + std::to_string(magic.magicId) + "_" + std::to_string(castPos.x) + "_" + std::to_string(castPos.y);
    m_activeMagics[key] = activeMagic;

    // 发送地狱火效果
    SendMagicEffect(caster, magic.magicId, castPos, 0);

    return true;
}

bool MagicManager::ProcessLightning(PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos) {
    if (!caster || !target) return false;

    // 疾光电影是快速单体攻击
    int damage = CalculateMagicDamage(caster, magic, target);

    // 雷电有概率造成麻痹效果
    if (rand() % 100 < 30) { // 30%概率
        ApplyParalysis(target, 3000); // 麻痹3秒
    }

    // 应用伤害
    target->TakeDamage(caster, damage, static_cast<int>(DamageType::MAGIC));

    // 发送雷电效果
    SendMagicEffect(caster, magic.magicId, target->GetCurrentPos(), target->GetObjectId());

    return true;
}

bool MagicManager::ProcessDefaultMagic(PlayObject* caster, const MagicInfo& magic, BaseObject* target, const Point& targetPos) {
    if (!caster) return false;

    // 默认魔法处理：简单的单体攻击
    if (target) {
        int damage = CalculateMagicDamage(caster, magic, target);
        target->TakeDamage(caster, damage, static_cast<int>(DamageType::MAGIC));
        SendMagicEffect(caster, magic.magicId, target->GetCurrentPos(), target->GetObjectId());
    } else {
        // 无目标魔法，在指定位置施放
        SendMagicEffect(caster, magic.magicId, targetPos, 0);
    }

    return true;
}

// ==================== 辅助方法实现 ====================

int MagicManager::CalculateMagicDamage(PlayObject* caster, const MagicInfo& magic, BaseObject* target) {
    if (!caster) return 0;

    // 基础伤害计算
    int baseDamage = magic.power + (rand() % (magic.maxPower - magic.power + 1));

    // 施法者属性加成
    int sc = caster->GetSC(); // 道术
    int mc = caster->GetMC(); // 魔法

    int damage = baseDamage;

    // 根据职业和魔法类型调整伤害
    switch (magic.job) {
        case 1: // 法师
            damage += mc / 4 + sc / 8;
            break;
        case 2: // 道士
            damage += sc / 4 + mc / 8;
            break;
        default: // 战士或通用
            damage += (mc + sc) / 8;
            break;
    }

    // 目标防御计算
    if (target) {
        int magicDefense = target->GetMagicDefense();
        damage = std::max(1, damage - magicDefense / 2);
    }

    return damage;
}

int MagicManager::CalculateHealAmount(PlayObject* caster, const MagicInfo& magic) {
    if (!caster) return 0;

    int baseHeal = magic.power + (rand() % (magic.maxPower - magic.power + 1));
    int sc = caster->GetSC();

    // 道术影响治疗量
    int healAmount = baseHeal + sc / 3;

    return healAmount;
}

std::vector<BaseObject*> MagicManager::GetTargetsInRange(PlayObject* caster, const Point& center, int range) {
    std::vector<BaseObject*> targets;

    if (!caster || !caster->GetEnvironment()) {
        return targets;
    }

    Environment* env = caster->GetEnvironment();

    for (int x = center.x - range; x <= center.x + range; ++x) {
        for (int y = center.y - range; y <= center.y + range; ++y) {
            if (x == center.x && y == center.y) continue;

            Point pos(x, y);
            if (!env->IsValidPosition(x, y)) continue;

            auto objects = env->GetObjectsAt(pos);
            for (auto obj : objects) {
                if (obj && obj->GetObjectType() != ObjectType::ITEM) {
                    targets.push_back(obj.get());
                }
            }
        }
    }

    return targets;
}

bool MagicManager::IsValidTarget(PlayObject* caster, BaseObject* target) {
    if (!caster || !target) return false;

    // 不能攻击自己
    if (caster == target) return false;

    // 不能攻击同组队成员
    if (target->GetObjectType() == ObjectType::HUMAN) {
        PlayObject* targetPlayer = static_cast<PlayObject*>(target);
        if (caster->IsInSameGroup(targetPlayer)) {
            return false;
        }
    }

    // 检查PK模式
    if (target->GetObjectType() == ObjectType::HUMAN) {
        // TODO: 实现PK模式检查
    }

    return true;
}

DirectionType MagicManager::GetDirection(const Point& from, const Point& to) {
    int dx = to.x - from.x;
    int dy = to.y - from.y;

    if (abs(dx) > abs(dy)) {
        return (dx > 0) ? DirectionType::RIGHT : DirectionType::LEFT;
    } else {
        return (dy > 0) ? DirectionType::DOWN : DirectionType::UP;
    }
}

void MagicManager::PushObject(BaseObject* obj, DirectionType direction, int distance) {
    if (!obj || !obj->GetEnvironment()) return;

    Point currentPos = obj->GetCurrentPos();
    Point newPos = currentPos;

    for (int i = 0; i < distance; ++i) {
        Point nextPos = newPos;

        switch (direction) {
            case DirectionType::UP:
                nextPos.y--;
                break;
            case DirectionType::DOWN:
                nextPos.y++;
                break;
            case DirectionType::LEFT:
                nextPos.x--;
                break;
            case DirectionType::RIGHT:
                nextPos.x++;
                break;
            default:
                return;
        }

        // 检查新位置是否可行走
        if (obj->GetEnvironment()->CanWalk(nextPos.x, nextPos.y)) {
            newPos = nextPos;
        } else {
            break; // 遇到障碍物停止推动
        }
    }

    // 移动对象到新位置
    if (newPos.x != currentPos.x || newPos.y != currentPos.y) {
        obj->SetCurrentPos(newPos);
    }
}

void MagicManager::ApplyParalysis(BaseObject* target, DWORD duration) {
    if (!target) return;

    // TODO: 实现麻痹状态
    // target->AddBuff(BuffType::PARALYSIS, duration);
}

void MagicManager::SendMagicEffect(PlayObject* caster, WORD magicId, const Point& pos, uint32_t targetId) {
    if (!caster) return;

    // TODO: 实现魔法效果发送
    // 这里应该发送魔法效果包给周围的玩家
}

void MagicManager::ProcessMagicTick(ActiveMagic& activeMagic) {
    if (!activeMagic.caster) return;

    // 处理持续性魔法的每次tick效果
    switch (activeMagic.magicId) {
        case 9: // 地狱火
            ProcessHellFireTick(activeMagic);
            break;
        default:
            break;
    }
}

void MagicManager::ProcessHellFireTick(ActiveMagic& activeMagic) {
    // 地狱火每次tick对范围内的敌人造成伤害
    std::vector<BaseObject*> targets = GetTargetsInRange(activeMagic.caster, activeMagic.position, 1);

    for (BaseObject* obj : targets) {
        if (obj && obj != activeMagic.caster && IsValidTarget(activeMagic.caster, obj)) {
            obj->TakeDamage(activeMagic.caster, activeMagic.power / 5, static_cast<int>(DamageType::MAGIC)); // 每次造成1/5伤害
        }
    }
}

void MagicManager::OnMagicEnd(const ActiveMagic& activeMagic) {
    // 魔法效果结束时的处理
    // 可以在这里清理魔法效果，发送结束消息等
}

} // namespace MirServer
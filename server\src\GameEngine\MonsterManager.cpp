// MonsterManager.cpp - 怪物管理器实现
#include "MonsterManager.h"
#include "Environment.h"
#include "../Common/Logger.h"
#include "../Common/Utils.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <random>

namespace MirServer {

// 全局实例
std::unique_ptr<MonsterManager> g_MonsterManager = nullptr;

MonsterManager::MonsterManager() {
    m_initialized = false;
    m_lastRunTime = GetCurrentTime();
    m_lastRespawnCheckTime = GetCurrentTime();
    m_lastAIProcessTime = GetCurrentTime();
}

MonsterManager::~MonsterManager() {
    Finalize();
}

bool MonsterManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    // 加载怪物模板
    if (!LoadMonsterTemplates()) {
        Logger::Error("Failed to load monster templates");
        return false;
    }

    // 加载刷新点
    if (!LoadSpawnPoints()) {
        Logger::Error("Failed to load monster spawn points");
        return false;
    }

    // 刷新所有怪物
    SpawnAllMonsters();

    m_initialized = true;
    Logger::Info("MonsterManager initialized successfully");
    return true;
}

void MonsterManager::Finalize() {
    if (!m_initialized) {
        return;
    }

    // 移除所有怪物
    RemoveAllMonsters();

    // 清理数据
    ClearMonsterTemplates();
    ClearSpawnPoints();

    m_initialized = false;
    Logger::Info("MonsterManager finalized");
}

bool MonsterManager::LoadMonsterTemplates(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        Logger::Error("Cannot open monster template file: " + filename);
        return false;
    }

    std::lock_guard<std::mutex> lock(m_templatesMutex);
    m_monsterTemplates.clear();

    std::string line;
    bool isFirstLine = true;
    int lineNumber = 0;

    while (std::getline(file, line)) {
        lineNumber++;

        // 跳过标题行和注释行
        if (isFirstLine || line.empty() || line[0] == '#' || line[0] == ';') {
            isFirstLine = false;
            continue;
        }

        MonsterTemplate monsterTemplate;
        if (ParseMonsterTemplateLine(line, monsterTemplate)) {
            m_monsterTemplates[monsterTemplate.monsterId] = monsterTemplate;
        } else {
            Logger::Warning("Failed to parse monster template line " + std::to_string(lineNumber) + ": " + line);
        }
    }

    file.close();
    Logger::Info("Loaded " + std::to_string(m_monsterTemplates.size()) + " monster templates");
    return true;
}

bool MonsterManager::ParseMonsterTemplateLine(const std::string& line, MonsterTemplate& monsterTemplate) {
    std::vector<std::string> tokens;
    std::stringstream ss(line);
    std::string token;

    while (std::getline(ss, token, '\t')) {
        tokens.push_back(token);
    }

    if (tokens.size() < 15) {
        return false;
    }

    try {
        monsterTemplate.monsterId = static_cast<WORD>(std::stoi(tokens[0]));
        monsterTemplate.monsterName = tokens[1];
        monsterTemplate.monsterType = static_cast<MonsterType>(std::stoi(tokens[2]));
        monsterTemplate.appr = static_cast<WORD>(std::stoi(tokens[3]));
        monsterTemplate.level = static_cast<BYTE>(std::stoi(tokens[4]));

        // 基础属性
        monsterTemplate.hp = std::stoi(tokens[5]);
        monsterTemplate.mp = std::stoi(tokens[6]);
        monsterTemplate.ac = std::stoi(tokens[7]);
        monsterTemplate.mac = std::stoi(tokens[8]);
        monsterTemplate.dc = std::stoi(tokens[9]);
        monsterTemplate.mc = std::stoi(tokens[10]);
        monsterTemplate.sc = std::stoi(tokens[11]);

        // 战斗属性
        monsterTemplate.attackSpeed = std::stoi(tokens[12]);
        monsterTemplate.attackRange = std::stoi(tokens[13]);
        monsterTemplate.expValue = static_cast<DWORD>(std::stoi(tokens[14]));

        // 可选字段
        if (tokens.size() > 15) {
            monsterTemplate.viewRange = std::stoi(tokens[15]);
        }
        if (tokens.size() > 16) {
            monsterTemplate.aggroRange = std::stoi(tokens[16]);
        }
        if (tokens.size() > 17) {
            monsterTemplate.chaseRange = std::stoi(tokens[17]);
        }
        if (tokens.size() > 18) {
            monsterTemplate.moveSpeed = std::stoi(tokens[18]);
        }
        if (tokens.size() > 19) {
            monsterTemplate.canMove = (std::stoi(tokens[19]) != 0);
        }
        if (tokens.size() > 20) {
            monsterTemplate.canAttack = (std::stoi(tokens[20]) != 0);
        }
        if (tokens.size() > 21) {
            monsterTemplate.canPoison = (std::stoi(tokens[21]) != 0);
        }
        if (tokens.size() > 22) {
            monsterTemplate.canParalyze = (std::stoi(tokens[22]) != 0);
        }
        if (tokens.size() > 23) {
            monsterTemplate.magicResist = std::stoi(tokens[23]);
        }
        if (tokens.size() > 24) {
            monsterTemplate.respawnTime = static_cast<DWORD>(std::stoi(tokens[24]));
        }
        if (tokens.size() > 25) {
            // 解析掉落物品
            ParseDropItemsString(tokens[25], monsterTemplate.dropItems);
        }
        if (tokens.size() > 26) {
            monsterTemplate.description = tokens[26];
        }

        return true;
    } catch (const std::exception& e) {
        Logger::Error("Error parsing monster template: " + std::string(e.what()));
        return false;
    }
}

bool MonsterManager::ParseDropItemsString(const std::string& dropStr, std::vector<DropItem>& dropItems) {
    if (dropStr.empty()) return true;

    // 格式: itemId1:rate1:min1:max1;itemId2:rate2:min2:max2;...
    std::stringstream ss(dropStr);
    std::string itemStr;

    while (std::getline(ss, itemStr, ';')) {
        if (itemStr.empty()) continue;

        std::vector<std::string> parts;
        std::stringstream itemSS(itemStr);
        std::string part;

        while (std::getline(itemSS, part, ':')) {
            parts.push_back(part);
        }

        if (parts.size() >= 4) {
            try {
                DropItem dropItem;
                dropItem.itemIndex = static_cast<WORD>(std::stoi(parts[0]));
                dropItem.dropRate = std::stof(parts[1]) / 100.0f; // 转换为0-1范围
                dropItem.minCount = std::stoi(parts[2]);
                dropItem.maxCount = std::stoi(parts[3]);
                dropItems.push_back(dropItem);
            } catch (const std::exception& e) {
                Logger::Warning("Error parsing drop item: " + itemStr);
            }
        }
    }

    return true;
}

bool MonsterManager::LoadSpawnPoints(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        Logger::Warning("Cannot open monster spawn points file: " + filename + " (using templates only)");
        return true; // 不是致命错误
    }

    std::lock_guard<std::mutex> lock(m_spawnPointsMutex);
    m_spawnPoints.clear();
    m_spawnPointsByMap.clear();

    std::string line;
    bool isFirstLine = true;
    int lineNumber = 0;

    while (std::getline(file, line)) {
        lineNumber++;

        if (isFirstLine || line.empty() || line[0] == '#' || line[0] == ';') {
            isFirstLine = false;
            continue;
        }

        MonsterSpawnPoint spawnPoint;
        if (ParseSpawnPointLine(line, spawnPoint)) {
            size_t index = m_spawnPoints.size();
            m_spawnPoints.push_back(spawnPoint);
            m_spawnPointsByMap[spawnPoint.mapName].push_back(index);
        } else {
            Logger::Warning("Failed to parse spawn point line " + std::to_string(lineNumber) + ": " + line);
        }
    }

    file.close();
    Logger::Info("Loaded " + std::to_string(m_spawnPoints.size()) + " monster spawn points");
    return true;
}

bool MonsterManager::ParseSpawnPointLine(const std::string& line, MonsterSpawnPoint& spawnPoint) {
    std::vector<std::string> tokens;
    std::stringstream ss(line);
    std::string token;

    while (std::getline(ss, token, '\t')) {
        tokens.push_back(token);
    }

    if (tokens.size() < 7) {
        return false;
    }

    try {
        spawnPoint.monsterTemplateId = static_cast<WORD>(std::stoi(tokens[0]));
        spawnPoint.mapName = tokens[1];
        spawnPoint.position.x = std::stoi(tokens[2]);
        spawnPoint.position.y = std::stoi(tokens[3]);
        spawnPoint.spawnRange = std::stoi(tokens[4]);
        spawnPoint.maxCount = std::stoi(tokens[5]);
        spawnPoint.respawnTime = static_cast<DWORD>(std::stoi(tokens[6]));

        if (tokens.size() > 7) {
            spawnPoint.isActive = (std::stoi(tokens[7]) != 0);
        }
        if (tokens.size() > 8) {
            spawnPoint.minLevel = std::stoi(tokens[8]);
        }
        if (tokens.size() > 9) {
            spawnPoint.maxLevel = std::stoi(tokens[9]);
        }
        if (tokens.size() > 10) {
            spawnPoint.spawnCondition = tokens[10];
        }

        return true;
    } catch (const std::exception& e) {
        Logger::Error("Error parsing spawn point: " + std::string(e.what()));
        return false;
    }
}

std::shared_ptr<Monster> MonsterManager::CreateMonster(WORD monsterTemplateId, const std::string& mapName, const Point& pos) {
    const MonsterTemplate* monsterTemplate = GetMonsterTemplate(monsterTemplateId);
    if (!monsterTemplate) {
        Logger::Error("Monster template not found: " + std::to_string(monsterTemplateId));
        return nullptr;
    }

    return CreateMonsterFromTemplate(*monsterTemplate, mapName, pos);
}

std::shared_ptr<Monster> MonsterManager::CreateMonsterFromTemplate(const MonsterTemplate& monsterTemplate, const std::string& mapName, const Point& pos) {
    std::shared_ptr<Monster> monster = nullptr;

    // 根据怪物类型创建不同的对象
    switch (monsterTemplate.monsterType) {
        case MonsterType::ELITE:
            monster = std::make_shared<EliteMonster>();
            break;
        case MonsterType::BOSS:
            monster = std::make_shared<BossMonster>();
            break;
        case MonsterType::NORMAL:
        case MonsterType::GUARD:
        case MonsterType::PASSIVE:
        case MonsterType::UNDEAD:
        case MonsterType::ANIMAL:
        case MonsterType::DEMON:
        case MonsterType::HUMANOID:
        default:
            monster = std::make_shared<Monster>();
            break;
    }

    if (!monster) {
        Logger::Error("Failed to create monster object");
        return nullptr;
    }

    // 设置怪物属性
    SetupMonsterFromTemplate(monster, monsterTemplate);
    monster->SetMapName(mapName);
    monster->SetCurrentPos(pos);
    monster->SetSpawnPoint(pos);

    // 初始化怪物
    monster->Initialize();

    // 添加到管理列表
    {
        std::unique_lock<std::shared_mutex> lock(m_monstersMutex);
        m_monsters[monster->GetObjectId()] = monster;
        m_monstersByMap[mapName].push_back(monster->GetObjectId());
    }

    Logger::Debug("Created monster: " + monster->GetCharName() + " at " + mapName +
                  "(" + std::to_string(pos.x) + "," + std::to_string(pos.y) + ")");

    return monster;
}

void MonsterManager::SetupMonsterFromTemplate(std::shared_ptr<Monster> monster, const MonsterTemplate& monsterTemplate) {
    if (!monster) return;

    // 基本属性
    monster->SetCharName(monsterTemplate.monsterName);
    monster->SetAppr(monsterTemplate.appr);
    monster->SetMonsterType(monsterTemplate.monsterType);
    monster->SetMonsterLevel(monsterTemplate.level);

    // 生命值和魔法值
    monster->SetMaxHP(monsterTemplate.hp);
    monster->SetHP(monsterTemplate.hp);
    monster->SetMaxMP(monsterTemplate.mp);
    monster->SetMP(monsterTemplate.mp);

    // 攻击和防御
    monster->SetDC(monsterTemplate.dc);
    monster->SetMC(monsterTemplate.mc);
    monster->SetSC(monsterTemplate.sc);
    monster->SetAC(monsterTemplate.ac);
    monster->SetMAC(monsterTemplate.mac);

    // 战斗属性
    monster->SetAttackSpeed(monsterTemplate.attackSpeed);
    monster->SetAttackRange(monsterTemplate.attackRange);
    monster->SetViewRange(monsterTemplate.viewRange);
    monster->SetAggroRange(monsterTemplate.aggroRange);
    monster->SetChaseRange(monsterTemplate.chaseRange);

    // 特殊能力
    monster->SetCanPoison(monsterTemplate.canPoison);
    monster->SetCanParalyze(monsterTemplate.canParalyze);
    monster->SetMagicResist(monsterTemplate.magicResist);

    // 经验值
    monster->SetExpValue(monsterTemplate.expValue);

    // 重生时间
    monster->SetRespawnTime(monsterTemplate.respawnTime);

    // 掉落物品
    monster->ClearDropItems();
    for (const DropItem& dropItem : monsterTemplate.dropItems) {
        monster->AddDropItem(dropItem);
    }

    // 技能
    for (WORD skillId : monsterTemplate.skills) {
        monster->AddSkill(skillId, 1);
    }
}

void MonsterManager::SpawnAllMonsters() {
    std::lock_guard<std::mutex> lock(m_spawnPointsMutex);

    for (const MonsterSpawnPoint& spawnPoint : m_spawnPoints) {
        if (spawnPoint.isActive) {
            // 刷新到最大数量
            for (int i = 0; i < spawnPoint.maxCount; ++i) {
                SpawnMonster(spawnPoint);
            }
        }
    }

    UpdateStatistics();
    Logger::Info("Spawned monsters at " + std::to_string(m_spawnPoints.size()) + " spawn points");
}

bool MonsterManager::SpawnMonster(const MonsterSpawnPoint& spawnPoint) {
    // 检查是否可以刷新
    if (!ShouldRespawn(spawnPoint)) {
        return false;
    }

    // 获取刷新位置
    Point spawnPos = GetRandomSpawnPosition(spawnPoint);
    if (!CanSpawnAt(spawnPoint.mapName, spawnPos)) {
        return false;
    }

    // 创建怪物
    auto monster = CreateMonster(spawnPoint.monsterTemplateId, spawnPoint.mapName, spawnPos);
    if (!monster) {
        return false;
    }

    // 更新刷新点计数
    // TODO: 实现刷新点计数更新

    return true;
}

Point MonsterManager::GetRandomSpawnPosition(const MonsterSpawnPoint& spawnPoint) {
    if (spawnPoint.spawnRange <= 0) {
        return spawnPoint.position;
    }

    // 在范围内随机选择位置
    int offsetX = GenerateRandom(-spawnPoint.spawnRange, spawnPoint.spawnRange);
    int offsetY = GenerateRandom(-spawnPoint.spawnRange, spawnPoint.spawnRange);

    Point pos;
    pos.x = spawnPoint.position.x + offsetX;
    pos.y = spawnPoint.position.y + offsetY;

    return pos;
}

bool MonsterManager::CanSpawnAt(const std::string& mapName, const Point& pos) {
    // TODO: 检查位置是否可以刷新怪物
    // 需要检查地图是否存在、位置是否可行走等
    return true;
}

bool MonsterManager::ShouldRespawn(const MonsterSpawnPoint& spawnPoint) {
    // TODO: 实现刷新条件检查
    // 检查当前数量、时间间隔、特殊条件等
    return true;
}

void MonsterManager::Run() {
    DWORD currentTime = GetCurrentTime();

    // 处理怪物逻辑
    if (currentTime - m_lastRunTime >= 1000) { // 每秒处理一次
        ProcessMonsters();
        m_lastRunTime = currentTime;
    }

    // 处理AI
    if (currentTime - m_lastAIProcessTime >= m_aiProcessInterval) {
        ProcessAI();
        m_lastAIProcessTime = currentTime;
    }

    // 检查重生
    if (currentTime - m_lastRespawnCheckTime >= m_respawnCheckInterval) {
        CheckRespawns();
        m_lastRespawnCheckTime = currentTime;
    }
}

void MonsterManager::ProcessMonsters() {
    std::shared_lock<std::shared_mutex> lock(m_monstersMutex);

    for (auto& pair : m_monsters) {
        auto monster = pair.second;
        if (monster) {
            monster->Run();
        }
    }
}

void MonsterManager::ProcessAI() {
    std::shared_lock<std::shared_mutex> lock(m_monstersMutex);

    for (auto& pair : m_monsters) {
        auto monster = pair.second;
        if (monster && monster->IsAlive()) {
            // AI处理在Monster::Run()中进行
        }
    }
}

void MonsterManager::CheckRespawns() {
    std::lock_guard<std::mutex> lock(m_spawnPointsMutex);

    DWORD currentTime = GetCurrentTime();

    for (const MonsterSpawnPoint& spawnPoint : m_spawnPoints) {
        if (!spawnPoint.isActive) continue;

        // 检查是否需要重生
        if (currentTime - spawnPoint.lastSpawnTime >= static_cast<DWORD>(spawnPoint.respawnTime * m_globalRespawnRate)) {
            // TODO: 检查当前数量并决定是否刷新
            if (spawnPoint.currentCount < spawnPoint.maxCount) {
                SpawnMonster(spawnPoint);
            }
        }
    }
}

bool MonsterManager::AddMonsterTemplate(const MonsterTemplate& monsterTemplate) {
    std::lock_guard<std::mutex> lock(m_templatesMutex);
    m_monsterTemplates[monsterTemplate.monsterId] = monsterTemplate;
    return true;
}

const MonsterTemplate* MonsterManager::GetMonsterTemplate(WORD monsterId) const {
    std::lock_guard<std::mutex> lock(m_templatesMutex);
    auto it = m_monsterTemplates.find(monsterId);
    return (it != m_monsterTemplates.end()) ? &it->second : nullptr;
}

void MonsterManager::ClearMonsterTemplates() {
    std::lock_guard<std::mutex> lock(m_templatesMutex);
    m_monsterTemplates.clear();
}

bool MonsterManager::AddSpawnPoint(const MonsterSpawnPoint& spawnPoint) {
    std::lock_guard<std::mutex> lock(m_spawnPointsMutex);
    size_t index = m_spawnPoints.size();
    m_spawnPoints.push_back(spawnPoint);
    m_spawnPointsByMap[spawnPoint.mapName].push_back(index);
    return true;
}

void MonsterManager::ClearSpawnPoints() {
    std::lock_guard<std::mutex> lock(m_spawnPointsMutex);
    m_spawnPoints.clear();
    m_spawnPointsByMap.clear();
}

std::shared_ptr<Monster> MonsterManager::FindMonster(uint32_t objectId) const {
    std::shared_lock<std::shared_mutex> lock(m_monstersMutex);
    auto it = m_monsters.find(objectId);
    return (it != m_monsters.end()) ? it->second : nullptr;
}

std::vector<std::shared_ptr<Monster>> MonsterManager::GetMonstersInMap(const std::string& mapName) const {
    std::vector<std::shared_ptr<Monster>> result;
    std::shared_lock<std::shared_mutex> lock(m_monstersMutex);

    auto it = m_monstersByMap.find(mapName);
    if (it != m_monstersByMap.end()) {
        for (uint32_t objectId : it->second) {
            auto monsterIt = m_monsters.find(objectId);
            if (monsterIt != m_monsters.end() && monsterIt->second) {
                result.push_back(monsterIt->second);
            }
        }
    }

    return result;
}

std::vector<std::shared_ptr<Monster>> MonsterManager::GetMonstersInRange(const std::string& mapName, const Point& center, int range) const {
    std::vector<std::shared_ptr<Monster>> result;
    auto monstersInMap = GetMonstersInMap(mapName);

    for (auto monster : monstersInMap) {
        if (monster) {
            Point monsterPos = monster->GetCurrentPos();
            double distance = GetDistance(center, monsterPos);
            if (distance <= range) {
                result.push_back(monster);
            }
        }
    }

    return result;
}

std::vector<std::shared_ptr<Monster>> MonsterManager::GetMonstersByType(MonsterType type) const {
    std::vector<std::shared_ptr<Monster>> result;
    std::shared_lock<std::shared_mutex> lock(m_monstersMutex);

    for (const auto& pair : m_monsters) {
        auto monster = pair.second;
        if (monster && monster->GetMonsterType() == type) {
            result.push_back(monster);
        }
    }

    return result;
}

bool MonsterManager::RemoveMonster(uint32_t objectId) {
    std::unique_lock<std::shared_mutex> lock(m_monstersMutex);

    auto it = m_monsters.find(objectId);
    if (it != m_monsters.end()) {
        auto monster = it->second;

        // 从地图列表中移除
        if (monster) {
            std::string mapName = monster->GetMapName();
            auto mapIt = m_monstersByMap.find(mapName);
            if (mapIt != m_monstersByMap.end()) {
                auto& objectIds = mapIt->second;
                objectIds.erase(std::remove(objectIds.begin(), objectIds.end(), objectId), objectIds.end());
            }

            // 清理怪物
            monster->Finalize();
        }

        // 从主映射中移除
        m_monsters.erase(it);

        return true;
    }

    return false;
}

void MonsterManager::RemoveAllMonsters() {
    std::unique_lock<std::shared_mutex> lock(m_monstersMutex);

    // 清理所有怪物
    for (auto& pair : m_monsters) {
        if (pair.second) {
            pair.second->Finalize();
        }
    }

    m_monsters.clear();
    m_monstersByMap.clear();
    m_summonedMonsters.clear();
}

void MonsterManager::RemoveMonstersInMap(const std::string& mapName) {
    auto monsters = GetMonstersInMap(mapName);

    for (auto monster : monsters) {
        if (monster) {
            RemoveMonster(monster->GetObjectId());
        }
    }
}

void MonsterManager::UpdateStatistics() {
    std::shared_lock<std::shared_mutex> lock(m_monstersMutex);

    m_statistics.totalMonsters = static_cast<int>(m_monsters.size());
    m_statistics.aliveMonsters = 0;
    m_statistics.normalMonsters = 0;
    m_statistics.eliteMonsters = 0;
    m_statistics.bossMonsters = 0;
    m_statistics.summonedMonsters = 0;

    for (const auto& pair : m_monsters) {
        auto monster = pair.second;
        if (monster) {
            if (monster->IsAlive()) {
                m_statistics.aliveMonsters++;
            }

            // 统计不同类型的怪物
            switch (monster->GetMonsterType()) {
                case MonsterType::NORMAL:
                case MonsterType::GUARD:
                case MonsterType::PASSIVE:
                case MonsterType::UNDEAD:
                case MonsterType::ANIMAL:
                case MonsterType::DEMON:
                case MonsterType::HUMANOID:
                    m_statistics.normalMonsters++;
                    break;
                case MonsterType::ELITE:
                    m_statistics.eliteMonsters++;
                    break;
                case MonsterType::BOSS:
                    m_statistics.bossMonsters++;
                    break;
            }

            if (monster->IsSummoned()) {
                m_statistics.summonedMonsters++;
            }
        }
    }

    m_statistics.activeSpawnPoints = 0;
    {
        std::lock_guard<std::mutex> spawnLock(m_spawnPointsMutex);
        for (const auto& spawnPoint : m_spawnPoints) {
            if (spawnPoint.isActive) {
                m_statistics.activeSpawnPoints++;
            }
        }
    }

    m_statistics.lastUpdateTime = GetCurrentTime();
}

} // namespace MirServer

// NetworkManager.cpp - 网络管理器实现
#include "NetworkManager.h"
#include "../Common/Logger.h"
#include <iostream>
#include <chrono>
#include <cstring>

namespace MirServer {
namespace Network {

// ==================== NetworkManager 实现 ====================

NetworkManager::NetworkManager() {
}

NetworkManager::~NetworkManager() {
    Shutdown();
}

bool NetworkManager::Initialize() {
#ifdef _WIN32
    // 初始化 Winsock
    if (WSAStartup(MAKEWORD(2, 2), &m_wsaData) != 0) {
        std::cerr << "WSAStartup failed" << std::endl;
        return false;
    }
#endif
    return true;
}

void NetworkManager::Shutdown() {
    StopServer();
    
#ifdef _WIN32
    WSACleanup();
#endif
}

bool NetworkManager::StartServer(uint16_t port, int maxConnections) {
    if (m_isRunning) {
        return false;
    }
    
    m_serverPort = port;
    m_maxConnections = maxConnections;
    
    // 创建服务器套接字
    m_serverSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (m_serverSocket == INVALID_SOCKET) {
        std::cerr << "Failed to create server socket" << std::endl;
        return false;
    }
    
    // 设置套接字选项
    if (!SetSocketOptions(m_serverSocket)) {
        CloseSocket(m_serverSocket);
        return false;
    }
    
    // 绑定地址
    sockaddr_in serverAddr{};
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = INADDR_ANY;
    serverAddr.sin_port = htons(port);
    
    if (bind(m_serverSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
        std::cerr << "Failed to bind server socket to port " << port << std::endl;
        CloseSocket(m_serverSocket);
        return false;
    }
    
    // 开始监听
    if (listen(m_serverSocket, SOMAXCONN) == SOCKET_ERROR) {
        std::cerr << "Failed to listen on server socket" << std::endl;
        CloseSocket(m_serverSocket);
        return false;
    }
    
    // 设置为非阻塞模式
    if (!SetSocketNonBlocking(m_serverSocket)) {
        CloseSocket(m_serverSocket);
        return false;
    }
    
    // 启动工作线程
    m_isRunning = true;
    m_shouldStop = false;
    
    m_acceptThread = std::thread(&NetworkManager::AcceptThread, this);
    m_ioThread = std::thread(&NetworkManager::IOThread, this);
    m_eventThread = std::thread(&NetworkManager::ProcessEvents, this);
    
    std::cout << "Server started on port " << port << std::endl;
    return true;
}

void NetworkManager::StopServer() {
    if (!m_isRunning) {
        return;
    }
    
    m_shouldStop = true;
    m_eventCV.notify_all();
    
    // 关闭服务器套接字
    if (m_serverSocket != INVALID_SOCKET) {
        CloseSocket(m_serverSocket);
        m_serverSocket = INVALID_SOCKET;
    }
    
    // 断开所有客户端
    {
        std::lock_guard<std::mutex> lock(m_clientsMutex);
        for (auto& pair : m_clients) {
            pair.second->Disconnect();
        }
        m_clients.clear();
    }
    
    // 等待线程结束
    if (m_acceptThread.joinable()) {
        m_acceptThread.join();
    }
    if (m_ioThread.joinable()) {
        m_ioThread.join();
    }
    if (m_eventThread.joinable()) {
        m_eventThread.join();
    }
    
    m_isRunning = false;
    std::cout << "Server stopped" << std::endl;
}

void NetworkManager::AcceptThread() {
    while (!m_shouldStop) {
        sockaddr_in clientAddr{};
        int addrLen = sizeof(clientAddr);
        
        SOCKET_TYPE clientSocket = accept(m_serverSocket, (sockaddr*)&clientAddr, &addrLen);
        if (clientSocket == INVALID_SOCKET) {
            if (!m_shouldStop) {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            continue;
        }
        
        // 检查连接数限制
        if (GetClientCount() >= static_cast<size_t>(m_maxConnections)) {
            CloseSocket(clientSocket);
            continue;
        }
        
        // 设置客户端套接字选项
        SetSocketOptions(clientSocket);
        SetSocketNonBlocking(clientSocket);
        
        // 获取客户端信息
        char ipStr[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &clientAddr.sin_addr, ipStr, INET_ADDRSTRLEN);
        std::string remoteIP(ipStr);
        uint16_t remotePort = ntohs(clientAddr.sin_port);
        
        // 创建客户端连接
        uint32_t clientId = GenerateClientId();
        auto client = std::make_shared<ClientConnection>(clientId, clientSocket, remoteIP, remotePort);
        
        // 添加到客户端列表
        {
            std::lock_guard<std::mutex> lock(m_clientsMutex);
            m_clients[clientId] = client;
        }
        
        // 更新统计
        {
            std::lock_guard<std::mutex> lock(m_statsMutex);
            m_stats.currentConnections++;
            m_stats.totalConnections++;
        }
        
        // 发送连接事件
        NetworkEvent event;
        event.type = NetworkEventType::CLIENT_CONNECTED;
        event.client = client;
        PushEvent(event);
        
        std::cout << "Client connected: " << remoteIP << ":" << remotePort << " (ID: " << clientId << ")" << std::endl;
    }
}

void NetworkManager::IOThread() {
    while (!m_shouldStop) {
        std::vector<std::shared_ptr<ClientConnection>> clientsCopy;
        
        // 复制客户端列表
        {
            std::lock_guard<std::mutex> lock(m_clientsMutex);
            for (const auto& pair : m_clients) {
                clientsCopy.push_back(pair.second);
            }
        }
        
        // 处理每个客户端的IO
        for (auto& client : clientsCopy) {
            if (!client->IsConnected()) {
                continue;
            }
            
            // 接收数据
            if (client->Receive()) {
                // 检查是否有完整的数据包
                while (client->HasCompletePacket()) {
                    std::vector<uint8_t> packet;
                    if (client->GetPacket(packet)) {
                        // 更新统计
                        {
                            std::lock_guard<std::mutex> lock(m_statsMutex);
                            m_stats.totalPacketsReceived++;
                            m_stats.totalBytesReceived += packet.size();
                        }
                        
                        // 发送数据包事件
                        NetworkEvent event;
                        event.type = NetworkEventType::PACKET_RECEIVED;
                        event.client = client;
                        event.data = std::move(packet);
                        PushEvent(event);
                    }
                }
            } else if (!client->IsConnected()) {
                // 客户端断开连接
                DisconnectClient(client->GetId());
            }
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
}

void NetworkManager::ProcessEvents() {
    while (!m_shouldStop) {
        NetworkEvent event;
        if (PopEvent(event)) {
            // 调用事件回调
            if (m_eventCallback) {
                m_eventCallback(event);
            }
            
            // 调用包处理器
            if (m_packetHandler) {
                switch (event.type) {
                    case NetworkEventType::CLIENT_CONNECTED:
                        m_packetHandler->OnClientConnected(event.client);
                        break;
                        
                    case NetworkEventType::CLIENT_DISCONNECTED:
                        m_packetHandler->OnClientDisconnected(event.client);
                        break;
                        
                    case NetworkEventType::PACKET_RECEIVED:
                        if (event.data.size() >= sizeof(Protocol::PacketHeader)) {
                            const Protocol::PacketHeader* header = reinterpret_cast<const Protocol::PacketHeader*>(event.data.data());
                            const uint8_t* data = event.data.data() + sizeof(Protocol::PacketHeader);
                            size_t dataSize = event.data.size() - sizeof(Protocol::PacketHeader);
                            m_packetHandler->HandlePacket(event.client, *header, data, dataSize);
                        }
                        break;
                        
                    case NetworkEventType::ERROR_OCCURRED:
                        m_packetHandler->OnError(event.errorMsg);
                        break;
                }
            }
        } else {
            std::unique_lock<std::mutex> lock(m_eventMutex);
            m_eventCV.wait_for(lock, std::chrono::milliseconds(10));
        }
    }
}

void NetworkManager::BroadcastPacket(const void* data, size_t size) {
    std::lock_guard<std::mutex> lock(m_clientsMutex);
    for (const auto& pair : m_clients) {
        if (pair.second->IsConnected()) {
            pair.second->Send(data, size);
        }
    }
}

void NetworkManager::SendToClient(uint32_t clientId, const void* data, size_t size) {
    std::lock_guard<std::mutex> lock(m_clientsMutex);
    auto it = m_clients.find(clientId);
    if (it != m_clients.end() && it->second->IsConnected()) {
        it->second->Send(data, size);
        
        // 更新统计
        {
            std::lock_guard<std::mutex> lock(m_statsMutex);
            m_stats.totalPacketsSent++;
            m_stats.totalBytesSent += size;
        }
    }
}

void NetworkManager::DisconnectClient(uint32_t clientId) {
    std::shared_ptr<ClientConnection> client;
    
    {
        std::lock_guard<std::mutex> lock(m_clientsMutex);
        auto it = m_clients.find(clientId);
        if (it != m_clients.end()) {
            client = it->second;
            m_clients.erase(it);
        }
    }
    
    if (client) {
        client->Disconnect();
        
        // 更新统计
        {
            std::lock_guard<std::mutex> lock(m_statsMutex);
            if (m_stats.currentConnections > 0) {
                m_stats.currentConnections--;
            }
        }
        
        // 发送断开连接事件
        NetworkEvent event;
        event.type = NetworkEventType::CLIENT_DISCONNECTED;
        event.client = client;
        PushEvent(event);
        
        std::cout << "Client disconnected: " << client->GetRemoteIP() << ":" 
                  << client->GetRemotePort() << " (ID: " << clientId << ")" << std::endl;
    }
}

size_t NetworkManager::GetClientCount() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_clientsMutex));
    return m_clients.size();
}

NetworkManager::Statistics NetworkManager::GetStatistics() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_statsMutex));
    return m_stats;
}

bool NetworkManager::SetSocketNonBlocking(SOCKET_TYPE socket) {
#ifdef _WIN32
    u_long mode = 1;
    return ioctlsocket(socket, FIONBIO, &mode) == 0;
#else
    int flags = fcntl(socket, F_GETFL, 0);
    if (flags == -1) return false;
    return fcntl(socket, F_SETFL, flags | O_NONBLOCK) != -1;
#endif
}

bool NetworkManager::SetSocketOptions(SOCKET_TYPE socket) {
    // 禁用 Nagle 算法
    int nodelay = 1;
    if (setsockopt(socket, IPPROTO_TCP, TCP_NODELAY, (const char*)&nodelay, sizeof(nodelay)) == SOCKET_ERROR) {
        return false;
    }
    
    // 设置 SO_REUSEADDR
    int reuse = 1;
    if (setsockopt(socket, SOL_SOCKET, SO_REUSEADDR, (const char*)&reuse, sizeof(reuse)) == SOCKET_ERROR) {
        return false;
    }
    
    // 设置发送和接收缓冲区大小
    int bufSize = 65536;
    setsockopt(socket, SOL_SOCKET, SO_SNDBUF, (const char*)&bufSize, sizeof(bufSize));
    setsockopt(socket, SOL_SOCKET, SO_RCVBUF, (const char*)&bufSize, sizeof(bufSize));
    
    return true;
}

void NetworkManager::CloseSocket(SOCKET_TYPE socket) {
#ifdef _WIN32
    closesocket(socket);
#else
    close(socket);
#endif
}

uint32_t NetworkManager::GenerateClientId() {
    return m_nextClientId.fetch_add(1);
}

void NetworkManager::PushEvent(const NetworkEvent& event) {
    {
        std::lock_guard<std::mutex> lock(m_eventMutex);
        m_eventQueue.push(event);
    }
    m_eventCV.notify_one();
}

bool NetworkManager::PopEvent(NetworkEvent& event) {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    if (m_eventQueue.empty()) {
        return false;
    }
    event = m_eventQueue.front();
    m_eventQueue.pop();
    return true;
}

// ==================== ClientConnection 实现 ====================

ClientConnection::ClientConnection(uint32_t id, SOCKET_TYPE socket, const std::string& remoteIP, uint16_t remotePort)
    : m_id(id), m_socket(socket), m_remoteIP(remoteIP), m_remotePort(remotePort) {
    
    m_recvBuffer.resize(RECV_BUFFER_SIZE);
    m_lastActiveTime = GetCurrentTime();
    
    // 初始化会话信息
    m_sessionInfo.socket = socket;
    m_sessionInfo.remoteIP = remoteIP;
    m_sessionInfo.remotePort = remotePort;
    m_sessionInfo.connectTime = GetCurrentTime();
    m_sessionInfo.lastActiveTime = m_sessionInfo.connectTime;
    m_sessionInfo.isValid = true;
}

ClientConnection::~ClientConnection() {
    Disconnect();
}

bool ClientConnection::Send(const void* data, size_t size) {
    if (!m_connected || size == 0) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_sendMutex);
    
    // 直接发送数据
    int totalSent = 0;
    while (totalSent < static_cast<int>(size)) {
        int result = send(m_socket, 
                         static_cast<const char*>(data) + totalSent, 
                         static_cast<int>(size - totalSent), 
                         0);
        
        if (result == SOCKET_ERROR) {
#ifdef _WIN32
            int error = WSAGetLastError();
            if (error == WSAEWOULDBLOCK) {
                // 暂时无法发送，将剩余数据放入发送缓冲区
                m_sendBuffer.insert(m_sendBuffer.end(), 
                                  static_cast<const uint8_t*>(data) + totalSent,
                                  static_cast<const uint8_t*>(data) + size);
                break;
            }
#else
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                m_sendBuffer.insert(m_sendBuffer.end(), 
                                  static_cast<const uint8_t*>(data) + totalSent,
                                  static_cast<const uint8_t*>(data) + size);
                break;
            }
#endif
            // 发送错误
            Disconnect();
            return false;
        }
        
        totalSent += result;
        m_bytesSent += result;
    }
    
    m_lastActiveTime = GetCurrentTime();
    return true;
}

bool ClientConnection::Receive() {
    if (!m_connected) {
        return false;
    }
    
    // 接收数据
    int result = recv(m_socket, 
                     reinterpret_cast<char*>(m_recvBuffer.data() + m_recvSize),
                     static_cast<int>(RECV_BUFFER_SIZE - m_recvSize),
                     0);
    
    if (result > 0) {
        m_recvSize += result;
        m_bytesReceived += result;
        m_lastActiveTime = GetCurrentTime();
        m_sessionInfo.lastActiveTime = m_lastActiveTime;
        
        // 处理接收到的数据
        ProcessReceivedData();
        return true;
    } else if (result == 0) {
        // 连接关闭
        Disconnect();
        return false;
    } else {
        // 检查错误
#ifdef _WIN32
        int error = WSAGetLastError();
        if (error != WSAEWOULDBLOCK) {
            Disconnect();
            return false;
        }
#else
        if (errno != EAGAIN && errno != EWOULDBLOCK) {
            Disconnect();
            return false;
        }
#endif
    }
    
    return true;
}

void ClientConnection::ProcessReceivedData() {
    size_t processed = 0;
    
    while (processed + sizeof(Protocol::PacketHeader) <= m_recvSize) {
        // 读取包头
        const Protocol::PacketHeader* header = reinterpret_cast<const Protocol::PacketHeader*>(m_recvBuffer.data() + processed);
        
        // 检查数据包长度
        if (header->length < sizeof(Protocol::PacketHeader) || header->length > 65535) {
            // 无效的数据包
            Disconnect();
            return;
        }
        
        // 检查是否有完整的数据包
        if (processed + header->length > m_recvSize) {
            break;
        }
        
        // 提取完整的数据包
        std::vector<uint8_t> packet(header->length);
        std::memcpy(packet.data(), m_recvBuffer.data() + processed, header->length);
        
        // 添加到数据包队列
        {
            std::lock_guard<std::mutex> lock(m_packetMutex);
            m_packetQueue.push(std::move(packet));
        }
        
        processed += header->length;
    }
    
    // 移动未处理的数据到缓冲区开头
    if (processed > 0 && processed < m_recvSize) {
        std::memmove(m_recvBuffer.data(), m_recvBuffer.data() + processed, m_recvSize - processed);
        m_recvSize -= processed;
    } else if (processed == m_recvSize) {
        m_recvSize = 0;
    }
}

bool ClientConnection::HasCompletePacket() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_packetMutex));
    return !m_packetQueue.empty();
}

bool ClientConnection::GetPacket(std::vector<uint8_t>& packet) {
    std::lock_guard<std::mutex> lock(m_packetMutex);
    if (m_packetQueue.empty()) {
        return false;
    }
    packet = std::move(m_packetQueue.front());
    m_packetQueue.pop();
    return true;
}

void ClientConnection::Disconnect() {
    if (m_connected.exchange(false)) {
        if (m_socket != INVALID_SOCKET) {
#ifdef _WIN32
            closesocket(m_socket);
#else
            close(m_socket);
#endif
            m_socket = INVALID_SOCKET;
        }
        
        m_sessionInfo.isValid = false;
    }
}

} // namespace Network
} // namespace MirServer 
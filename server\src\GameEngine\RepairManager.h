#pragma once

#include "../Common/Types.h"
#include "../BaseObject/PlayObject.h"
#include <unordered_map>
#include <shared_mutex>

namespace MirServer {

// 修理数据结构
struct RepairData {
    WORD itemIndex;         // 物品索引
    DWORD cost;            // 修理费用
    WORD maxDurability;    // 最大耐久度
    WORD currentDurability; // 当前耐久度
    bool canRepair;        // 是否可以修理
};

// 修理结果
enum class RepairResult {
    SUCCESS,               // 修理成功
    ITEM_NOT_FOUND,       // 物品不存在
    INSUFFICIENT_GOLD,    // 金币不足
    ITEM_NOT_DAMAGED,     // 物品未损坏
    ITEM_CANNOT_REPAIR,   // 物品无法修理
    REPAIR_FAILED         // 修理失败
};

// 修理管理器类（对应delphi的修理系统）
class RepairManager {
public:
    RepairManager();
    ~RepairManager();

    // 初始化和清理
    bool Initialize();
    void Finalize();

    // 修理操作
    RepairResult RepairItem(PlayObject* player, WORD itemIndex);
    RepairResult RepairAllItems(PlayObject* player);

    // 查询修理费用
    DWORD GetRepairCost(PlayObject* player, WORD itemIndex);
    DWORD GetRepairAllCost(PlayObject* player);

    // 检查物品状态
    bool CanRepairItem(const UserItem& item);
    bool IsItemDamaged(const UserItem& item);
    WORD GetItemDurability(const UserItem& item);
    WORD GetMaxDurability(const UserItem& item);

    // 修理费用计算
    DWORD CalculateRepairCost(const UserItem& item);
    DWORD CalculateRepairCostByDamage(WORD maxDura, WORD currentDura, WORD itemValue);

    // 统计信息
    struct RepairStatistics {
        DWORD totalRepairs;
        DWORD totalCost;
        DWORD successfulRepairs;
        DWORD failedRepairs;
    };

    RepairStatistics GetStatistics() const;
    void ResetStatistics();

private:
    bool m_initialized;
    mutable std::shared_mutex m_statsMutex;
    RepairStatistics m_statistics;

    // 内部方法
    bool ValidateRepairRequest(PlayObject* player, WORD itemIndex);
    bool ExecuteRepair(PlayObject* player, UserItem& item, DWORD cost);
    void UpdateStatistics(bool success, DWORD cost);

    // 修理费用配置
    static constexpr double REPAIR_COST_MULTIPLIER = 0.1; // 修理费用倍数
    static constexpr DWORD MIN_REPAIR_COST = 100;         // 最小修理费用
    static constexpr DWORD MAX_REPAIR_COST = 100000;      // 最大修理费用
};

} // namespace MirServer

#!/bin/bash

echo "=== 传奇服务器重构项目构建测试 ==="

# 设置构建目录
BUILD_DIR="build_test"
SRC_DIR="$(pwd)"

# 清理之前的构建
if [ -d "$BUILD_DIR" ]; then
    echo "清理之前的构建目录..."
    rm -rf "$BUILD_DIR"
fi

# 创建构建目录
echo "创建构建目录: $BUILD_DIR"
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# 检查CMake版本
echo "检查CMake版本..."
cmake --version
if [ $? -ne 0 ]; then
    echo "错误: 未找到CMake，请先安装CMake 3.16或更高版本"
    exit 1
fi

# 配置项目
echo "配置CMake项目..."
cmake "$SRC_DIR" -DCMAKE_BUILD_TYPE=Debug
if [ $? -ne 0 ]; then
    echo "错误: CMake配置失败"
    exit 1
fi

# 构建项目
echo "构建项目..."
cmake --build . --config Debug
if [ $? -ne 0 ]; then
    echo "错误: 项目构建失败"
    exit 1
fi

echo "=== 构建测试完成 ==="
echo "构建目录: $BUILD_DIR"
echo "可执行文件位置:"
find . -name "*.exe" -o -name "GameEngine" -o -name "LoginServer" -o -name "DBServer" -o -name "GateServer" 2>/dev/null

cd "$SRC_DIR"
echo "构建测试成功完成！" 
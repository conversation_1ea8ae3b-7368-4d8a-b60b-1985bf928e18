// GameData.h - 游戏数据结构定义
#pragma once

#include <string>
#include <cstdint>
#include "Types.h"  // 包含JobType等基础类型定义

namespace MirServer {

// 物品数据结构
struct StdItem {
    int32_t idx;           // 索引
    std::string name;      // 名称
    uint8_t stdMode;       // 标准模式
    uint8_t shape;         // 形状
    uint16_t weight;       // 重量
    uint8_t aniCount;      // 动画数量
    int16_t source;        // 来源
    uint8_t reserved;      // 保留
    uint16_t looks;        // 外观
    uint16_t duraMax;      // 最大耐久
    uint16_t ac;           // 防御力下限
    uint16_t ac2;          // 防御力上限
    uint16_t mac;          // 魔法防御下限
    uint16_t mac2;         // 魔法防御上限
    uint16_t dc;           // 物理攻击下限
    uint16_t dc2;          // 物理攻击上限
    uint16_t mc;           // 魔法攻击下限
    uint16_t mc2;          // 魔法攻击上限
    uint16_t sc;           // 道术攻击下限
    uint16_t sc2;          // 道术攻击上限
    uint8_t need;          // 需求类型 (0:等级 1:DC 2:MC 3:SC)
    uint8_t needLevel;     // 需要等级
    uint32_t price;        // 价格

    StdItem() : idx(0), stdMode(0), shape(0), weight(0), aniCount(0),
                source(0), reserved(0), looks(0), duraMax(0),
                ac(0), ac2(0), mac(0), mac2(0), dc(0), dc2(0),
                mc(0), mc2(0), sc(0), sc2(0), need(0), needLevel(0), price(0) {}
};

// 魔法数据结构
struct Magic {
    uint16_t magicId;          // 魔法ID
    std::string magicName;     // 魔法名称
    uint8_t effectType;        // 效果类型
    uint8_t effect;            // 效果
    uint16_t spell;            // 施法消耗
    uint16_t power;            // 威力
    uint16_t maxPower;         // 最大威力
    uint8_t job;               // 职业 (0:战士 1:法师 2:道士)
    uint8_t trainLevel[4];     // 各等级需求
    uint16_t maxTrain[4];      // 各等级训练值
    uint8_t trainLv;           // 训练等级
    uint32_t delayTime;        // 延迟时间
    uint8_t defSpell;          // 防御施法
    uint8_t defPower;          // 防御威力
    uint8_t defMaxPower;       // 防御最大威力
    std::string descr;         // 描述

    Magic() : magicId(0), effectType(0), effect(0), spell(0),
              power(0), maxPower(0), job(0), trainLv(0),
              delayTime(0), defSpell(0), defPower(0), defMaxPower(0) {
        for (int i = 0; i < 4; i++) {
            trainLevel[i] = 0;
            maxTrain[i] = 0;
        }
    }
};

// 物品类型枚举
enum class ItemType : uint8_t {
    // 装备类
    WEAPON = 5,        // 武器
    ARMOR = 10,        // 盔甲
    HELMET = 15,       // 头盔
    NECKLACE = 19,     // 项链
    BRACELET = 24,     // 手镯
    RING = 22,         // 戒指
    BELT = 54,         // 腰带
    BOOTS = 52,        // 靴子
    CHARM = 25,        // 护身符

    // 消耗品类
    DRUG = 0,          // 药品
    SCROLL = 3,        // 卷轴
    MEAT = 40,         // 肉类

    // 其他
    GOLD = 1,          // 金币
    QUEST = 2,         // 任务物品
    OTHER = 31         // 其他
};

// 需求类型枚举
enum class NeedType : uint8_t {
    LEVEL = 0,         // 等级
    DC = 1,            // 物理攻击
    MC = 2,            // 魔法攻击
    SC = 3             // 道术攻击
};

// 掉落物品结构
struct DropItem {
    WORD itemIndex = 0;        // 物品索引
    float dropRate = 0.0f;     // 掉落概率 (0.0-1.0)
    int minCount = 1;          // 最小数量
    int maxCount = 1;          // 最大数量

    DropItem() = default;
    DropItem(WORD index, float rate, int min = 1, int max = 1)
        : itemIndex(index), dropRate(rate), minCount(min), maxCount(max) {}
};

} // namespace MirServer
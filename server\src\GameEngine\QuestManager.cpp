#include "QuestManager.h"
#include "../BaseObject/PlayObject.h"
#include "../Common/Logger.h"
#include "../Protocol/PacketTypes.h"
#include <algorithm>
#include <fstream>
#include <sstream>

namespace MirServer {

// 全局实例
std::unique_ptr<QuestManager> g_QuestManager = nullptr;

QuestManager::QuestManager() {
    Logger::Info("QuestManager created");
}

QuestManager::~QuestManager() {
    Finalize();
    Logger::Info("QuestManager destroyed");
}

bool QuestManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    Logger::Info("Initializing QuestManager...");

    // 初始化统计信息
    m_statistics = {};
    m_lastProcessTime = GetCurrentTime();
    m_lastTimeoutCheckTime = GetCurrentTime();

    // 加载任务数据
    if (!LoadQuestData("data/quests.txt")) {
        Logger::Warning("Failed to load quest data, using default quests");

        // 创建一些默认任务
        QuestData defaultQuest;
        defaultQuest.questId = 1;
        defaultQuest.name = "新手任务";
        defaultQuest.description = "击败5只鸡";
        defaultQuest.type = QuestType::KILL_MONSTER;
        defaultQuest.minLevel = 1;
        defaultQuest.maxLevel = 10;
        defaultQuest.giveNPC = "新手导师";
        defaultQuest.finishNPC = "新手导师";

        QuestObjective objective;
        objective.type = QuestType::KILL_MONSTER;
        objective.target = "鸡";
        objective.requiredCount = 5;
        objective.description = "击败5只鸡";
        defaultQuest.objectives.push_back(objective);

        defaultQuest.reward.exp = 100;
        defaultQuest.reward.gold = 50;
        defaultQuest.reward.description = "经验100，金币50";

        AddQuest(defaultQuest);
    }

    m_initialized = true;
    Logger::Info("QuestManager initialized successfully");

    return true;
}

void QuestManager::Finalize() {
    if (!m_initialized) {
        return;
    }

    Logger::Info("Finalizing QuestManager...");

    // 保存所有玩家任务数据
    {
        std::shared_lock<std::shared_mutex> lock(m_playerQuestsMutex);
        for (const auto& pair : m_playerQuests) {
            SavePlayerQuestsToDB(pair.first, pair.second);
        }
    }

    // 清理数据
    {
        std::unique_lock<std::shared_mutex> questLock(m_questsMutex);
        std::unique_lock<std::shared_mutex> playerLock(m_playerQuestsMutex);

        m_quests.clear();
        m_playerQuests.clear();
        m_npcQuests.clear();
    }

    m_initialized = false;
    Logger::Info("QuestManager finalized");
}

bool QuestManager::LoadQuestData(const std::string& questFile) {
    std::ifstream file(questFile);
    if (!file.is_open()) {
        Logger::Error("Failed to open quest file: " + questFile);
        return false;
    }

    std::string line;
    int lineNumber = 0;

    while (std::getline(file, line)) {
        lineNumber++;

        // 跳过注释和空行
        if (line.empty() || line[0] == '#') {
            continue;
        }

        // 解析任务数据
        std::istringstream iss(line);
        std::string token;
        std::vector<std::string> tokens;

        while (std::getline(iss, token, '\t')) {
            tokens.push_back(token);
        }

        if (tokens.size() < 8) {
            Logger::Warning("Invalid quest data at line " + std::to_string(lineNumber));
            continue;
        }

        try {
            QuestData quest;
            quest.questId = static_cast<WORD>(std::stoi(tokens[0]));
            quest.name = tokens[1];
            quest.description = tokens[2];
            quest.type = static_cast<QuestType>(std::stoi(tokens[3]));
            quest.minLevel = static_cast<WORD>(std::stoi(tokens[4]));
            quest.maxLevel = static_cast<WORD>(std::stoi(tokens[5]));
            quest.giveNPC = tokens[6];
            quest.finishNPC = tokens[7];

            // 解析任务目标
            if (tokens.size() > 8) {
                QuestObjective objective;
                objective.type = quest.type;
                objective.target = tokens[8];
                objective.requiredCount = (tokens.size() > 9) ? std::stoi(tokens[9]) : 1;
                objective.description = quest.description;
                quest.objectives.push_back(objective);
            }

            // 解析奖励
            if (tokens.size() > 10) {
                quest.reward.exp = static_cast<DWORD>(std::stoi(tokens[10]));
            }
            if (tokens.size() > 11) {
                quest.reward.gold = static_cast<DWORD>(std::stoi(tokens[11]));
            }

            if (ValidateQuestData(quest)) {
                AddQuest(quest);
                Logger::Debug("Loaded quest: " + quest.name);
            } else {
                Logger::Warning("Invalid quest data: " + quest.name);
            }

        } catch (const std::exception& e) {
            Logger::Error("Error parsing quest at line " + std::to_string(lineNumber) + ": " + e.what());
        }
    }

    file.close();
    Logger::Info("Quest data loaded from: " + questFile);
    return true;
}

bool QuestManager::AddQuest(const QuestData& quest) {
    if (!ValidateQuestData(quest)) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_questsMutex);

    m_quests[quest.questId] = std::make_unique<QuestData>(quest);

    // 添加到NPC任务映射
    if (!quest.giveNPC.empty()) {
        m_npcQuests[quest.giveNPC].push_back(quest.questId);
    }

    Logger::Debug("Quest added: " + quest.name);
    return true;
}

const QuestData* QuestManager::GetQuest(WORD questId) const {
    std::shared_lock<std::shared_mutex> lock(m_questsMutex);

    auto it = m_quests.find(questId);
    return (it != m_quests.end()) ? it->second.get() : nullptr;
}

std::vector<const QuestData*> QuestManager::GetQuestsByNPC(const std::string& npcName) const {
    std::vector<const QuestData*> result;

    std::shared_lock<std::shared_mutex> lock(m_questsMutex);

    auto it = m_npcQuests.find(npcName);
    if (it != m_npcQuests.end()) {
        for (WORD questId : it->second) {
            auto questIt = m_quests.find(questId);
            if (questIt != m_quests.end()) {
                result.push_back(questIt->second.get());
            }
        }
    }

    return result;
}

bool QuestManager::CanAcceptQuest(PlayObject* player, WORD questId) const {
    if (!player || !m_initialized) {
        return false;
    }

    const QuestData* quest = GetQuest(questId);
    if (!quest) {
        return false;
    }

    // 检查是否已经有这个任务
    if (HasQuest(player, questId)) {
        return false;
    }

    // 检查任务条件
    if (!CheckQuestConditions(player, *quest)) {
        return false;
    }

    // 检查前置任务
    if (!CheckQuestPrerequisites(player, *quest)) {
        return false;
    }

    return true;
}

bool QuestManager::AcceptQuest(PlayObject* player, WORD questId) {
    if (!player || !m_initialized) {
        return false;
    }

    if (!CanAcceptQuest(player, questId)) {
        return false;
    }

    const QuestData* quest = GetQuest(questId);
    if (!quest) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_playerQuestsMutex);

    // 创建玩家任务状态
    PlayerQuestStatus questStatus;
    questStatus.questId = questId;
    questStatus.state = QuestState::ACCEPTED;
    questStatus.objectives = quest->objectives; // 复制目标
    questStatus.acceptTime = GetCurrentTime();

    // 添加到玩家任务列表
    std::string playerName = player->GetCharName();
    m_playerQuests[playerName].push_back(questStatus);

    // 执行开始脚本
    if (!quest->startScript.empty()) {
        ExecuteQuestScript(player, quest->startScript, questId);
    }

    // 通知客户端
    player->SendDefMessage(Protocol::SM_QUESTACCEPT, questId, 0, 0, 0);
    player->SendMessage("接受任务：" + quest->name);

    Logger::Info("Quest accepted: " + player->GetCharName() + " -> " + quest->name);
    return true;
}

bool QuestManager::CompleteQuest(PlayObject* player, WORD questId) {
    if (!player || !m_initialized) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_playerQuestsMutex);

    std::string playerName = player->GetCharName();
    auto it = m_playerQuests.find(playerName);
    if (it == m_playerQuests.end()) {
        return false;
    }

    // 查找任务
    auto questIt = std::find_if(it->second.begin(), it->second.end(),
        [questId](const PlayerQuestStatus& status) {
            return status.questId == questId;
        });

    if (questIt == it->second.end() || questIt->state != QuestState::COMPLETED) {
        return false;
    }

    const QuestData* quest = GetQuest(questId);
    if (!quest) {
        return false;
    }

    // 给予奖励
    GiveQuestReward(player, quest->reward);

    // 更新任务状态
    questIt->state = QuestState::FINISHED;
    questIt->completeTime = GetCurrentTime();
    questIt->completedCount++;

    // 执行完成脚本
    if (!quest->completeScript.empty()) {
        ExecuteQuestScript(player, quest->completeScript, questId);
    }

    // 如果不是可重复任务，从列表中移除
    if (!quest->repeatable) {
        it->second.erase(questIt);
    }

    // 通知客户端
    NotifyQuestComplete(player, questId);
    player->SendMessage("完成任务：" + quest->name);

    Logger::Info("Quest completed: " + player->GetCharName() + " -> " + quest->name);
    return true;
}

bool QuestManager::AbandonQuest(PlayObject* player, WORD questId) {
    if (!player || !m_initialized) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_playerQuestsMutex);

    std::string playerName = player->GetCharName();
    auto it = m_playerQuests.find(playerName);
    if (it == m_playerQuests.end()) {
        return false;
    }

    // 查找并移除任务
    auto questIt = std::find_if(it->second.begin(), it->second.end(),
        [questId](const PlayerQuestStatus& status) {
            return status.questId == questId;
        });

    if (questIt == it->second.end()) {
        return false;
    }

    const QuestData* quest = GetQuest(questId);
    std::string questName = quest ? quest->name : "未知任务";

    it->second.erase(questIt);

    // 通知客户端
    player->SendDefMessage(Protocol::SM_QUESTABANDON, questId, 0, 0, 0);
    player->SendMessage("放弃任务：" + questName);

    Logger::Info("Quest abandoned: " + player->GetCharName() + " -> " + questName);
    return true;
}

void QuestManager::UpdateQuestProgress(PlayObject* player, QuestType type, const std::string& target, int count) {
    if (!player || !m_initialized) {
        return;
    }

    std::unique_lock<std::shared_mutex> lock(m_playerQuestsMutex);

    std::string playerName = player->GetCharName();
    auto it = m_playerQuests.find(playerName);
    if (it == m_playerQuests.end()) {
        return;
    }

    bool hasUpdate = false;

    for (auto& questStatus : it->second) {
        if (questStatus.state != QuestState::ACCEPTED) {
            continue;
        }

        // 更新任务目标
        for (auto& objective : questStatus.objectives) {
            if (objective.type == type && objective.target == target && !objective.completed) {
                objective.currentCount += count;
                if (objective.currentCount >= objective.requiredCount) {
                    objective.currentCount = objective.requiredCount;
                    objective.completed = true;
                }
                hasUpdate = true;
            }
        }

        // 检查任务是否完成
        if (AreAllObjectivesCompleted(questStatus)) {
            questStatus.state = QuestState::COMPLETED;
            NotifyQuestComplete(player, questStatus.questId);
        } else if (hasUpdate) {
            NotifyQuestUpdate(player, questStatus);
        }
    }
}

void QuestManager::OnMonsterKilled(PlayObject* player, const std::string& monsterName) {
    UpdateQuestProgress(player, QuestType::KILL_MONSTER, monsterName, 1);
}

void QuestManager::OnItemCollected(PlayObject* player, const UserItem& item) {
    // TODO: 根据物品索引获取物品名称
    std::string itemName = "Item_" + std::to_string(item.itemIndex);
    UpdateQuestProgress(player, QuestType::COLLECT_ITEM, itemName, 1);
}

void QuestManager::OnNPCTalk(PlayObject* player, const std::string& npcName) {
    UpdateQuestProgress(player, QuestType::TALK_TO_NPC, npcName, 1);
}

void QuestManager::OnLevelUp(PlayObject* player, WORD newLevel) {
    UpdateQuestProgress(player, QuestType::REACH_LEVEL, std::to_string(newLevel), 1);
}

void QuestManager::OnMapEnter(PlayObject* player, const std::string& mapName) {
    UpdateQuestProgress(player, QuestType::EXPLORE_MAP, mapName, 1);
}

std::vector<PlayerQuestStatus> QuestManager::GetPlayerQuests(PlayObject* player) const {
    if (!player || !m_initialized) {
        return {};
    }

    std::shared_lock<std::shared_mutex> lock(m_playerQuestsMutex);

    std::string playerName = player->GetCharName();
    auto it = m_playerQuests.find(playerName);
    if (it != m_playerQuests.end()) {
        return it->second;
    }

    return {};
}

PlayerQuestStatus* QuestManager::GetPlayerQuest(PlayObject* player, WORD questId) const {
    if (!player || !m_initialized) {
        return nullptr;
    }

    std::shared_lock<std::shared_mutex> lock(m_playerQuestsMutex);

    std::string playerName = player->GetCharName();
    auto it = m_playerQuests.find(playerName);
    if (it == m_playerQuests.end()) {
        return nullptr;
    }

    auto questIt = std::find_if(it->second.begin(), it->second.end(),
        [questId](const PlayerQuestStatus& status) {
            return status.questId == questId;
        });

    return (questIt != it->second.end()) ? const_cast<PlayerQuestStatus*>(&(*questIt)) : nullptr;
}

bool QuestManager::HasQuest(PlayObject* player, WORD questId) const {
    return GetPlayerQuest(player, questId) != nullptr;
}

bool QuestManager::IsQuestCompleted(PlayObject* player, WORD questId) const {
    PlayerQuestStatus* quest = GetPlayerQuest(player, questId);
    return quest && (quest->state == QuestState::COMPLETED || quest->state == QuestState::FINISHED);
}

std::vector<WORD> QuestManager::GetAvailableQuests(PlayObject* player, const std::string& npcName) const {
    std::vector<WORD> result;

    if (!player || !m_initialized) {
        return result;
    }

    auto npcQuests = GetQuestsByNPC(npcName);
    for (const QuestData* quest : npcQuests) {
        if (quest && CanAcceptQuest(player, quest->questId)) {
            result.push_back(quest->questId);
        }
    }

    return result;
}

std::vector<WORD> QuestManager::GetCompletableQuests(PlayObject* player, const std::string& npcName) const {
    std::vector<WORD> result;

    if (!player || !m_initialized) {
        return result;
    }

    auto playerQuests = GetPlayerQuests(player);
    for (const auto& questStatus : playerQuests) {
        if (questStatus.state == QuestState::COMPLETED) {
            const QuestData* quest = GetQuest(questStatus.questId);
            if (quest && quest->finishNPC == npcName) {
                result.push_back(questStatus.questId);
            }
        }
    }

    return result;
}

void QuestManager::Run() {
    if (!m_initialized) {
        return;
    }

    DWORD currentTime = GetCurrentTime();

    // 处理任务逻辑
    if (currentTime - m_lastProcessTime >= 5000) { // 每5秒处理一次
        ProcessQuests();
        m_lastProcessTime = currentTime;
    }

    // 检查超时
    if (currentTime - m_lastTimeoutCheckTime >= 60000) { // 每分钟检查一次超时
        CheckTimeouts();
        m_lastTimeoutCheckTime = currentTime;
    }
}

void QuestManager::ProcessQuests() {
    // 更新统计信息
    UpdateStatistics();
}

void QuestManager::CheckTimeouts() {
    DWORD currentTime = GetCurrentTime();

    std::unique_lock<std::shared_mutex> lock(m_playerQuestsMutex);

    for (auto& playerPair : m_playerQuests) {
        auto& quests = playerPair.second;

        for (auto it = quests.begin(); it != quests.end();) {
            const QuestData* quest = GetQuest(it->questId);

            if (quest && quest->timeLimit > 0 &&
                it->state == QuestState::ACCEPTED &&
                currentTime - it->acceptTime > quest->timeLimit) {

                // 任务超时，设为失败
                it->state = QuestState::FAILED;
                Logger::Info("Quest timeout: " + playerPair.first + " -> " + quest->name);

                // 如果不是可重复任务，移除
                if (!quest->repeatable) {
                    it = quests.erase(it);
                    continue;
                }
            }
            ++it;
        }
    }
}

bool QuestManager::SavePlayerQuests(PlayObject* player) {
    if (!player || !m_initialized) {
        return false;
    }

    std::shared_lock<std::shared_mutex> lock(m_playerQuestsMutex);

    std::string playerName = player->GetCharName();
    auto it = m_playerQuests.find(playerName);
    if (it != m_playerQuests.end()) {
        return SavePlayerQuestsToDB(playerName, it->second);
    }

    return true;
}

bool QuestManager::LoadPlayerQuests(PlayObject* player) {
    if (!player || !m_initialized) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_playerQuestsMutex);

    std::string playerName = player->GetCharName();
    std::vector<PlayerQuestStatus> quests;

    if (LoadPlayerQuestsFromDB(playerName, quests)) {
        m_playerQuests[playerName] = std::move(quests);
        return true;
    }

    return false;
}

void QuestManager::UpdateStatistics() {
    std::shared_lock<std::shared_mutex> questLock(m_questsMutex);
    std::shared_lock<std::shared_mutex> playerLock(m_playerQuestsMutex);

    m_statistics.totalQuests = static_cast<int>(m_quests.size());
    m_statistics.activeQuests = 0;
    m_statistics.completedQuests = 0;
    m_statistics.failedQuests = 0;

    for (const auto& playerPair : m_playerQuests) {
        for (const auto& quest : playerPair.second) {
            switch (quest.state) {
                case QuestState::ACCEPTED:
                    m_statistics.activeQuests++;
                    break;
                case QuestState::COMPLETED:
                case QuestState::FINISHED:
                    m_statistics.completedQuests++;
                    break;
                case QuestState::FAILED:
                    m_statistics.failedQuests++;
                    break;
                default:
                    break;
            }
        }
    }

    m_statistics.lastUpdateTime = GetCurrentTime();
}

// 私有方法实现
bool QuestManager::ValidateQuestData(const QuestData& quest) const {
    if (quest.questId == 0 || quest.name.empty()) {
        return false;
    }

    if (quest.minLevel > quest.maxLevel) {
        return false;
    }

    if (quest.objectives.empty()) {
        return false;
    }

    return true;
}

bool QuestManager::CheckQuestPrerequisites(PlayObject* player, const QuestData& quest) const {
    for (WORD prereqId : quest.prerequisiteQuests) {
        if (!IsQuestCompleted(player, prereqId)) {
            return false;
        }
    }
    return true;
}

bool QuestManager::CheckQuestConditions(PlayObject* player, const QuestData& quest) const {
    // 检查等级要求
    WORD playerLevel = player->GetLevel();
    if (playerLevel < quest.minLevel || playerLevel > quest.maxLevel) {
        return false;
    }

    // 检查职业要求
    if (quest.requiredJob != JobType::NONE && player->GetJob() != quest.requiredJob) {
        return false;
    }

    return true;
}

void QuestManager::GiveQuestReward(PlayObject* player, const QuestReward& reward) {
    if (!player) return;

    // 给予经验
    if (reward.exp > 0) {
        player->GainExp(reward.exp);
    }

    // 给予金币
    if (reward.gold > 0) {
        player->IncGold(reward.gold);
    }

    // 给予物品
    for (const auto& item : reward.items) {
        if (!player->IsBagFull()) {
            player->AddBagItem(item);
        } else {
            // 背包满了，掉落到地上
            // TODO: 实现物品掉落
        }
    }
}

void QuestManager::UpdateQuestObjective(PlayerQuestStatus& questStatus, QuestType type, const std::string& target, int count) {
    for (auto& objective : questStatus.objectives) {
        if (objective.type == type && objective.target == target && !objective.completed) {
            objective.currentCount += count;
            if (objective.currentCount >= objective.requiredCount) {
                objective.currentCount = objective.requiredCount;
                objective.completed = true;
            }
        }
    }
}

bool QuestManager::IsQuestObjectiveCompleted(const QuestObjective& objective) const {
    return objective.completed || objective.currentCount >= objective.requiredCount;
}

bool QuestManager::AreAllObjectivesCompleted(const PlayerQuestStatus& questStatus) const {
    for (const auto& objective : questStatus.objectives) {
        if (!IsQuestObjectiveCompleted(objective)) {
            return false;
        }
    }
    return true;
}

void QuestManager::NotifyQuestUpdate(PlayObject* player, const PlayerQuestStatus& questStatus) {
    if (!player) return;

    // 发送任务更新通知
    player->SendDefMessage(Protocol::SM_QUESTUPDATE, questStatus.questId, 0, 0, 0);
}

void QuestManager::NotifyQuestComplete(PlayObject* player, WORD questId) {
    if (!player) return;

    // 发送任务完成通知
    player->SendDefMessage(Protocol::SM_QUESTCOMPLETE, questId, 0, 0, 0);
}

bool QuestManager::LoadPlayerQuestsFromDB(const std::string& playerName, std::vector<PlayerQuestStatus>& quests) {
    // TODO: 实现从数据库加载玩家任务数据
    Logger::Debug("Loading player quests from DB: " + playerName);
    return true;
}

bool QuestManager::SavePlayerQuestsToDB(const std::string& playerName, const std::vector<PlayerQuestStatus>& quests) {
    // TODO: 实现保存玩家任务数据到数据库
    Logger::Debug("Saving player quests to DB: " + playerName);
    return true;
}

bool QuestManager::ExecuteQuestScript(PlayObject* player, const std::string& script, WORD questId) {
    // TODO: 实现任务脚本执行
    Logger::Debug("Executing quest script for player: " + player->GetCharName() +
                  ", quest: " + std::to_string(questId) + ", script: " + script);
    return true;
}

} // namespace MirServer

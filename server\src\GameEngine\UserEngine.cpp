// UserEngine.cpp - 用户引擎实现
#include "UserEngine.h"
#include "MapManager.h"
#include "ItemManager.h"
#include "../Common/Logger.h"
#include <algorithm>
#include <sstream>
#include <chrono>

namespace MirServer {

// 全局实例
std::unique_ptr<UserEngine> g_UserEngine;

UserEngine::UserEngine() {
}

UserEngine::~UserEngine() {
    Finalize();
}

bool UserEngine::Initialize(std::shared_ptr<MapManager> mapManager,
                          std::shared_ptr<ItemManager> itemManager) {
    if (m_initialized) {
        return true;
    }
    
    m_mapManager = mapManager;
    m_itemManager = itemManager;
    
    if (!m_mapManager || !m_itemManager) {
        Logger::Error("UserEngine: Invalid map or item manager");
        return false;
    }
    
    // 清空数据
    m_players.clear();
    m_connectionMap.clear();
    
    // 重置统计
    m_statistics = Statistics();
    
    m_initialized = true;
    Logger::Info("UserEngine initialized successfully");
    return true;
}

void UserEngine::Finalize() {
    if (!m_initialized) {
        return;
    }
    
    // 保存所有玩家
    SaveAllPlayers();
    
    // 踢出所有玩家
    KickAllPlayers("Server shutdown");
    
    // 清理数据
    {
        std::unique_lock<std::shared_mutex> lock(m_playersMutex);
        m_players.clear();
        m_connectionMap.clear();
    }
    
    m_initialized = false;
    Logger::Info("UserEngine finalized");
}

std::shared_ptr<PlayObject> UserEngine::CreatePlayer(const HumDataInfo& humData) {
    auto player = std::make_shared<PlayObject>();
    
    // 设置基本信息
    player->SetCharName(humData.charName);
    player->SetJob(humData.job);
    player->SetGender(humData.gender);
    player->SetLevel(humData.level);
    player->SetDirection(humData.direction);
    player->SetCurrentPos(humData.currentPos);
    player->SetMapName(humData.mapName);
    player->SetGold(humData.gold);
    
    // 初始化其他属性
    player->Initialize();
    
    return player;
}

bool UserEngine::AddPlayer(std::shared_ptr<PlayObject> player) {
    if (!player) {
        return false;
    }
    
    std::unique_lock<std::shared_mutex> lock(m_playersMutex);
    
    // 检查是否已存在
    if (m_players.find(player->GetCharName()) != m_players.end()) {
        Logger::Error("Player already exists: " + player->GetCharName());
        return false;
    }
    
    // 添加到列表
    m_players[player->GetCharName()] = player;
    
    // 更新统计
    {
        std::lock_guard<std::mutex> statsLock(m_statsMutex);
        m_statistics.totalPlayers++;
        m_statistics.activePlayers++;
        if (m_statistics.activePlayers > m_statistics.maxConcurrentPlayers) {
            m_statistics.maxConcurrentPlayers = m_statistics.activePlayers;
        }
    }
    
    Logger::Info("Player added: " + player->GetCharName());
    return true;
}

bool UserEngine::RemovePlayer(const std::string& charName) {
    std::unique_lock<std::shared_mutex> lock(m_playersMutex);
    
    auto it = m_players.find(charName);
    if (it == m_players.end()) {
        return false;
    }
    
    m_players.erase(it);
    
    // 更新统计
    {
        std::lock_guard<std::mutex> statsLock(m_statsMutex);
        if (m_statistics.activePlayers > 0) {
            m_statistics.activePlayers--;
        }
    }
    
    Logger::Info("Player removed: " + charName);
    return true;
}

std::shared_ptr<PlayObject> UserEngine::GetPlayer(const std::string& charName) const {
    std::shared_lock<std::shared_mutex> lock(m_playersMutex);
    
    auto it = m_players.find(charName);
    if (it != m_players.end()) {
        return it->second;
    }
    
    return nullptr;
}

std::shared_ptr<PlayObject> UserEngine::GetPlayerByConnection(uint32_t connectionId) const {
    std::shared_lock<std::shared_mutex> lock(m_playersMutex);
    
    auto it = m_connectionMap.find(connectionId);
    if (it != m_connectionMap.end()) {
        return it->second;
    }
    
    return nullptr;
}

std::vector<std::shared_ptr<PlayObject>> UserEngine::GetAllPlayers() const {
    std::shared_lock<std::shared_mutex> lock(m_playersMutex);
    
    std::vector<std::shared_ptr<PlayObject>> result;
    result.reserve(m_players.size());
    
    for (const auto& pair : m_players) {
        result.push_back(pair.second);
    }
    
    return result;
}

size_t UserEngine::GetPlayerCount() const {
    std::shared_lock<std::shared_mutex> lock(m_playersMutex);
    return m_players.size();
}

bool UserEngine::BindPlayerToConnection(std::shared_ptr<PlayObject> player, uint32_t connectionId) {
    if (!player) {
        return false;
    }
    
    std::unique_lock<std::shared_mutex> lock(m_playersMutex);
    
    // 移除旧的连接绑定
    for (auto it = m_connectionMap.begin(); it != m_connectionMap.end(); ) {
        if (it->second == player) {
            it = m_connectionMap.erase(it);
        } else {
            ++it;
        }
    }
    
    // 绑定新连接
    m_connectionMap[connectionId] = player;
    player->SetConnectionId(connectionId);
    
    return true;
}

void UserEngine::UnbindPlayerConnection(uint32_t connectionId) {
    std::unique_lock<std::shared_mutex> lock(m_playersMutex);
    
    auto it = m_connectionMap.find(connectionId);
    if (it != m_connectionMap.end()) {
        if (it->second) {
            it->second->SetConnectionId(0);
        }
        m_connectionMap.erase(it);
    }
}

bool UserEngine::PlayerLogin(std::shared_ptr<PlayObject> player) {
    if (!player) {
        return false;
    }
    
    // 添加玩家
    if (!AddPlayer(player)) {
        return false;
    }
    
    // 更新统计
    {
        std::lock_guard<std::mutex> lock(m_statsMutex);
        m_statistics.totalLoginCount++;
    }
    
    // 触发登录事件
    if (m_onPlayerLogin) {
        m_onPlayerLogin(player);
    }
    
    Logger::Info("Player login: " + player->GetCharName());
    return true;
}

bool UserEngine::PlayerLogout(const std::string& charName) {
    auto player = GetPlayer(charName);
    if (!player) {
        return false;
    }
    
    // 保存玩家数据
    player->SaveData();
    
    // 更新统计
    {
        std::lock_guard<std::mutex> lock(m_statsMutex);
        m_statistics.totalLogoutCount++;
    }
    
    // 触发登出事件
    if (m_onPlayerLogout) {
        m_onPlayerLogout(player);
    }
    
    // 解绑连接
    if (player->GetConnectionId() > 0) {
        UnbindPlayerConnection(player->GetConnectionId());
    }
    
    // 移除玩家
    RemovePlayer(charName);
    
    Logger::Info("Player logout: " + charName);
    return true;
}

void UserEngine::PlayerDisconnect(uint32_t connectionId) {
    auto player = GetPlayerByConnection(connectionId);
    if (player) {
        PlayerLogout(player->GetCharName());
    } else {
        UnbindPlayerConnection(connectionId);
    }
}

void UserEngine::BroadcastMessage(const std::string& message, BYTE color) {
    auto players = GetAllPlayers();
    for (const auto& player : players) {
        player->SendMessage(message, color);
    }
}

void UserEngine::BroadcastSystemMessage(const std::string& message) {
    BroadcastMessage("[System] " + message, 0xFF);
}

void UserEngine::BroadcastPacket(const std::vector<uint8_t>& packet) {
    auto players = GetAllPlayers();
    for (const auto& player : players) {
        player->SendPacket(packet);
    }
}

void UserEngine::BroadcastPacketInRange(const Point& center, int range, const std::string& mapName, 
                                       const std::vector<uint8_t>& packet) {
    auto players = GetAllPlayers();
    for (const auto& player : players) {
        if (player->GetMapName() == mapName) {
            if (GetDistance(player->GetCurrentPos(), center) <= range) {
                player->SendPacket(packet);
            }
        }
    }
}

void UserEngine::Run() {
    DWORD currentTime = GetCurrentTime();
    
    // 处理玩家
    ProcessPlayers();
    
    // 检查超时
    if (currentTime - m_lastCheckTime >= m_timeoutCheckInterval) {
        CheckPlayerTimeOut();
        m_lastCheckTime = currentTime;
    }
    
    // 自动保存
    if (currentTime - m_lastSaveTime >= m_autoSaveInterval) {
        SaveAllPlayers();
        m_lastSaveTime = currentTime;
    }
    
    // 处理定时任务
    {
        std::lock_guard<std::mutex> lock(m_timerMutex);
        for (auto& task : m_timerTasks) {
            if (currentTime - task.lastRunTime >= task.interval) {
                task.task();
                task.lastRunTime = currentTime;
            }
        }
    }
}

void UserEngine::ProcessPlayers() {
    auto players = GetAllPlayers();
    
    for (const auto& player : players) {
        if (!player || !player->IsAlive()) {
            continue;
        }
        
        // 处理玩家数据包
        ProcessPlayerPackets(player);
        
        // 处理移动
        HandlePlayerMovement(player);
        
        // 处理战斗
        HandlePlayerCombat(player);
        
        // 处理物品
        HandlePlayerItems(player);
        
        // 处理交易
        HandlePlayerTrade(player);
        
        // 处理组队
        HandlePlayerGroup(player);
        
        // 更新环境
        UpdatePlayerEnvironment(player);
        
        // 运行玩家逻辑
        player->Run();
    }
}

void UserEngine::CheckPlayerTimeOut() {
    auto players = GetAllPlayers();
    DWORD currentTime = GetCurrentTime();
    
    for (const auto& player : players) {
        if (player && player->GetConnectionId() > 0) {
            // 检查超时
            if (currentTime - player->GetLastActiveTime() > m_playerTimeout) {
                Logger::Warning("Player timeout: " + player->GetCharName());
                PlayerDisconnect(player->GetConnectionId());
            }
        }
    }
}

void UserEngine::SaveAllPlayers() {
    auto players = GetAllPlayers();
    
    for (const auto& player : players) {
        if (player) {
            player->SaveData();
        }
    }
    
    Logger::Info("All players saved");
}

UserEngine::Statistics UserEngine::GetStatistics() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    return m_statistics;
}

bool UserEngine::ProcessGMCommand(std::shared_ptr<PlayObject> player, const std::string& command) {
    if (!player || !player->IsGM()) {
        return false;
    }
    
    // 解析命令
    std::istringstream iss(command);
    std::string cmd;
    std::vector<std::string> args;
    
    iss >> cmd;
    std::string arg;
    while (iss >> arg) {
        args.push_back(arg);
    }
    
    // 转换为小写
    std::transform(cmd.begin(), cmd.end(), cmd.begin(), ::tolower);
    
    // 处理命令
    if (cmd == "@level") {
        GMCommand_Level(player, args);
    } else if (cmd == "@gold") {
        GMCommand_Gold(player, args);
    } else if (cmd == "@item") {
        GMCommand_Item(player, args);
    } else if (cmd == "@move") {
        GMCommand_Move(player, args);
    } else if (cmd == "@kick") {
        GMCommand_Kick(player, args);
    } else if (cmd == "@shutdown") {
        GMCommand_Shutdown(player, args);
    } else {
        player->SendMessage("Unknown command: " + cmd);
        return false;
    }
    
    return true;
}

void UserEngine::KickPlayer(const std::string& charName, const std::string& reason) {
    auto player = GetPlayer(charName);
    if (player) {
        player->SendMessage("You have been kicked: " + reason);
        PlayerLogout(charName);
    }
}

void UserEngine::KickAllPlayers(const std::string& reason) {
    auto players = GetAllPlayers();
    
    for (const auto& player : players) {
        if (player) {
            player->SendMessage("Server is shutting down: " + reason);
        }
    }
    
    // 清空所有玩家
    {
        std::unique_lock<std::shared_mutex> lock(m_playersMutex);
        m_players.clear();
        m_connectionMap.clear();
    }
}

bool UserEngine::IsPlayerOnline(const std::string& charName) const {
    return GetPlayer(charName) != nullptr;
}

// 内部方法实现
void UserEngine::ProcessPlayerPackets(std::shared_ptr<PlayObject> player) {
    // 这里处理玩家的网络数据包
    // 具体实现依赖于网络模块
}

void UserEngine::HandlePlayerMovement(std::shared_ptr<PlayObject> player) {
    // 处理玩家移动逻辑
}

void UserEngine::HandlePlayerCombat(std::shared_ptr<PlayObject> player) {
    // 处理玩家战斗逻辑
}

void UserEngine::HandlePlayerItems(std::shared_ptr<PlayObject> player) {
    // 处理玩家物品逻辑
}

void UserEngine::HandlePlayerTrade(std::shared_ptr<PlayObject> player) {
    // 处理玩家交易逻辑
}

void UserEngine::HandlePlayerGroup(std::shared_ptr<PlayObject> player) {
    // 处理玩家组队逻辑
}

void UserEngine::UpdatePlayerEnvironment(std::shared_ptr<PlayObject> player) {
    // 更新玩家周围环境
}

// GM命令实现
void UserEngine::GMCommand_Level(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args) {
    if (args.empty()) {
        player->SendMessage("Usage: @level <level>");
        return;
    }
    
    int level = std::stoi(args[0]);
    if (level < 1 || level > 65535) {
        player->SendMessage("Invalid level");
        return;
    }
    
    player->SetLevel(level);
    player->SendMessage("Level set to " + std::to_string(level));
}

void UserEngine::GMCommand_Gold(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args) {
    if (args.empty()) {
        player->SendMessage("Usage: @gold <amount>");
        return;
    }
    
    DWORD gold = std::stoul(args[0]);
    player->SetGold(gold);
    player->SendMessage("Gold set to " + std::to_string(gold));
}

void UserEngine::GMCommand_Item(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args) {
    if (args.empty()) {
        player->SendMessage("Usage: @item <itemname> [count]");
        return;
    }
    
    // 这里需要ItemManager支持
    player->SendMessage("Item command not implemented yet");
}

void UserEngine::GMCommand_Move(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args) {
    if (args.size() < 3) {
        player->SendMessage("Usage: @move <mapname> <x> <y>");
        return;
    }
    
    std::string mapName = args[0];
    int x = std::stoi(args[1]);
    int y = std::stoi(args[2]);
    
    // 这里需要MapManager支持
    player->SendMessage("Move command not implemented yet");
}

void UserEngine::GMCommand_Kick(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args) {
    if (args.empty()) {
        player->SendMessage("Usage: @kick <charname>");
        return;
    }
    
    std::string targetName = args[0];
    KickPlayer(targetName, "Kicked by GM");
    player->SendMessage("Player " + targetName + " kicked");
}

void UserEngine::GMCommand_Shutdown(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args) {
    int delay = args.empty() ? 0 : std::stoi(args[0]);
    
    if (delay > 0) {
        BroadcastSystemMessage("Server will shutdown in " + std::to_string(delay) + " seconds");
        // 这里需要实现延迟关闭
    } else {
        BroadcastSystemMessage("Server is shutting down now!");
        KickAllPlayers("Server shutdown");
    }
}

// 其他方法实现
std::shared_ptr<PlayObject> UserEngine::FindPlayer(std::function<bool(const std::shared_ptr<PlayObject>&)> predicate) const {
    auto players = GetAllPlayers();
    
    auto it = std::find_if(players.begin(), players.end(), predicate);
    if (it != players.end()) {
        return *it;
    }
    
    return nullptr;
}

std::vector<std::shared_ptr<PlayObject>> UserEngine::FindPlayers(std::function<bool(const std::shared_ptr<PlayObject>&)> predicate) const {
    auto players = GetAllPlayers();
    std::vector<std::shared_ptr<PlayObject>> result;
    
    std::copy_if(players.begin(), players.end(), std::back_inserter(result), predicate);
    
    return result;
}

std::vector<std::shared_ptr<PlayObject>> UserEngine::GetTopLevelPlayers(size_t count) const {
    auto players = GetAllPlayers();
    
    // 按等级排序
    std::sort(players.begin(), players.end(), 
        [](const std::shared_ptr<PlayObject>& a, const std::shared_ptr<PlayObject>& b) {
            return a->GetHumDataInfo().level > b->GetHumDataInfo().level;
        });
    
    // 返回前N个
    if (players.size() > count) {
        players.resize(count);
    }
    
    return players;
}

std::vector<std::shared_ptr<PlayObject>> UserEngine::GetTopPKPlayers(size_t count) const {
    auto players = GetAllPlayers();
    
    // 按PK点数排序
    std::sort(players.begin(), players.end(), 
        [](const std::shared_ptr<PlayObject>& a, const std::shared_ptr<PlayObject>& b) {
            return a->GetHumDataInfo().pkPoint > b->GetHumDataInfo().pkPoint;
        });
    
    // 返回前N个
    if (players.size() > count) {
        players.resize(count);
    }
    
    return players;
}

void UserEngine::RegisterTimerTask(const std::string& name, std::function<void()> task, DWORD interval) {
    std::lock_guard<std::mutex> lock(m_timerMutex);
    
    // 移除同名任务
    m_timerTasks.erase(
        std::remove_if(m_timerTasks.begin(), m_timerTasks.end(),
            [&name](const TimerTask& t) { return t.name == name; }),
        m_timerTasks.end()
    );
    
    // 添加新任务
    TimerTask newTask;
    newTask.name = name;
    newTask.task = task;
    newTask.interval = interval;
    newTask.lastRunTime = GetCurrentTime();
    
    m_timerTasks.push_back(newTask);
}

void UserEngine::UnregisterTimerTask(const std::string& name) {
    std::lock_guard<std::mutex> lock(m_timerMutex);
    
    m_timerTasks.erase(
        std::remove_if(m_timerTasks.begin(), m_timerTasks.end(),
            [&name](const TimerTask& t) { return t.name == name; }),
        m_timerTasks.end()
    );
}

} // namespace MirServer 
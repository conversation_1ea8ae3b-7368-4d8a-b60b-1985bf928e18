#pragma once

#include "../Common/Types.h"
#include "../Common/Logger.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <functional>
#include <shared_mutex>

namespace MirServer {

// 前向声明
class PlayObject;
class NPC;
class Environment;

// 脚本变量管理
struct ScriptVar {
    std::string name;
    int value;
    bool persistent;  // 是否持久化
    DWORD lastModified;

    ScriptVar() : value(0), persistent(false), lastModified(0) {}
    ScriptVar(const std::string& n, int v, bool p = false)
        : name(n), value(v), persistent(p), lastModified(GetCurrentTime()) {}
};

// 列表管理
class ListManager {
public:
    // 名单管理
    bool AddToNameList(const std::string& listName, const std::string& playerName);
    bool RemoveFromNameList(const std::string& listName, const std::string& playerName);
    bool IsInNameList(const std::string& listName, const std::string& playerName) const;

    // IP列表管理
    bool AddToIPList(const std::string& listName, const std::string& ip);
    bool RemoveFromIPList(const std::string& listName, const std::string& ip);
    bool IsInIPList(const std::string& listName, const std::string& ip) const;

    // 账号列表管理
    bool AddToAccountList(const std::string& listName, const std::string& account);
    bool RemoveFromAccountList(const std::string& listName, const std::string& account);
    bool IsInAccountList(const std::string& listName, const std::string& account) const;

    // 列表保存和加载
    bool SaveLists();
    bool LoadLists();

private:
    std::unordered_map<std::string, std::unordered_set<std::string>> m_nameLists;
    std::unordered_map<std::string, std::unordered_set<std::string>> m_ipLists;
    std::unordered_map<std::string, std::unordered_set<std::string>> m_accountLists;
    mutable std::shared_mutex m_listsMutex;
};

// 脚本条件结构
struct ScriptCondition {
    std::string type;           // 条件类型：CHECKLEVEL, CHECKITEM等
    std::string param1;         // 第一个参数
    std::string param2;         // 第二个参数
    std::string param3;         // 第三个参数
    std::string param4;         // 第四个参数
    std::string param5;         // 第五个参数
    std::string param6;         // 第六个参数
    int nParam1 = 0;           // 数值参数1
    int nParam2 = 0;           // 数值参数2
    int nParam3 = 0;           // 数值参数3
    int nParam4 = 0;           // 数值参数4
    int nParam5 = 0;           // 数值参数5
    int nParam6 = 0;           // 数值参数6
    char method = '=';          // 比较方法：=, >, <

    ScriptCondition() = default;
    ScriptCondition(const std::string& t, const std::string& p1 = "", const std::string& p2 = "",
                   const std::string& p3 = "", int n1 = 0, int n2 = 0, int n3 = 0)
        : type(t), param1(p1), param2(p2), param3(p3), nParam1(n1), nParam2(n2), nParam3(n3) {}
};

// 脚本动作结构
struct ScriptAction {
    std::string type;           // 动作类型：GIVE, TAKE, MAP等
    std::string param1;         // 第一个参数
    std::string param2;         // 第二个参数
    std::string param3;         // 第三个参数
    std::string param4;         // 第四个参数
    std::string param5;         // 第五个参数
    std::string param6;         // 第六个参数
    int nParam1 = 0;           // 数值参数1
    int nParam2 = 0;           // 数值参数2
    int nParam3 = 0;           // 数值参数3
    int nParam4 = 0;           // 数值参数4
    int nParam5 = 0;           // 数值参数5
    int nParam6 = 0;           // 数值参数6

    ScriptAction() = default;
    ScriptAction(const std::string& t, const std::string& p1 = "", const std::string& p2 = "",
                const std::string& p3 = "", int n1 = 0, int n2 = 0, int n3 = 0)
        : type(t), param1(p1), param2(p2), param3(p3), nParam1(n1), nParam2(n2), nParam3(n3) {}
};

// 脚本选项结构
struct ScriptOption {
    std::string text;           // 选项显示文本
    std::string gotoLabel;      // 跳转标签

    ScriptOption() = default;
    ScriptOption(const std::string& t, const std::string& label) : text(t), gotoLabel(label) {}
};

// 脚本块结构（对应原版的TSayingRecord和TSayingProcedure）
struct ScriptBlock {
    std::string label;                          // 标签名：@main, @shop等
    std::string text;                           // 对话文本
    std::vector<ScriptCondition> conditions;   // #IF条件列表
    std::vector<ScriptAction> actions;          // #ACT动作列表
    std::vector<ScriptAction> elseActions;      // #ELSEACT动作列表
    std::vector<ScriptOption> options;          // 选项列表
    bool hasExtJmp = false;                     // 是否有外部跳转

    ScriptBlock() = default;
    ScriptBlock(const std::string& l) : label(l) {}

    // 清空所有内容
    void Clear() {
        text.clear();
        conditions.clear();
        actions.clear();
        elseActions.clear();
        options.clear();
        hasExtJmp = false;
    }
};

// NPC脚本结构（对应原版的TNormNpc脚本系统）
struct NPCScript {
    std::string npcName;                                    // NPC名称
    std::string scriptFile;                                 // 脚本文件路径
    std::unordered_map<std::string, ScriptBlock> blocks;   // 脚本块映射
    DWORD lastModifyTime = 0;                              // 文件最后修改时间

    NPCScript() = default;
    NPCScript(const std::string& name, const std::string& file)
        : npcName(name), scriptFile(file) {}

    // 获取脚本块
    const ScriptBlock* GetBlock(const std::string& label) const {
        auto it = blocks.find(label);
        return (it != blocks.end()) ? &it->second : nullptr;
    }

    // 添加脚本块
    void AddBlock(const std::string& label, const ScriptBlock& block) {
        blocks[label] = block;
    }

    // 清空所有脚本块
    void Clear() {
        blocks.clear();
        lastModifyTime = 0;
    }
};

// 前向声明
class PlayObject;
class NPC;

// 脚本执行引擎类
class ScriptEngine {
public:
    ScriptEngine();
    ~ScriptEngine();

    // 脚本执行
    bool ExecuteScript(PlayObject* player, NPC* npc, const NPCScript& script, const std::string& label = "main");
    bool ExecuteScriptBlock(PlayObject* player, NPC* npc, const ScriptBlock& block);

    // 条件检查
    bool CheckCondition(PlayObject* player, NPC* npc, const ScriptCondition& condition);
    bool CheckAllConditions(PlayObject* player, NPC* npc, const std::vector<ScriptCondition>& conditions);

    // 动作执行
    bool ExecuteAction(PlayObject* player, NPC* npc, const ScriptAction& action);
    bool ExecuteAllActions(PlayObject* player, NPC* npc, const std::vector<ScriptAction>& actions);

    // 脚本跳转
    bool GotoLabel(PlayObject* player, NPC* npc, const NPCScript& script, const std::string& label);

    // 发送对话
    void SendDialog(PlayObject* player, NPC* npc, const ScriptBlock& block);

    // 变量管理
    bool SetScriptVar(const std::string& varName, int value, bool persistent = false);
    int GetScriptVar(const std::string& varName) const;
    bool SaveScriptVar(const std::string& varName);
    bool LoadScriptVar(const std::string& varName);
    void ClearScriptVars();

    // 列表管理
    ListManager& GetListManager() { return m_listManager; }

    // 错误处理
    std::string GetLastError() const { return m_lastError; }
    void SetLastError(const std::string& error);

private:
    // 条件检查实现
    bool CheckLevel(PlayObject* player, const ScriptCondition& condition);
    bool CheckJob(PlayObject* player, const ScriptCondition& condition);
    bool CheckGold(PlayObject* player, const ScriptCondition& condition);
    bool CheckItem(PlayObject* player, const ScriptCondition& condition);
    bool CheckBagSize(PlayObject* player, const ScriptCondition& condition);
    bool CheckDC(PlayObject* player, const ScriptCondition& condition);
    bool CheckMC(PlayObject* player, const ScriptCondition& condition);
    bool CheckSC(PlayObject* player, const ScriptCondition& condition);
    bool CheckHP(PlayObject* player, const ScriptCondition& condition);
    bool CheckMP(PlayObject* player, const ScriptCondition& condition);
    bool CheckExp(PlayObject* player, const ScriptCondition& condition);
    bool CheckPKPoint(PlayObject* player, const ScriptCondition& condition);
    bool CheckCreditPoint(PlayObject* player, const ScriptCondition& condition);
    bool CheckSkill(PlayObject* player, const ScriptCondition& condition);
    bool CheckGender(PlayObject* player, const ScriptCondition& condition);
    bool CheckMapName(PlayObject* player, const ScriptCondition& condition);
    bool CheckSafeZone(PlayObject* player, const ScriptCondition& condition);

    // 时间相关条件检查
    bool CheckTime(PlayObject* player, const ScriptCondition& condition);
    bool CheckDate(PlayObject* player, const ScriptCondition& condition);
    bool CheckDay(PlayObject* player, const ScriptCondition& condition);
    bool CheckHour(PlayObject* player, const ScriptCondition& condition);
    bool CheckMin(PlayObject* player, const ScriptCondition& condition);

    // 游戏状态条件检查
    bool CheckMarry(PlayObject* player, const ScriptCondition& condition);
    bool CheckMaster(PlayObject* player, const ScriptCondition& condition);
    bool CheckGuild(PlayObject* player, const ScriptCondition& condition);
    bool CheckGuildRank(PlayObject* player, const ScriptCondition& condition);
    bool CheckCastleOwner(PlayObject* player, const ScriptCondition& condition);

    // 高级条件检查
    bool CheckVar(PlayObject* player, const ScriptCondition& condition);
    bool CheckNameList(PlayObject* player, const ScriptCondition& condition);
    bool CheckIPList(PlayObject* player, const ScriptCondition& condition);
    bool CheckAccountList(PlayObject* player, const ScriptCondition& condition);
    bool CheckSlaveCount(PlayObject* player, const ScriptCondition& condition);
    bool CheckOnline(PlayObject* player, const ScriptCondition& condition);
    bool CheckDuraEva(PlayObject* player, const ScriptCondition& condition);
    bool CheckBagCount(PlayObject* player, const ScriptCondition& condition);
    bool CheckItemW(PlayObject* player, const ScriptCondition& condition);
    bool CheckItemType(PlayObject* player, const ScriptCondition& condition);
    bool CheckItemAddValue(PlayObject* player, const ScriptCondition& condition);
    bool CheckItemLevel(PlayObject* player, const ScriptCondition& condition);

    // 装备条件检查
    bool CheckWearing(PlayObject* player, const ScriptCondition& condition);
    bool CheckWeapon(PlayObject* player, const ScriptCondition& condition);
    bool CheckArmor(PlayObject* player, const ScriptCondition& condition);
    bool CheckNecklace(PlayObject* player, const ScriptCondition& condition);
    bool CheckHelmet(PlayObject* player, const ScriptCondition& condition);
    bool CheckRingL(PlayObject* player, const ScriptCondition& condition);
    bool CheckRingR(PlayObject* player, const ScriptCondition& condition);
    bool CheckArmRingL(PlayObject* player, const ScriptCondition& condition);
    bool CheckArmRingR(PlayObject* player, const ScriptCondition& condition);
    bool CheckBelt(PlayObject* player, const ScriptCondition& condition);
    bool CheckBoots(PlayObject* player, const ScriptCondition& condition);
    bool CheckCharm(PlayObject* player, const ScriptCondition& condition);

    // 特殊条件检查
    bool CheckGroupCount(PlayObject* player, const ScriptCondition& condition);
    bool CheckGroupLeader(PlayObject* player, const ScriptCondition& condition);
    bool CheckPoseDir(PlayObject* player, const ScriptCondition& condition);
    bool CheckPoseLevel(PlayObject* player, const ScriptCondition& condition);
    bool CheckContainsText(PlayObject* player, const ScriptCondition& condition);
    bool CheckStringList(PlayObject* player, const ScriptCondition& condition);
    bool CheckRangeCount(PlayObject* player, const ScriptCondition& condition);
    bool CheckMonCount(PlayObject* player, const ScriptCondition& condition);
    bool CheckHumCount(PlayObject* player, const ScriptCondition& condition);
    bool CheckIsAdmin(PlayObject* player, const ScriptCondition& condition);
    bool CheckAccountIPCount(PlayObject* player, const ScriptCondition& condition);
    bool CheckIPCount(PlayObject* player, const ScriptCondition& condition);

    // 补充的高级条件检查
    bool CheckCastleMaster(PlayObject* player, const ScriptCondition& condition);
    bool CheckGuildMaster(PlayObject* player, const ScriptCondition& condition);
    bool CheckBuildPoint(PlayObject* player, const ScriptCondition& condition);
    bool CheckPlayerCount(PlayObject* player, const ScriptCondition& condition);
    bool CheckGameGold(PlayObject* player, const ScriptCondition& condition);
    bool CheckGamePoint(PlayObject* player, const ScriptCondition& condition);

    // 动作执行实现
    bool ActionGive(PlayObject* player, const ScriptAction& action);
    bool ActionTake(PlayObject* player, const ScriptAction& action);
    bool ActionGiveExp(PlayObject* player, const ScriptAction& action);
    bool ActionTakeGold(PlayObject* player, const ScriptAction& action);
    bool ActionGiveGold(PlayObject* player, const ScriptAction& action);
    bool ActionGiveItem(PlayObject* player, const ScriptAction& action);
    bool ActionTakeItem(PlayObject* player, const ScriptAction& action);
    bool ActionGiveSkill(PlayObject* player, const ScriptAction& action);
    bool ActionTakeSkill(PlayObject* player, const ScriptAction& action);
    bool ActionChangeLevel(PlayObject* player, const ScriptAction& action);
    bool ActionChangeJob(PlayObject* player, const ScriptAction& action);
    bool ActionChangeGender(PlayObject* player, const ScriptAction& action);
    bool ActionChangePKPoint(PlayObject* player, const ScriptAction& action);
    bool ActionMap(PlayObject* player, const ScriptAction& action);
    bool ActionMapMove(PlayObject* player, const ScriptAction& action);
    bool ActionRecall(PlayObject* player, const ScriptAction& action);
    bool ActionOpenMerchant(PlayObject* player, NPC* npc, const ScriptAction& action);
    bool ActionOpenRepair(PlayObject* player, NPC* npc, const ScriptAction& action);
    bool ActionOpenStorage(PlayObject* player, NPC* npc, const ScriptAction& action);
    bool ActionSendMsg(PlayObject* player, const ScriptAction& action);
    bool ActionMessageBox(PlayObject* player, const ScriptAction& action);
    bool ActionGoto(PlayObject* player, NPC* npc, const NPCScript& script, const ScriptAction& action);
    bool ActionBreak(PlayObject* player, const ScriptAction& action);
    bool ActionExit(PlayObject* player, const ScriptAction& action);
    bool ActionClose(PlayObject* player, const ScriptAction& action);

    // 怪物和宠物相关动作
    bool ActionMonGen(PlayObject* player, const ScriptAction& action);
    bool ActionKillMonster(PlayObject* player, const ScriptAction& action);
    bool ActionKillSlave(PlayObject* player, const ScriptAction& action);
    bool ActionRecallSlave(PlayObject* player, const ScriptAction& action);
    bool ActionClearSlave(PlayObject* player, const ScriptAction& action);
    bool ActionMonGenEx(PlayObject* player, const ScriptAction& action);
    bool ActionMobPlace(PlayObject* player, const ScriptAction& action);
    bool ActionMobCount(PlayObject* player, const ScriptAction& action);
    bool ActionClearMon(PlayObject* player, const ScriptAction& action);
    bool ActionClearItem(PlayObject* player, const ScriptAction& action);

    // 高级功能动作
    bool ActionGMExecute(PlayObject* player, const ScriptAction& action);
    bool ActionAddNameList(PlayObject* player, const ScriptAction& action);
    bool ActionDelNameList(PlayObject* player, const ScriptAction& action);
    bool ActionAddIPList(PlayObject* player, const ScriptAction& action);
    bool ActionDelIPList(PlayObject* player, const ScriptAction& action);
    bool ActionAddAccountList(PlayObject* player, const ScriptAction& action);
    bool ActionDelAccountList(PlayObject* player, const ScriptAction& action);
    bool ActionSetVar(PlayObject* player, const ScriptAction& action);
    bool ActionCalcVar(PlayObject* player, const ScriptAction& action);
    bool ActionSaveVar(PlayObject* player, const ScriptAction& action);
    bool ActionLoadVar(PlayObject* player, const ScriptAction& action);

    // 行会相关动作
    bool ActionAddGuild(PlayObject* player, const ScriptAction& action);
    bool ActionDelGuild(PlayObject* player, const ScriptAction& action);
    bool ActionGuildWar(PlayObject* player, const ScriptAction& action);
    bool ActionEndGuildWar(PlayObject* player, const ScriptAction& action);

    // 特殊功能动作
    bool ActionHair(PlayObject* player, const ScriptAction& action);
    bool ActionTakeW(PlayObject* player, const ScriptAction& action);
    bool ActionTakeOn(PlayObject* player, const ScriptAction& action);
    bool ActionSendMsgUser(PlayObject* player, const ScriptAction& action);
    bool ActionSendMsgMap(PlayObject* player, const ScriptAction& action);
    bool ActionSendMsgAll(PlayObject* player, const ScriptAction& action);
    bool ActionTimeRecallMob(PlayObject* player, const ScriptAction& action);
    bool ActionParam1(PlayObject* player, const ScriptAction& action);
    bool ActionParam2(PlayObject* player, const ScriptAction& action);
    bool ActionParam3(PlayObject* player, const ScriptAction& action);
    bool ActionAutoAddPoint(PlayObject* player, const ScriptAction& action);
    bool ActionDelayExecute(PlayObject* player, const ScriptAction& action);
    bool ActionRandExecute(PlayObject* player, const ScriptAction& action);
    bool ActionCheckExecute(PlayObject* player, const ScriptAction& action);
    bool ActionRestart(PlayObject* player, const ScriptAction& action);
    bool ActionPlayBGM(PlayObject* player, const ScriptAction& action);
    bool ActionPlayWav(PlayObject* player, const ScriptAction& action);
    bool ActionDayChangeColor(PlayObject* player, const ScriptAction& action);
    bool ActionNightChangeColor(PlayObject* player, const ScriptAction& action);
    bool ActionFireBurn(PlayObject* player, const ScriptAction& action);
    bool ActionLighting(PlayObject* player, const ScriptAction& action);
    bool ActionDigUp(PlayObject* player, const ScriptAction& action);
    bool ActionDigDown(PlayObject* player, const ScriptAction& action);

    // 补充的重要动作
    bool ActionServerNotice(PlayObject* player, const ScriptAction& action);
    bool ActionSystemBroadcast(PlayObject* player, const ScriptAction& action);
    bool ActionBuildCastle(PlayObject* player, const ScriptAction& action);
    bool ActionRepairCastle(PlayObject* player, const ScriptAction& action);
    bool ActionUpgradeCastle(PlayObject* player, const ScriptAction& action);
    bool ActionChangeGameGold(PlayObject* player, const ScriptAction& action);
    bool ActionChangeGamePoint(PlayObject* player, const ScriptAction& action);

    // 工具方法
    bool CompareValue(int value1, int value2, char method);
    int ParseIntParam(const std::string& param, int defaultValue = 0);
    std::string ParseStringParam(const std::string& param);
    Point FindSafeTeleportPosition(const std::string& mapName, const Point& targetPos);

    std::string m_lastError;
    std::unordered_map<std::string, std::function<bool(PlayObject*, const ScriptCondition&)>> m_conditionHandlers;
    std::unordered_map<std::string, std::function<bool(PlayObject*, NPC*, const ScriptAction&)>> m_actionHandlers;

    // 变量管理
    std::unordered_map<std::string, ScriptVar> m_scriptVars;
    mutable std::shared_mutex m_varsMutex;

    // 列表管理
    ListManager m_listManager;
};

// 脚本解析器类
class ScriptParser {
public:
    ScriptParser();
    ~ScriptParser();

    // 解析脚本文件
    bool ParseScriptFile(const std::string& scriptFile, NPCScript& script);

    // 解析单个脚本块
    bool ParseScriptBlock(const std::vector<std::string>& lines, int& lineIndex, ScriptBlock& block);

    // 解析条件行
    bool ParseConditionLine(const std::string& line, ScriptCondition& condition);

    // 解析动作行
    bool ParseActionLine(const std::string& line, ScriptAction& action);

    // 解析选项行
    bool ParseOptionLine(const std::string& line, ScriptOption& option);

    // 解析对话文本
    std::string ParseDialogText(const std::vector<std::string>& lines, int& lineIndex);

    // 工具方法
    static std::string Trim(const std::string& str);
    static std::vector<std::string> Split(const std::string& str, char delimiter);
    static std::vector<std::string> SplitParams(const std::string& str);
    static bool IsLabel(const std::string& line);
    static bool IsConditionSection(const std::string& line);
    static bool IsActionSection(const std::string& line);
    static bool IsElseActionSection(const std::string& line);
    static bool IsOption(const std::string& line);
    static bool IsComment(const std::string& line);
    static std::string ExtractLabel(const std::string& line);
    static std::string ReplaceVariables(const std::string& text, PlayObject* player, NPC* npc = nullptr);

    // 错误处理
    void SetLastError(const std::string& error, int lineNumber = -1);
    std::string GetLastError() const { return m_lastError; }
    int GetLastErrorLine() const { return m_lastErrorLine; }

private:
    std::string m_lastError;        // 最后的错误信息
    int m_lastErrorLine;            // 错误行号

    // 内部解析方法
    bool ParseParameters(const std::string& paramStr, ScriptCondition& condition);
    bool ParseParameters(const std::string& paramStr, ScriptAction& action);
    std::string ExtractOptionText(const std::string& line);
    std::string ExtractOptionGoto(const std::string& line);
    bool ValidateCondition(const ScriptCondition& condition);
    bool ValidateAction(const ScriptAction& action);
};

} // namespace MirServer

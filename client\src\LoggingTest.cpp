#include <iostream>
#include <fstream>
#include <filesystem>
#include "Utils/Logger.h"
#include "Utils/ExceptionHandler.h"

int main() {
    std::cout << "Starting logging test..." << std::endl;
    
    // Create logs directory if it doesn't exist
    try {
        std::filesystem::create_directories("logs");
        std::cout << "Logs directory created or already exists" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Failed to create logs directory: " << e.what() << std::endl;
    }
    
    // Try to create a test file
    try {
        std::ofstream testFile("logs/test.txt");
        if (testFile.is_open()) {
            testFile << "This is a test file" << std::endl;
            testFile.close();
            std::cout << "Test file created successfully" << std::endl;
        } else {
            std::cerr << "Failed to create test file" << std::endl;
        }
    } catch (const std::exception& e) {
        std::cerr << "Exception creating test file: " << e.what() << std::endl;
    }
    
    // Initialize logger
    if (!Logger::GetInstance().Initialize("logs/test.log", LogLevel::LOG_LEVEL_DEBUG)) {
        std::cerr << "Failed to initialize logger" << std::endl;
    } else {
        std::cout << "Logger initialized successfully" << std::endl;
        
        // Log some messages
        LOG_DEBUG("This is a debug message");
        LOG_INFO("This is an info message");
        LOG_WARNING("This is a warning message");
        LOG_ERROR("This is an error message");
        LOG_FATAL("This is a fatal message");
        
        // Log an exception
        try {
            throw std::runtime_error("This is a test exception");
        } catch (const std::exception& e) {
            LOG_EXCEPTION(e.what());
        }
        
        // Close logger
        Logger::GetInstance().Close();
        std::cout << "Logger closed" << std::endl;
    }
    
    // Check if log file exists
    if (std::filesystem::exists("logs/test.log")) {
        std::cout << "Log file exists" << std::endl;
        
        // Read and print log file contents
        std::ifstream logFile("logs/test.log");
        if (logFile.is_open()) {
            std::cout << "Log file contents:" << std::endl;
            std::string line;
            while (std::getline(logFile, line)) {
                std::cout << line << std::endl;
            }
            logFile.close();
        } else {
            std::cerr << "Failed to open log file for reading" << std::endl;
        }
    } else {
        std::cerr << "Log file does not exist" << std::endl;
    }
    
    std::cout << "Logging test completed" << std::endl;
    return 0;
}

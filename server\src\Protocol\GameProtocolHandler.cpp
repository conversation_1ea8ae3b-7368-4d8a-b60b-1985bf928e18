#include "GameProtocolHandler.h"
#include "PacketTypes.h"
#include "MessageConverter.h"
#include "../Common/Logger.h"
#include "../GameEngine/GameEngine.h"
#include "../GameEngine/GroupManager.h"
#include <cstring>

namespace MirServer {
namespace Protocol {

GameProtocolHandler::GameProtocolHandler() {
}

GameProtocolHandler::~GameProtocolHandler() {
}

void GameProtocolHandler::HandlePacket(std::shared_ptr<Network::ClientConnection> client,
                                      const PacketHeader& header,
                                      const uint8_t* data,
                                      size_t dataSize) {
    // 获取玩家对象
    PlayObject* player = GetPlayerByConnection(client);
    if (!player) {
        Logger::Warning("Received packet from unknown client: " + std::to_string(client->GetId()));
        return;
    }

    // 更新玩家活跃时间
    player->UpdateActiveTime();

    // 根据协议类型处理
    switch (header.packetType) {
        // 仓库系统
        case CM_USERSTORAGEITEM:
            HandleStorageItem(player, data, dataSize);
            break;

        case CM_USERTAKEBACKSTORAGEITEM:
            HandleTakebackStorageItem(player, data, dataSize);
            break;

        // 修理系统
        case CM_USERREPAIRITEM:
            HandleRepairItem(player, data, dataSize);
            break;

        case CM_MERCHANTQUERYREPAIRCOST:
            HandleQueryRepairCost(player, data, dataSize);
            break;

        // 交易系统
        case CM_DEALTRY:
            HandleDealTry(player, data, dataSize);
            break;

        case CM_DEALADDITEM:
            HandleDealAddItem(player, data, dataSize);
            break;

        case CM_DEALDELITEM:
            HandleDealDeleteItem(player, data, dataSize);
            break;

        case CM_DEALCANCEL:
            HandleDealCancel(player, data, dataSize);
            break;

        case CM_DEALCHGGOLD:
            HandleDealChangeGold(player, data, dataSize);
            break;

        case CM_DEALEND:
            HandleDealEnd(player, data, dataSize);
            break;

        // 小地图
        case CM_WANTMINIMAP:
            HandleWantMinimap(player, data, dataSize);
            break;

        // 组队系统
        case CM_GROUPMODE:
            HandleGroupMode(player, data, dataSize);
            break;

        case CM_CREATEGROUP:
            HandleCreateGroup(player, data, dataSize);
            break;

        case CM_ADDGROUPMEMBER:
            HandleAddGroupMember(player, data, dataSize);
            break;

        case CM_DELGROUPMEMBER:
            HandleDelGroupMember(player, data, dataSize);
            break;

        case CM_GROUPCHAT:
            HandleGroupChat(player, data, dataSize);
            break;

        case CM_GROUPTELEPORT:
            HandleGroupTeleport(player, data, dataSize);
            break;

        case CM_GROUPEXPMODE:
            HandleGroupExpMode(player, data, dataSize);
            break;

        // 基础移动
        case CM_WALK:
            HandleWalk(player, data, dataSize);
            break;

        case CM_RUN:
            HandleRun(player, data, dataSize);
            break;

        case CM_TURN:
            HandleTurn(player, data, dataSize);
            break;

        // 聊天
        case CM_SAY:
            HandleSay(player, data, dataSize);
            break;

        // 物品操作
        case CM_PICKUP:
            HandlePickup(player, data, dataSize);
            break;

        case CM_DROPITEM:
            HandleDropItem(player, data, dataSize);
            break;

        case CM_USEBAGITEM:
            HandleUseBagItem(player, data, dataSize);
            break;

        case CM_TAKEONITEM:
            HandleTakeOnItem(player, data, dataSize);
            break;

        case CM_TAKEOFFITEM:
            HandleTakeOffItem(player, data, dataSize);
            break;

        // NPC交互
        case CM_CLICKNPC:
            HandleClickNPC(player, data, dataSize);
            break;

        default:
            Logger::Warning("Unhandled packet type: " + std::to_string(header.packetType) +
                          " from player: " + player->GetCharName());
            break;
    }
}

void GameProtocolHandler::OnClientConnected(std::shared_ptr<Network::ClientConnection> client) {
    Logger::Info("Client connected: " + std::to_string(client->GetId()));
}

void GameProtocolHandler::OnClientDisconnected(std::shared_ptr<Network::ClientConnection> client) {
    Logger::Info("Client disconnected: " + std::to_string(client->GetId()));

    // TODO: 清理玩家数据
    PlayObject* player = GetPlayerByConnection(client);
    if (player) {
        Logger::Info("Player " + player->GetCharName() + " disconnected");
        // 执行玩家登出逻辑
        player->LogoutGame();
    }
}

void GameProtocolHandler::HandleStorageItem(PlayObject* player, const uint8_t* data, size_t dataSize) {
    StorageItemRequest request;
    if (!ParseStorageItemRequest(data, dataSize, request)) {
        SendErrorMessage(player->GetConnection(), "Invalid storage item request");
        return;
    }

    // 获取仓库管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* storageManager = gameEngine.GetStorageManager();

    if (!storageManager) {
        player->SendDefMessage(SM_STORAGE_FAIL, 0, 0, 0, 0);
        return;
    }

    // 如果提供了密码，先尝试打开仓库
    if (!request.password.empty()) {
        if (!player->OpenStorage(request.password)) {
            return; // 密码错误，OpenStorage已经发送了错误消息
        }
    }

    // 获取物品
    UserItem* item = player->GetBagItemByIndex(request.itemIndex);
    if (!item) {
        player->SendDefMessage(SM_STORAGE_FAIL, 0, 0, 0, 0);
        player->SendMessage("物品不存在", 0);
        return;
    }

    // 存储物品
    if (player->StorageAddItem(*item)) {
        Logger::Debug("Player " + player->GetCharName() + " stored item " + std::to_string(request.itemIndex));
    }
}

void GameProtocolHandler::HandleTakebackStorageItem(PlayObject* player, const uint8_t* data, size_t dataSize) {
    TakebackItemRequest request;
    if (!ParseTakebackItemRequest(data, dataSize, request)) {
        SendErrorMessage(player->GetConnection(), "Invalid takeback item request");
        return;
    }

    // 从仓库取出物品
    UserItem outItem;
    if (player->StorageTakeItem(request.itemIndex, outItem)) {
        Logger::Debug("Player " + player->GetCharName() + " took back item " + std::to_string(request.itemIndex));
    }
}

void GameProtocolHandler::HandleRepairItem(PlayObject* player, const uint8_t* data, size_t dataSize) {
    RepairItemRequest request;
    if (!ParseRepairItemRequest(data, dataSize, request)) {
        SendErrorMessage(player->GetConnection(), "Invalid repair item request");
        return;
    }

    // 获取修理管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* repairManager = gameEngine.GetRepairManager();

    if (!repairManager) {
        player->SendDefMessage(SM_REPAIRITEM_FAIL, 0, 0, 0, 0);
        return;
    }

    // 修理物品
    auto result = repairManager->RepairItem(player, request.itemIndex);

    Logger::Debug("Player " + player->GetCharName() + " repair item " + std::to_string(request.itemIndex) +
                 " result: " + std::to_string(static_cast<int>(result)));
}

void GameProtocolHandler::HandleQueryRepairCost(PlayObject* player, const uint8_t* data, size_t dataSize) {
    RepairItemRequest request;
    if (!ParseRepairItemRequest(data, dataSize, request)) {
        SendErrorMessage(player->GetConnection(), "Invalid query repair cost request");
        return;
    }

    // 获取修理管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* repairManager = gameEngine.GetRepairManager();

    if (!repairManager) {
        player->SendDefMessage(SM_QUERYREPAIRCOST, 0, 0, 0, 0);
        return;
    }

    // 查询修理费用
    DWORD cost = repairManager->GetRepairCost(player, request.itemIndex);

    // 发送修理费用
    player->SendDefMessage(SM_QUERYREPAIRCOST, request.itemIndex,
                          static_cast<WORD>(cost & 0xFFFF),
                          static_cast<WORD>(cost >> 16), 0);

    Logger::Debug("Player " + player->GetCharName() + " queried repair cost for item " +
                 std::to_string(request.itemIndex) + ": " + std::to_string(cost));
}

void GameProtocolHandler::HandleDealTry(PlayObject* player, const uint8_t* data, size_t dataSize) {
    DealRequest request;
    if (!ParseDealRequest(data, dataSize, request)) {
        SendErrorMessage(player->GetConnection(), "Invalid deal try request");
        return;
    }

    // 获取交易管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* tradeManager = gameEngine.GetTradeManager();

    if (!tradeManager) {
        player->SendDefMessage(SM_DEALMENU, 0, 0, 0, 0);
        return;
    }

    // TODO: 根据targetId找到目标玩家
    PlayObject* target = nullptr; // GetPlayerById(request.targetId);

    if (!target) {
        player->SendMessage("目标玩家不存在", 0);
        return;
    }

    // 请求交易
    if (tradeManager->RequestTrade(player, target)) {
        Logger::Debug("Player " + player->GetCharName() + " requested trade with " + target->GetCharName());
    }
}

PlayObject* GameProtocolHandler::GetPlayerByConnection(std::shared_ptr<Network::ClientConnection> client) {
    // TODO: 实现根据连接获取玩家对象
    // 这里需要维护一个连接ID到玩家对象的映射
    return nullptr;
}

void GameProtocolHandler::SendErrorMessage(std::shared_ptr<Network::ClientConnection> client, const std::string& message) {
    // TODO: 实现发送错误消息到客户端
    Logger::Warning("Error for client " + std::to_string(client->GetId()) + ": " + message);
}

// 解析方法实现
bool GameProtocolHandler::ParseStorageItemRequest(const uint8_t* data, size_t dataSize, StorageItemRequest& request) {
    if (dataSize < sizeof(WORD)) {
        return false;
    }

    const uint8_t* ptr = data;

    // 解析物品索引
    std::memcpy(&request.itemIndex, ptr, sizeof(WORD));
    ptr += sizeof(WORD);

    // 解析密码（如果有）
    if (dataSize > sizeof(WORD)) {
        size_t passwordLen = dataSize - sizeof(WORD);
        request.password = std::string(reinterpret_cast<const char*>(ptr), passwordLen);
    }

    return true;
}

bool GameProtocolHandler::ParseTakebackItemRequest(const uint8_t* data, size_t dataSize, TakebackItemRequest& request) {
    if (dataSize < sizeof(WORD)) {
        return false;
    }

    std::memcpy(&request.itemIndex, data, sizeof(WORD));
    return true;
}

bool GameProtocolHandler::ParseRepairItemRequest(const uint8_t* data, size_t dataSize, RepairItemRequest& request) {
    if (dataSize < sizeof(WORD)) {
        return false;
    }

    std::memcpy(&request.itemIndex, data, sizeof(WORD));
    return true;
}

bool GameProtocolHandler::ParseDealRequest(const uint8_t* data, size_t dataSize, DealRequest& request) {
    if (dataSize < sizeof(DWORD)) {
        return false;
    }

    std::memcpy(&request.targetId, data, sizeof(DWORD));
    return true;
}

// 组队协议处理实现
void GameProtocolHandler::HandleGroupMode(PlayObject* player, const uint8_t* data, size_t dataSize) {
    GroupModeRequest request;
    if (!ParseGroupModeRequest(data, dataSize, request)) {
        SendErrorMessage(player->GetConnection(), "Invalid group mode request");
        return;
    }

    // 获取组队管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* groupManager = gameEngine.GetGroupManager();

    if (!groupManager) {
        player->SendMessage("组队系统暂时不可用", 0);
        return;
    }

    // 设置组队模式
    GroupMode mode = static_cast<GroupMode>(request.mode);
    groupManager->SetGroupMode(player, mode);

    Logger::Debug("Player " + player->GetCharName() + " set group mode to " + std::to_string(request.mode));
}

void GameProtocolHandler::HandleCreateGroup(PlayObject* player, const uint8_t* data, size_t dataSize) {
    // 获取组队管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* groupManager = gameEngine.GetGroupManager();

    if (!groupManager) {
        player->SendMessage("组队系统暂时不可用", 0);
        return;
    }

    // 创建组队
    if (groupManager->CreateGroup(player)) {
        Logger::Info("Player " + player->GetCharName() + " created a group");
    }
}

void GameProtocolHandler::HandleAddGroupMember(PlayObject* player, const uint8_t* data, size_t dataSize) {
    AddGroupMemberRequest request;
    if (!ParseAddGroupMemberRequest(data, dataSize, request)) {
        SendErrorMessage(player->GetConnection(), "Invalid add group member request");
        return;
    }

    // 获取组队管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* groupManager = gameEngine.GetGroupManager();

    if (!groupManager) {
        player->SendMessage("组队系统暂时不可用", 0);
        return;
    }

    // 查找目标玩家
    auto target = gameEngine.FindPlayerByName(request.playerName);
    if (!target) {
        player->SendMessage("玩家 " + request.playerName + " 不在线", 0);
        return;
    }

    // 邀请玩家加入组队
    if (groupManager->InvitePlayer(player, target.get())) {
        Logger::Info("Player " + player->GetCharName() + " invited " + request.playerName + " to group");
    }
}

void GameProtocolHandler::HandleDelGroupMember(PlayObject* player, const uint8_t* data, size_t dataSize) {
    DelGroupMemberRequest request;
    if (!ParseDelGroupMemberRequest(data, dataSize, request)) {
        SendErrorMessage(player->GetConnection(), "Invalid delete group member request");
        return;
    }

    // 获取组队管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* groupManager = gameEngine.GetGroupManager();

    if (!groupManager) {
        player->SendMessage("组队系统暂时不可用", 0);
        return;
    }

    // 如果是自己，则离开组队
    if (request.playerName == player->GetCharName()) {
        if (groupManager->LeaveGroup(player)) {
            Logger::Info("Player " + player->GetCharName() + " left the group");
        }
    } else {
        // 踢出成员
        if (groupManager->KickMember(player, request.playerName)) {
            Logger::Info("Player " + player->GetCharName() + " kicked " + request.playerName + " from group");
        }
    }
}

// 组队相关解析方法实现
bool GameProtocolHandler::ParseGroupModeRequest(const uint8_t* data, size_t dataSize, GroupModeRequest& request) {
    if (dataSize < sizeof(BYTE)) {
        return false;
    }

    std::memcpy(&request.mode, data, sizeof(BYTE));
    return true;
}

bool GameProtocolHandler::ParseAddGroupMemberRequest(const uint8_t* data, size_t dataSize, AddGroupMemberRequest& request) {
    if (dataSize == 0) {
        return false;
    }

    // 解析玩家名称
    request.playerName = std::string(reinterpret_cast<const char*>(data), dataSize);

    // 移除可能的空字符
    size_t nullPos = request.playerName.find('\0');
    if (nullPos != std::string::npos) {
        request.playerName = request.playerName.substr(0, nullPos);
    }

    return !request.playerName.empty();
}

bool GameProtocolHandler::ParseDelGroupMemberRequest(const uint8_t* data, size_t dataSize, DelGroupMemberRequest& request) {
    if (dataSize == 0) {
        return false;
    }

    // 解析玩家名称
    request.playerName = std::string(reinterpret_cast<const char*>(data), dataSize);

    // 移除可能的空字符
    size_t nullPos = request.playerName.find('\0');
    if (nullPos != std::string::npos) {
        request.playerName = request.playerName.substr(0, nullPos);
    }

    return !request.playerName.empty();
}

// 新增的组队协议处理方法实现
void GameProtocolHandler::HandleGroupChat(PlayObject* player, const uint8_t* data, size_t dataSize) {
    GroupChatRequest request;
    if (!ParseGroupChatRequest(data, dataSize, request)) {
        SendErrorMessage(player->GetConnection(), "Invalid group chat request");
        return;
    }

    // 获取组队管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* groupManager = gameEngine.GetGroupManager();

    if (!groupManager) {
        player->SendMessage("组队系统暂时不可用", 0);
        return;
    }

    // 发送组队消息
    groupManager->SendGroupMessage(player, request.message);

    Logger::Debug("Group chat from " + player->GetCharName() + ": " + request.message);
}

void GameProtocolHandler::HandleGroupTeleport(PlayObject* player, const uint8_t* data, size_t dataSize) {
    GroupTeleportRequest request;
    if (!ParseGroupTeleportRequest(data, dataSize, request)) {
        SendErrorMessage(player->GetConnection(), "Invalid group teleport request");
        return;
    }

    // 获取组队管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* groupManager = gameEngine.GetGroupManager();

    if (!groupManager) {
        player->SendMessage("组队系统暂时不可用", 0);
        return;
    }

    if (request.teleportType == 0) {
        // 召集队员到队长位置
        groupManager->TeleportGroupToLeader(player);
    } else if (request.teleportType == 1) {
        // 传送队伍到指定位置
        groupManager->TeleportGroupToPosition(player, request.mapName, request.x, request.y);
    }

    Logger::Info("Group teleport by " + player->GetCharName() + " type: " + std::to_string(request.teleportType));
}

void GameProtocolHandler::HandleGroupExpMode(PlayObject* player, const uint8_t* data, size_t dataSize) {
    GroupExpModeRequest request;
    if (!ParseGroupExpModeRequest(data, dataSize, request)) {
        SendErrorMessage(player->GetConnection(), "Invalid group exp mode request");
        return;
    }

    // 获取组队管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* groupManager = gameEngine.GetGroupManager();

    if (!groupManager) {
        player->SendMessage("组队系统暂时不可用", 0);
        return;
    }

    // 设置经验分配模式
    ExpShareMode mode = static_cast<ExpShareMode>(request.expMode);
    groupManager->SetExpShareMode(player, mode);

    Logger::Info("Player " + player->GetCharName() + " set group exp mode to " + std::to_string(request.expMode));
}

// 新增的解析方法实现
bool GameProtocolHandler::ParseGroupChatRequest(const uint8_t* data, size_t dataSize, GroupChatRequest& request) {
    if (dataSize == 0) {
        return false;
    }

    // 解析聊天消息
    request.message = std::string(reinterpret_cast<const char*>(data), dataSize);

    // 移除可能的空字符
    size_t nullPos = request.message.find('\0');
    if (nullPos != std::string::npos) {
        request.message = request.message.substr(0, nullPos);
    }

    return !request.message.empty();
}

bool GameProtocolHandler::ParseGroupTeleportRequest(const uint8_t* data, size_t dataSize, GroupTeleportRequest& request) {
    if (dataSize < sizeof(BYTE)) {
        return false;
    }

    size_t offset = 0;

    // 解析传送类型
    std::memcpy(&request.teleportType, data + offset, sizeof(BYTE));
    offset += sizeof(BYTE);

    if (request.teleportType == 1) {
        // 需要解析地图名和坐标
        if (dataSize < offset + sizeof(WORD) * 2) {
            return false;
        }

        // 解析坐标
        std::memcpy(&request.x, data + offset, sizeof(WORD));
        offset += sizeof(WORD);
        std::memcpy(&request.y, data + offset, sizeof(WORD));
        offset += sizeof(WORD);

        // 解析地图名
        if (offset < dataSize) {
            request.mapName = std::string(reinterpret_cast<const char*>(data + offset), dataSize - offset);

            // 移除可能的空字符
            size_t nullPos = request.mapName.find('\0');
            if (nullPos != std::string::npos) {
                request.mapName = request.mapName.substr(0, nullPos);
            }
        }
    }

    return true;
}

bool GameProtocolHandler::ParseGroupExpModeRequest(const uint8_t* data, size_t dataSize, GroupExpModeRequest& request) {
    if (dataSize < sizeof(BYTE)) {
        return false;
    }

    std::memcpy(&request.expMode, data, sizeof(BYTE));
    return true;
}

} // namespace Protocol
} // namespace MirServer

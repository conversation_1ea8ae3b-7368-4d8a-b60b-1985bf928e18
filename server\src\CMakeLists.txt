# 源代码目录CMakeLists.txt

# 创建共享库
add_library(Common STATIC
    Common/Types.cpp
    Common/Logger.cpp
    Common/Config.cpp
    Common/Utils.cpp
)

add_library(Protocol STATIC
    Protocol/PacketTypes.cpp
    Protocol/MessageConverter.cpp
    Protocol/NetworkManager.cpp
)

add_library(BaseObject STATIC
    BaseObject/BaseObject.cpp
    BaseObject/PlayObject.cpp
    BaseObject/NPC.cpp
    BaseObject/Monster.cpp
    BaseObject/QuestNPC.cpp
)

# 设置包含目录
target_include_directories(Common PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})
target_include_directories(Protocol PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})
target_include_directories(BaseObject PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})

# 链接依赖
target_link_libraries(Protocol PUBLIC Common)
target_link_libraries(BaseObject PUBLIC Common Protocol)

# 平台特定的链接库
if(WIN32)
    target_link_libraries(Protocol PUBLIC ws2_32)
endif()

# 查找线程库
find_package(Threads REQUIRED)
target_link_libraries(Common PUBLIC Threads::Threads)

# 添加服务器子目录
add_subdirectory(LoginServer)
add_subdirectory(DBServer)
add_subdirectory(GateServer)
add_subdirectory(SelGateServer)
add_subdirectory(GameEngine)

# 设置C++标准
set_property(TARGET Common PROPERTY CXX_STANDARD 17)
set_property(TARGET Protocol PROPERTY CXX_STANDARD 17)
set_property(TARGET BaseObject PROPERTY CXX_STANDARD 17)
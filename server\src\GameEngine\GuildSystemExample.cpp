#include "GuildManager.h"
#include "GuildProtocolHandler.h"
#include "../BaseObject/PlayObject.h"
#include "../Common/Logger.h"
#include <iostream>
#include <memory>

using namespace MirServer;

// 简单的测试函数
void TestGuildSystem() {
    std::cout << "=== Guild System Test Start ===" << std::endl;

    try {
        // 初始化日志系统
        std::cout << "Initializing logger..." << std::endl;
        Logger::SetLogFile("guild_test.log");
        Logger::SetLogLevel(LogLevel::LOG_DEBUG);
        std::cout << "Logger initialized." << std::endl;

        // 初始化行会管理器
        std::cout << "Initializing guild manager..." << std::endl;
        auto& guildManager = GuildManager::GetInstance();
        if (!guildManager.Initialize()) {
            std::cout << "Guild manager initialization failed" << std::endl;
            return;
        }

        std::cout << "Guild manager initialized successfully" << std::endl;

        // 创建测试玩家
        std::cout << "Creating test players..." << std::endl;
        auto player1 = std::make_unique<PlayObject>();
        auto player2 = std::make_unique<PlayObject>();
        auto player3 = std::make_unique<PlayObject>();

        // 设置玩家基本信息
        auto& humData1 = const_cast<HumDataInfo&>(player1->GetHumDataInfo());
        humData1.charName = "TestPlayer1";
        humData1.level = 50;
        humData1.job = JobType::WARRIOR; // 战士

        auto& humData2 = const_cast<HumDataInfo&>(player2->GetHumDataInfo());
        humData2.charName = "TestPlayer2";
        humData2.level = 45;
        humData2.job = JobType::WIZARD; // 法师

        auto& humData3 = const_cast<HumDataInfo&>(player3->GetHumDataInfo());
        humData3.charName = "TestPlayer3";
        humData3.level = 40;
        humData3.job = JobType::TAOIST; // 道士

        std::cout << "Test players created successfully" << std::endl;

        // 测试1: 创建行会
        std::cout << "\n--- Test 1: Create Guild ---" << std::endl;
        bool result = guildManager.CreateGuild("TestGuild", player1.get());
        if (result) {
            std::cout << "✓ Guild created successfully: TestGuild" << std::endl;
            std::cout << "✓ Chief: " << player1->GetCharName() << std::endl;
            std::cout << "✓ Player guild info: " << player1->GetGuildName() << std::endl;
        } else {
            std::cout << "✗ Guild creation failed" << std::endl;
            return;
        }

    // 获取行会对象
    Guild* guild = guildManager.FindGuild("TestGuild");
    if (!guild) {
        std::cout << "✗ 无法找到创建的行会" << std::endl;
        return;
    }

    // 测试2: 添加行会成员
    std::cout << "\n--- 测试2: 添加行会成员 ---" << std::endl;
    bool addResult1 = guild->AddMember(player2.get(), GuildRank::MEMBER);
    bool addResult2 = guild->AddMember(player3.get(), GuildRank::MEMBER);

    if (addResult1 && addResult2) {
        std::cout << "✓ 成员添加成功" << std::endl;
        std::cout << "✓ 当前成员数量: " << guild->GetMemberCount() << std::endl;
        std::cout << "✓ Player2行会: " << player2->GetGuildName() << std::endl;
        std::cout << "✓ Player3行会: " << player3->GetGuildName() << std::endl;
    } else {
        std::cout << "✗ 成员添加失败" << std::endl;
    }

    // 测试3: 职位管理
    std::cout << "\n--- 测试3: 职位管理 ---" << std::endl;
    bool rankResult = guild->UpdateMemberRank("TestPlayer2", GuildRank::VICE_CHIEF, "副会长");
    if (rankResult) {
        std::cout << "✓ 职位更新成功" << std::endl;
        std::cout << "✓ Player2新职位: " << guild->GetRankName("TestPlayer2") << std::endl;
        std::cout << "✓ Player2职位等级: " << static_cast<int>(guild->GetMemberRank("TestPlayer2")) << std::endl;
    } else {
        std::cout << "✗ 职位更新失败" << std::endl;
    }

    // 测试4: 行会公告
    std::cout << "\n--- 测试4: 行会公告 ---" << std::endl;
    guild->AddNotice("欢迎新成员加入行会！");
    guild->AddNotice("行会活动通知：明天晚上8点攻城战");
    guild->AddNotice("请大家积极参与行会建设");

    const auto& notices = guild->GetNotices();
    std::cout << "✓ 公告数量: " << notices.size() << std::endl;
    for (size_t i = 0; i < notices.size(); ++i) {
        std::cout << "  " << (i + 1) << ". " << notices[i] << std::endl;
    }

    // 测试5: 行会属性
    std::cout << "\n--- 测试5: 行会属性 ---" << std::endl;
    guild->SetBuildPoint(1000);
    guild->SetAurae(500);
    guild->SetStability(800);
    guild->SetFlourishing(600);

    std::cout << "✓ 建设度: " << guild->GetBuildPoint() << std::endl;
    std::cout << "✓ 灵气值: " << guild->GetAurae() << std::endl;
    std::cout << "✓ 安定度: " << guild->GetStability() << std::endl;
    std::cout << "✓ 繁荣度: " << guild->GetFlourishing() << std::endl;

    // 测试6: 创建第二个行会用于测试联盟和战争
    std::cout << "\n--- 测试6: 行会联盟和战争 ---" << std::endl;
    auto player4 = std::make_unique<PlayObject>();
    auto& humData4 = const_cast<HumDataInfo&>(player4->GetHumDataInfo());
    humData4.charName = "TestPlayer4";
    humData4.level = 48;
    humData4.job = JobType::WARRIOR;

    bool guild2Result = guildManager.CreateGuild("TestGuild2", player4.get());
    if (guild2Result) {
        std::cout << "✓ 第二个行会创建成功: TestGuild2" << std::endl;

        Guild* guild2 = guildManager.FindGuild("TestGuild2");
        if (guild2) {
            // 测试联盟
            bool allyResult = guild->AddAlly(guild2);
            if (allyResult) {
                std::cout << "✓ 行会联盟成功" << std::endl;
                std::cout << "✓ 联盟检查: " << (guild->IsAlly(guild2) ? "是联盟" : "不是联盟") << std::endl;
                std::cout << "✓ 玩家联盟检查: " << (player1->IsGuildAlly(player4.get()) ? "是联盟" : "不是联盟") << std::endl;
            } else {
                std::cout << "✗ 行会联盟失败" << std::endl;
            }

            // 解除联盟后测试战争
            guild->RemoveAlly(guild2);
            std::cout << "✓ 联盟已解除" << std::endl;

            // 测试战争
            bool warResult = guild->StartWar(guild2);
            if (warResult) {
                std::cout << "✓ 行会宣战成功" << std::endl;
                std::cout << "✓ 战争检查: " << (guild->IsWarWith(guild2) ? "处于战争状态" : "不在战争状态") << std::endl;
                std::cout << "✓ 玩家敌对检查: " << (player1->IsGuildEnemy(player4.get()) ? "是敌人" : "不是敌人") << std::endl;

                // 停战
                bool peaceResult = guild->EndWar(guild2);
                if (peaceResult) {
                    std::cout << "✓ 停战成功" << std::endl;
                    std::cout << "✓ 战争状态: " << (guild->IsWarWith(guild2) ? "仍在战争" : "已停战") << std::endl;
                }
            } else {
                std::cout << "✗ 行会宣战失败" << std::endl;
            }
        }
    } else {
        std::cout << "✗ 第二个行会创建失败" << std::endl;
    }

    // 测试7: 协议处理器
    std::cout << "\n--- 测试7: 协议处理器 ---" << std::endl;

    // 测试权限检查
    bool chiefCanManage = GuildProtocolHandler::CanManageMembers(player1.get());
    bool viceCanManage = GuildProtocolHandler::CanManageMembers(player2.get());
    bool memberCanManage = GuildProtocolHandler::CanManageMembers(player3.get());

    std::cout << "✓ 会长管理权限: " << (chiefCanManage ? "有权限" : "无权限") << std::endl;
    std::cout << "✓ 副会长管理权限: " << (viceCanManage ? "有权限" : "无权限") << std::endl;
    std::cout << "✓ 普通成员管理权限: " << (memberCanManage ? "有权限" : "无权限") << std::endl;

    bool chiefCanWar = GuildProtocolHandler::CanDeclareWar(player1.get());
    bool viceCanWar = GuildProtocolHandler::CanDeclareWar(player2.get());

    std::cout << "✓ 会长宣战权限: " << (chiefCanWar ? "有权限" : "无权限") << std::endl;
    std::cout << "✓ 副会长宣战权限: " << (viceCanWar ? "有权限" : "无权限") << std::endl;

    // 测试8: 成员管理
    std::cout << "\n--- 测试8: 成员管理 ---" << std::endl;

    // 显示所有成员信息
    const auto& members = guild->GetMembers();
    std::cout << "✓ 行会成员列表:" << std::endl;
    for (const auto& member : members) {
        std::cout << "  - " << member.playerName
                  << " (" << member.rankName << ")"
                  << " [" << (member.isOnline ? "在线" : "离线") << "]" << std::endl;
    }

    // 测试删除成员
    bool removeResult = guild->RemoveMember("TestPlayer3");
    if (removeResult) {
        std::cout << "✓ 成员删除成功" << std::endl;
        std::cout << "✓ 当前成员数量: " << guild->GetMemberCount() << std::endl;
        std::cout << "✓ Player3行会信息: " << (player3->GetGuildName().empty() ? "已清除" : player3->GetGuildName()) << std::endl;
    } else {
        std::cout << "✗ 成员删除失败" << std::endl;
    }

    // 测试9: 行会运行时处理
    std::cout << "\n--- 测试9: 运行时处理 ---" << std::endl;
    guild->Run();
    guildManager.Run();
    std::cout << "✓ 运行时处理完成" << std::endl;

    // 清理
    std::cout << "\n--- 清理测试环境 ---" << std::endl;
    guildManager.Finalize();
    // Logger不需要显式清理

    std::cout << "\n=== 行会系统测试完成 ===" << std::endl;
    std::cout << "所有核心功能测试通过！" << std::endl;
}

int main() {
    try {
        TestGuildSystem();
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "测试过程中发生未知异常" << std::endl;
        return 1;
    }
}

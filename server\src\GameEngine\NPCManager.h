#pragma once

#include "../Common/Types.h"
#include "../BaseObject/NPC.h"
#include <unordered_map>
#include <vector>
#include <string>
#include <memory>
#include <mutex>
#include <shared_mutex>

namespace MirServer {

// 前向声明
class Environment;

// NPC模板结构
struct NPCTemplate {
    WORD npcId = 0;                    // NPC ID
    std::string npcName;               // NPC名称
    NPCType npcType = NPCType::NORMAL; // NPC类型
    WORD appr = 0;                     // 外观
    std::string mapName;               // 地图名称
    Point position{0, 0};              // 位置
    DirectionType direction = DirectionType::DOWN; // 朝向
    std::string scriptFile;            // 脚本文件
    bool isActive = true;              // 是否激活

    // 商人相关
    std::vector<WORD> shopItems;       // 商店物品列表
    int buyRate = 100;                 // 收购价格比例
    int sellRate = 120;                // 出售价格比例

    // 守卫相关
    int guardRange = 10;               // 守卫范围
    bool attackCriminals = true;       // 是否攻击红名
};

// NPC刷新点结构
struct NPCSpawnPoint {
    WORD npcTemplateId = 0;            // NPC模板ID
    std::string mapName;               // 地图名称
    Point position{0, 0};              // 位置
    DirectionType direction = DirectionType::DOWN; // 朝向
    bool isActive = true;              // 是否激活
    DWORD respawnTime = 0;             // 重生时间（0表示不重生）
    DWORD lastSpawnTime = 0;           // 上次刷新时间
};

// NPC管理器类
class NPCManager {
public:
    NPCManager();
    ~NPCManager();

    // 初始化和清理
    bool Initialize();
    void Finalize();

    // NPC模板管理
    bool LoadNPCTemplates(const std::string& filename = "GameData/NPCList.txt");
    bool AddNPCTemplate(const NPCTemplate& npcTemplate);
    const NPCTemplate* GetNPCTemplate(WORD npcId) const;
    void ClearNPCTemplates();

    // NPC刷新点管理
    bool LoadSpawnPoints(const std::string& filename = "GameData/NPCSpawn.txt");
    bool AddSpawnPoint(const NPCSpawnPoint& spawnPoint);
    void ClearSpawnPoints();

    // NPC创建和管理
    std::shared_ptr<NPC> CreateNPC(WORD npcTemplateId, const std::string& mapName, const Point& pos, DirectionType dir = DirectionType::DOWN);
    std::shared_ptr<NPC> CreateNPCFromTemplate(const NPCTemplate& npcTemplate);
    bool SpawnNPC(const NPCSpawnPoint& spawnPoint);
    void SpawnAllNPCs();

    // NPC查找
    std::shared_ptr<NPC> FindNPC(uint32_t objectId) const;
    std::shared_ptr<NPC> FindNPCByName(const std::string& npcName) const;
    std::vector<std::shared_ptr<NPC>> GetNPCsInMap(const std::string& mapName) const;
    std::vector<std::shared_ptr<NPC>> GetNPCsInRange(const std::string& mapName, const Point& center, int range) const;

    // NPC移除
    bool RemoveNPC(uint32_t objectId);
    bool RemoveNPCByName(const std::string& npcName);
    void RemoveAllNPCs();

    // 运行时更新
    void Run();
    void ProcessNPCs();
    void CheckRespawns();

    // 商店相关
    bool SetupMerchant(std::shared_ptr<Merchant> merchant, const NPCTemplate& npcTemplate);
    bool AddShopItem(WORD npcId, WORD itemIndex, int price = -1);
    bool RemoveShopItem(WORD npcId, WORD itemIndex);

    // 脚本相关
    bool LoadNPCScript(std::shared_ptr<NPC> npc, const std::string& scriptFile);
    void ReloadAllScripts();

    // 统计信息
    struct Statistics {
        int totalNPCs = 0;
        int activeNPCs = 0;
        int merchants = 0;
        int guards = 0;
        int questNPCs = 0;
        DWORD lastUpdateTime = 0;
    };

    const Statistics& GetStatistics() const { return m_statistics; }
    void UpdateStatistics();

private:
    // 数据解析
    bool ParseNPCTemplateLine(const std::string& line, NPCTemplate& npcTemplate);
    bool ParseSpawnPointLine(const std::string& line, NPCSpawnPoint& spawnPoint);

    // NPC设置
    void SetupNPCFromTemplate(std::shared_ptr<NPC> npc, const NPCTemplate& npcTemplate);
    void SetupNPCDialogs(std::shared_ptr<NPC> npc, const std::string& scriptFile);

    // 环境管理
    void SetEnvironmentManager(std::shared_ptr<Environment> envManager) { m_envManager = envManager; }

private:
    bool m_initialized;

    // NPC模板数据
    std::unordered_map<WORD, NPCTemplate> m_npcTemplates;

    // NPC刷新点
    std::vector<NPCSpawnPoint> m_spawnPoints;

    // 活跃的NPC列表
    std::unordered_map<uint32_t, std::shared_ptr<NPC>> m_npcs; // objectId -> npc
    std::unordered_map<std::string, std::shared_ptr<NPC>> m_npcsByName; // name -> npc

    // 环境管理器
    std::shared_ptr<Environment> m_envManager;

    // 统计信息
    Statistics m_statistics;

    // 运行时状态
    DWORD m_lastRunTime = 0;
    DWORD m_lastRespawnCheckTime = 0;
    DWORD m_respawnCheckInterval = 30000; // 30秒检查一次重生

    // 线程安全
    mutable std::shared_mutex m_npcsMutex;
    mutable std::mutex m_templatesMutex;
    mutable std::mutex m_spawnPointsMutex;
};

// 全局NPC管理器实例
extern std::unique_ptr<NPCManager> g_NPCManager;

} // namespace MirServer

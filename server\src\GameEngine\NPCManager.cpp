// NPCManager.cpp - NPC管理器实现
#include "NPCManager.h"
#include "Environment.h"
#include "../BaseObject/QuestNPC.h"
#include "../Common/Logger.h"
#include "../Common/Utils.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <shared_mutex>

namespace MirServer {

// 全局实例
std::unique_ptr<NPCManager> g_NPCManager = nullptr;

NPCManager::NPCManager() {
    m_initialized = false;
    m_lastRunTime = GetCurrentTime();
    m_lastRespawnCheckTime = GetCurrentTime();
}

NPCManager::~NPCManager() {
    Finalize();
}

bool NPCManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    // 加载NPC模板
    if (!LoadNPCTemplates()) {
        Logger::Error("Failed to load NPC templates");
        return false;
    }

    // 加载刷新点
    if (!LoadSpawnPoints()) {
        Logger::Error("Failed to load NPC spawn points");
        return false;
    }

    // 刷新所有NPC
    SpawnAllNPCs();

    m_initialized = true;
    Logger::Info("NPCManager initialized successfully");
    return true;
}

void NPCManager::Finalize() {
    if (!m_initialized) {
        return;
    }

    // 移除所有NPC
    RemoveAllNPCs();

    // 清理数据
    ClearNPCTemplates();
    ClearSpawnPoints();

    m_initialized = false;
    Logger::Info("NPCManager finalized");
}

bool NPCManager::LoadNPCTemplates(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        Logger::Error("Cannot open NPC template file: " + filename);
        return false;
    }

    std::lock_guard<std::mutex> lock(m_templatesMutex);
    m_npcTemplates.clear();

    std::string line;
    bool isFirstLine = true;
    int lineNumber = 0;

    while (std::getline(file, line)) {
        lineNumber++;

        // 跳过标题行和注释行
        if (isFirstLine || line.empty() || line[0] == '#' || line[0] == ';') {
            isFirstLine = false;
            continue;
        }

        NPCTemplate npcTemplate;
        if (ParseNPCTemplateLine(line, npcTemplate)) {
            m_npcTemplates[npcTemplate.npcId] = npcTemplate;
        } else {
            Logger::Warning("Failed to parse NPC template line " + std::to_string(lineNumber) + ": " + line);
        }
    }

    file.close();
    Logger::Info("Loaded " + std::to_string(m_npcTemplates.size()) + " NPC templates");
    return true;
}

bool NPCManager::ParseNPCTemplateLine(const std::string& line, NPCTemplate& npcTemplate) {
    std::vector<std::string> tokens;
    std::stringstream ss(line);
    std::string token;

    while (std::getline(ss, token, '\t')) {
        tokens.push_back(token);
    }

    if (tokens.size() < 8) {
        return false;
    }

    try {
        npcTemplate.npcId = static_cast<WORD>(std::stoi(tokens[0]));
        npcTemplate.npcName = tokens[1];
        npcTemplate.npcType = static_cast<NPCType>(std::stoi(tokens[2]));
        npcTemplate.appr = static_cast<WORD>(std::stoi(tokens[3]));
        npcTemplate.mapName = tokens[4];
        npcTemplate.position.x = std::stoi(tokens[5]);
        npcTemplate.position.y = std::stoi(tokens[6]);
        npcTemplate.direction = static_cast<DirectionType>(std::stoi(tokens[7]));

        if (tokens.size() > 8) {
            npcTemplate.scriptFile = tokens[8];
        }

        if (tokens.size() > 9) {
            npcTemplate.isActive = (std::stoi(tokens[9]) != 0);
        }

        return true;
    } catch (const std::exception& e) {
        Logger::Error("Error parsing NPC template: " + std::string(e.what()));
        return false;
    }
}

bool NPCManager::LoadSpawnPoints(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        Logger::Warning("Cannot open NPC spawn points file: " + filename + " (using templates only)");
        return true; // 不是致命错误，可以只使用模板
    }

    std::lock_guard<std::mutex> lock(m_spawnPointsMutex);
    m_spawnPoints.clear();

    std::string line;
    bool isFirstLine = true;
    int lineNumber = 0;

    while (std::getline(file, line)) {
        lineNumber++;

        if (isFirstLine || line.empty() || line[0] == '#' || line[0] == ';') {
            isFirstLine = false;
            continue;
        }

        NPCSpawnPoint spawnPoint;
        if (ParseSpawnPointLine(line, spawnPoint)) {
            m_spawnPoints.push_back(spawnPoint);
        } else {
            Logger::Warning("Failed to parse spawn point line " + std::to_string(lineNumber) + ": " + line);
        }
    }

    file.close();
    Logger::Info("Loaded " + std::to_string(m_spawnPoints.size()) + " NPC spawn points");
    return true;
}

bool NPCManager::ParseSpawnPointLine(const std::string& line, NPCSpawnPoint& spawnPoint) {
    std::vector<std::string> tokens;
    std::stringstream ss(line);
    std::string token;

    while (std::getline(ss, token, '\t')) {
        tokens.push_back(token);
    }

    if (tokens.size() < 6) {
        return false;
    }

    try {
        spawnPoint.npcTemplateId = static_cast<WORD>(std::stoi(tokens[0]));
        spawnPoint.mapName = tokens[1];
        spawnPoint.position.x = std::stoi(tokens[2]);
        spawnPoint.position.y = std::stoi(tokens[3]);
        spawnPoint.direction = static_cast<DirectionType>(std::stoi(tokens[4]));
        spawnPoint.isActive = (std::stoi(tokens[5]) != 0);

        if (tokens.size() > 6) {
            spawnPoint.respawnTime = static_cast<DWORD>(std::stoi(tokens[6]));
        }

        return true;
    } catch (const std::exception& e) {
        Logger::Error("Error parsing spawn point: " + std::string(e.what()));
        return false;
    }
}

std::shared_ptr<NPC> NPCManager::CreateNPC(WORD npcTemplateId, const std::string& mapName, const Point& pos, DirectionType dir) {
    const NPCTemplate* npcTemplate = GetNPCTemplate(npcTemplateId);
    if (!npcTemplate) {
        Logger::Error("NPC template not found: " + std::to_string(npcTemplateId));
        return nullptr;
    }

    // 创建副本并修改位置信息
    NPCTemplate modifiedTemplate = *npcTemplate;
    modifiedTemplate.mapName = mapName;
    modifiedTemplate.position = pos;
    modifiedTemplate.direction = dir;

    return CreateNPCFromTemplate(modifiedTemplate);
}

std::shared_ptr<NPC> NPCManager::CreateNPCFromTemplate(const NPCTemplate& npcTemplate) {
    std::shared_ptr<NPC> npc = nullptr;

    // 根据NPC类型创建不同的对象
    switch (npcTemplate.npcType) {
        case NPCType::MERCHANT:
            npc = std::make_shared<Merchant>();
            break;
        case NPCType::GUARD:
            npc = std::make_shared<Guard>();
            break;
        case NPCType::QUEST:
            npc = std::make_shared<QuestNPC>();
            break;
        case NPCType::NORMAL:
        default:
            npc = std::make_shared<NPC>();
            break;
    }

    if (!npc) {
        Logger::Error("Failed to create NPC object");
        return nullptr;
    }

    // 设置NPC属性
    SetupNPCFromTemplate(npc, npcTemplate);

    // 初始化NPC
    npc->Initialize();

    // 添加到管理列表
    {
        std::unique_lock<std::shared_mutex> lock(m_npcsMutex);
        m_npcs[npc->GetObjectId()] = npc;
        m_npcsByName[npc->GetCharName()] = npc;
    }

    Logger::Debug("Created NPC: " + npc->GetCharName() + " at " + npcTemplate.mapName +
                  "(" + std::to_string(npcTemplate.position.x) + "," + std::to_string(npcTemplate.position.y) + ")");

    return npc;
}

void NPCManager::SetupNPCFromTemplate(std::shared_ptr<NPC> npc, const NPCTemplate& npcTemplate) {
    if (!npc) return;

    // 基本属性
    npc->SetCharName(npcTemplate.npcName);
    npc->SetAppr(npcTemplate.appr);
    npc->SetCurrentPos(npcTemplate.position);
    npc->SetDirection(npcTemplate.direction);
    npc->SetMapName(npcTemplate.mapName);

    // 特殊设置
    switch (npcTemplate.npcType) {
        case NPCType::MERCHANT: {
            auto merchant = std::dynamic_pointer_cast<Merchant>(npc);
            if (merchant) {
                SetupMerchant(merchant, npcTemplate);
            }
            break;
        }
        case NPCType::GUARD: {
            auto guard = std::dynamic_pointer_cast<Guard>(npc);
            if (guard) {
                guard->SetGuardRange(npcTemplate.guardRange);
                guard->SetAttackCriminals(npcTemplate.attackCriminals);
            }
            break;
        }
        default:
            break;
    }

    // 加载脚本
    if (!npcTemplate.scriptFile.empty()) {
        LoadNPCScript(npc, npcTemplate.scriptFile);
    }
}

void NPCManager::SpawnAllNPCs() {
    // 从模板创建NPC
    {
        std::lock_guard<std::mutex> lock(m_templatesMutex);
        for (const auto& pair : m_npcTemplates) {
            const NPCTemplate& npcTemplate = pair.second;
            if (npcTemplate.isActive) {
                CreateNPCFromTemplate(npcTemplate);
            }
        }
    }

    // 从刷新点创建NPC
    {
        std::lock_guard<std::mutex> lock(m_spawnPointsMutex);
        for (const NPCSpawnPoint& spawnPoint : m_spawnPoints) {
            if (spawnPoint.isActive) {
                SpawnNPC(spawnPoint);
            }
        }
    }

    UpdateStatistics();
    Logger::Info("Spawned " + std::to_string(m_statistics.totalNPCs) + " NPCs");
}

bool NPCManager::SpawnNPC(const NPCSpawnPoint& spawnPoint) {
    auto npc = CreateNPC(spawnPoint.npcTemplateId, spawnPoint.mapName, spawnPoint.position, spawnPoint.direction);
    return (npc != nullptr);
}

void NPCManager::Run() {
    DWORD currentTime = GetCurrentTime();

    // 处理NPC逻辑
    if (currentTime - m_lastRunTime >= 1000) { // 每秒处理一次
        ProcessNPCs();
        m_lastRunTime = currentTime;
    }

    // 检查重生
    if (currentTime - m_lastRespawnCheckTime >= m_respawnCheckInterval) {
        CheckRespawns();
        m_lastRespawnCheckTime = currentTime;
    }
}

void NPCManager::ProcessNPCs() {
    std::shared_lock<std::shared_mutex> lock(m_npcsMutex);

    for (auto& pair : m_npcs) {
        auto npc = pair.second;
        if (npc && npc->IsAlive()) {
            npc->Run();
        }
    }
}

void NPCManager::CheckRespawns() {
    // TODO: 实现NPC重生检查
    // 检查死亡的NPC是否需要重生
}

bool NPCManager::AddNPCTemplate(const NPCTemplate& npcTemplate) {
    std::lock_guard<std::mutex> lock(m_templatesMutex);
    m_npcTemplates[npcTemplate.npcId] = npcTemplate;
    return true;
}

const NPCTemplate* NPCManager::GetNPCTemplate(WORD npcId) const {
    std::lock_guard<std::mutex> lock(m_templatesMutex);
    auto it = m_npcTemplates.find(npcId);
    return (it != m_npcTemplates.end()) ? &it->second : nullptr;
}

void NPCManager::ClearNPCTemplates() {
    std::lock_guard<std::mutex> lock(m_templatesMutex);
    m_npcTemplates.clear();
}

bool NPCManager::AddSpawnPoint(const NPCSpawnPoint& spawnPoint) {
    std::lock_guard<std::mutex> lock(m_spawnPointsMutex);
    m_spawnPoints.push_back(spawnPoint);
    return true;
}

void NPCManager::ClearSpawnPoints() {
    std::lock_guard<std::mutex> lock(m_spawnPointsMutex);
    m_spawnPoints.clear();
}

std::shared_ptr<NPC> NPCManager::FindNPC(uint32_t objectId) const {
    std::shared_lock<std::shared_mutex> lock(m_npcsMutex);
    auto it = m_npcs.find(objectId);
    return (it != m_npcs.end()) ? it->second : nullptr;
}

std::shared_ptr<NPC> NPCManager::FindNPCByName(const std::string& npcName) const {
    std::shared_lock<std::shared_mutex> lock(m_npcsMutex);
    auto it = m_npcsByName.find(npcName);
    return (it != m_npcsByName.end()) ? it->second : nullptr;
}

std::vector<std::shared_ptr<NPC>> NPCManager::GetNPCsInMap(const std::string& mapName) const {
    std::vector<std::shared_ptr<NPC>> result;
    std::shared_lock<std::shared_mutex> lock(m_npcsMutex);

    for (const auto& pair : m_npcs) {
        auto npc = pair.second;
        if (npc && npc->GetMapName() == mapName) {
            result.push_back(npc);
        }
    }

    return result;
}

std::vector<std::shared_ptr<NPC>> NPCManager::GetNPCsInRange(const std::string& mapName, const Point& center, int range) const {
    std::vector<std::shared_ptr<NPC>> result;
    std::shared_lock<std::shared_mutex> lock(m_npcsMutex);

    for (const auto& pair : m_npcs) {
        auto npc = pair.second;
        if (npc && npc->GetMapName() == mapName) {
            Point npcPos = npc->GetCurrentPos();
            double distance = GetDistance(center, npcPos);
            if (distance <= range) {
                result.push_back(npc);
            }
        }
    }

    return result;
}

bool NPCManager::RemoveNPC(uint32_t objectId) {
    std::unique_lock<std::shared_mutex> lock(m_npcsMutex);

    auto it = m_npcs.find(objectId);
    if (it != m_npcs.end()) {
        auto npc = it->second;

        // 从名称映射中移除
        auto nameIt = m_npcsByName.find(npc->GetCharName());
        if (nameIt != m_npcsByName.end()) {
            m_npcsByName.erase(nameIt);
        }

        // 清理NPC
        npc->Finalize();

        // 从主映射中移除
        m_npcs.erase(it);

        return true;
    }

    return false;
}

bool NPCManager::RemoveNPCByName(const std::string& npcName) {
    std::shared_lock<std::shared_mutex> readLock(m_npcsMutex);
    auto it = m_npcsByName.find(npcName);
    if (it != m_npcsByName.end()) {
        uint32_t objectId = it->second->GetObjectId();
        readLock.unlock();
        return RemoveNPC(objectId);
    }
    return false;
}

void NPCManager::RemoveAllNPCs() {
    std::unique_lock<std::shared_mutex> lock(m_npcsMutex);

    // 清理所有NPC
    for (auto& pair : m_npcs) {
        if (pair.second) {
            pair.second->Finalize();
        }
    }

    m_npcs.clear();
    m_npcsByName.clear();
}

bool NPCManager::SetupMerchant(std::shared_ptr<Merchant> merchant, const NPCTemplate& npcTemplate) {
    if (!merchant) return false;

    // 设置商人属性
    merchant->SetBuyRate(npcTemplate.buyRate);
    merchant->SetSellRate(npcTemplate.sellRate);

    // 添加商店物品
    for (WORD itemIndex : npcTemplate.shopItems) {
        merchant->AddShopItem(itemIndex);
    }

    return true;
}

bool NPCManager::AddShopItem(WORD npcId, WORD itemIndex, int price) {
    auto npc = FindNPC(npcId);
    if (!npc) return false;

    auto merchant = std::dynamic_pointer_cast<Merchant>(npc);
    if (!merchant) return false;

    return merchant->AddShopItem(itemIndex, price);
}

bool NPCManager::RemoveShopItem(WORD npcId, WORD itemIndex) {
    auto npc = FindNPC(npcId);
    if (!npc) return false;

    auto merchant = std::dynamic_pointer_cast<Merchant>(npc);
    if (!merchant) return false;

    merchant->RemoveShopItem(itemIndex);
    return true;
}

bool NPCManager::LoadNPCScript(std::shared_ptr<NPC> npc, const std::string& scriptFile) {
    if (!npc || scriptFile.empty()) return false;

    // TODO: 实现脚本加载
    // 这里应该加载NPC的对话脚本和行为脚本

    return true;
}

void NPCManager::ReloadAllScripts() {
    std::shared_lock<std::shared_mutex> lock(m_npcsMutex);

    for (auto& pair : m_npcs) {
        auto npc = pair.second;
        if (npc) {
            // TODO: 重新加载NPC脚本
        }
    }
}

void NPCManager::UpdateStatistics() {
    std::shared_lock<std::shared_mutex> lock(m_npcsMutex);

    m_statistics.totalNPCs = static_cast<int>(m_npcs.size());
    m_statistics.activeNPCs = 0;
    m_statistics.merchants = 0;
    m_statistics.guards = 0;
    m_statistics.questNPCs = 0;

    for (const auto& pair : m_npcs) {
        auto npc = pair.second;
        if (npc) {
            if (npc->IsAlive()) {
                m_statistics.activeNPCs++;
            }

            // 统计不同类型的NPC
            if (std::dynamic_pointer_cast<Merchant>(npc)) {
                m_statistics.merchants++;
            } else if (std::dynamic_pointer_cast<Guard>(npc)) {
                m_statistics.guards++;
            } else if (std::dynamic_pointer_cast<QuestNPC>(npc)) {
                m_statistics.questNPCs++;
            }
        }
    }

    m_statistics.lastUpdateTime = GetCurrentTime();
}

} // namespace MirServer

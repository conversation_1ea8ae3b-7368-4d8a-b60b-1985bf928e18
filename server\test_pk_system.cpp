#include "src/GameEngine/PKManager.h"
#include "src/GameEngine/GuildManager.h"
#include "src/BaseObject/PlayObject.h"
#include "src/Common/Logger.h"
#include <iostream>
#include <memory>

using namespace MirServer;

int main() {
    std::cout << "=== PK系统测试 ===" << std::endl;

    try {
        // 初始化日志系统
        // Logger::Initialize("pk_test.log");

        // 初始化PK管理器
        auto& pkManager = PKManager::GetInstance();
        pkManager.Initialize();

        std::cout << "PK管理器初始化成功" << std::endl;

        // 创建测试玩家
        auto player1 = std::make_shared<PlayObject>();
        auto player2 = std::make_shared<PlayObject>();

        player1->SetCharName("TestPlayer1");
        player2->SetCharName("TestPlayer2");

        std::cout << "创建测试玩家: " << player1->GetCharName() << ", " << player2->GetCharName() << std::endl;

        // 测试1: PK值设置和获取
        std::cout << "\n--- 测试1: PK值管理 ---" << std::endl;
        pkManager.SetPKValue(player1.get(), 150);
        MirServer::DWORD pkValue = pkManager.GetPKValue(player1.get());
        std::cout << player1->GetCharName() << " PK值: " << pkValue << std::endl;

        // 测试2: PK状态判断
        std::cout << "\n--- 测试2: PK状态判断 ---" << std::endl;
        bool isYellow = pkManager.IsYellowName(player1.get());
        bool isRed = pkManager.IsRedName(player1.get());
        std::cout << player1->GetCharName() << " 黄名: " << (isYellow ? "是" : "否") << std::endl;
        std::cout << player1->GetCharName() << " 红名: " << (isRed ? "是" : "否") << std::endl;

        // 测试3: 攻击模式
        std::cout << "\n--- 测试3: 攻击模式 ---" << std::endl;
        pkManager.SetAttackMode(player1.get(), PKMode::ALL);
        PKMode mode = pkManager.GetAttackMode(player1.get());
        std::cout << player1->GetCharName() << " 攻击模式: " << static_cast<int>(mode) << " (6=全体模式)" << std::endl;

        // 测试4: 攻击判断
        std::cout << "\n--- 测试4: 攻击判断 ---" << std::endl;
        bool canAttack = pkManager.CanAttack(player1.get(), player2.get());
        std::cout << player1->GetCharName() << " 可以攻击 " << player2->GetCharName() << ": " << (canAttack ? "是" : "否") << std::endl;

        // 测试5: 行会战
        std::cout << "\n--- 测试5: 行会战系统 ---" << std::endl;
        bool warStarted = pkManager.StartGuildWar("TestGuild1", "TestGuild2", 30);
        std::cout << "行会战开始: " << (warStarted ? "成功" : "失败") << std::endl;

        bool isWar = pkManager.IsGuildWar("TestGuild1", "TestGuild2");
        std::cout << "行会战状态: " << (isWar ? "进行中" : "未进行") << std::endl;

        auto activeWars = pkManager.GetActiveGuildWars();
        std::cout << "当前活跃行会战数量: " << activeWars.size() << std::endl;

        bool warEnded = pkManager.EndGuildWar("TestGuild1", "TestGuild2");
        std::cout << "行会战结束: " << (warEnded ? "成功" : "失败") << std::endl;

        // 测试6: PK事件
        std::cout << "\n--- 测试6: PK事件处理 ---" << std::endl;
        pkManager.OnPlayerLogin(player1.get());
        pkManager.OnPlayerLogin(player2.get());
        std::cout << "玩家登录事件处理完成" << std::endl;

        MirServer::DWORD oldPKValue = pkManager.GetPKValue(player1.get());
        pkManager.OnPlayerKill(player1.get(), player2.get());
        MirServer::DWORD newPKValue = pkManager.GetPKValue(player1.get());
        std::cout << "杀人事件: " << player1->GetCharName() << " PK值从 " << oldPKValue << " 增加到 " << newPKValue << std::endl;

        // 测试7: 配置管理
        std::cout << "\n--- 测试7: 配置管理 ---" << std::endl;
        pkManager.SetKillAddPKValue(200);
        pkManager.SetRedNamePKValue(300);
        std::cout << "杀人增加PK值: " << pkManager.GetKillAddPKValue() << std::endl;
        std::cout << "红名PK值阈值: " << pkManager.GetRedNamePKValue() << std::endl;

        // 清理
        pkManager.OnPlayerLogout(player1.get());
        pkManager.OnPlayerLogout(player2.get());
        pkManager.Finalize();

        std::cout << "\n=== PK系统测试完成 ===" << std::endl;
        std::cout << "所有测试通过!" << std::endl;

        // Logger::Finalize();
        return 0;

    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}

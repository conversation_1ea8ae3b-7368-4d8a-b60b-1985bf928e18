// 测试数据生成器
#include "TestDataGenerator.h"
#include "../Common/Logger.h"
#include <fstream>
#include <random>
#include <sstream>

namespace MirServer {

TestDataGenerator::TestDataGenerator() {
    // 初始化随机数生成器
    std::random_device rd;
    m_randomEngine.seed(rd());
}

bool TestDataGenerator::GenerateTestData(const std::string& dataPath) {
    Logger::Info("Generating test data in: " + dataPath);
    
    try {
        // 创建必要的目录
        CreateDirectories(dataPath);
        
        // 生成各种测试数据文件
        bool success = true;
        success &= GenerateItemData(dataPath + "/StdItems.txt");
        success &= GenerateMonsterData(dataPath + "/Monster.txt");
        success &= GenerateMapData(dataPath + "/MapInfo.txt");
        success &= GenerateQuestData(dataPath + "/QuestInfo.txt");
        success &= GenerateNPCData(dataPath + "/Merchant.txt");
        success &= GenerateShopData(dataPath + "/Shop.txt");
        success &= GenerateMonsterDropData(dataPath + "/MonsterDrops.txt");
        
        if (success) {
            Logger::Info("Test data generation completed successfully");
        } else {
            Logger::Error("Some test data files failed to generate");
        }
        
        return success;
        
    } catch (const std::exception& e) {
        Logger::Error("Exception in GenerateTestData: " + std::string(e.what()));
        return false;
    }
}

bool TestDataGenerator::GenerateItemData(const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        Logger::Error("Failed to create item data file: " + filename);
        return false;
    }
    
    // 写入文件头
    file << ";物品数据文件 - 测试数据\n";
    file << ";格式: ID 名称 类型 外观 重量 持久 价格 等级 职业 性别 攻击力 魔法力 道术 防御力 魔防 敏捷 准确 幸运\n";
    file << "\n";
    
    // 生成武器
    file << "1\t木剑\t5\t1\t12\t0\t0\t0\t0\t0\t2000\t0\t0\t1\t3\t0\t0\t0\t100\n";
    file << "2\t短剑\t5\t2\t20\t0\t0\t0\t0\t0\t3000\t0\t0\t2\t5\t0\t0\t0\t200\n";
    file << "3\t铁剑\t5\t3\t35\t0\t0\t0\t0\t0\t5000\t0\t0\t5\t10\t0\t0\t0\t500\n";
    file << "4\t银剑\t5\t4\t50\t0\t0\t0\t0\t0\t8000\t0\t0\t8\t15\t0\t0\t0\t800\n";
    file << "5\t金剑\t5\t5\t80\t0\t0\t0\t0\t0\t15000\t0\t0\t12\t25\t0\t0\t0\t1500\n";
    
    // 生成防具
    file << "10\t布衣\t10\t1\t5\t0\t0\t0\t0\t0\t2000\t2\t0\t0\t0\t0\t0\t0\t50\n";
    file << "11\t皮甲\t10\t2\t15\t0\t0\t0\t0\t0\t4000\t5\t0\t0\t0\t0\t0\t0\t200\n";
    file << "12\t铁甲\t10\t3\t40\t0\t0\t0\t0\t0\t8000\t10\t0\t0\t0\t0\t0\t0\t800\n";
    file << "13\t银甲\t10\t4\t60\t0\t0\t0\t0\t0\t15000\t15\t0\t0\t0\t0\t0\t0\t1500\n";
    file << "14\t金甲\t10\t5\t100\t0\t0\t0\t0\t0\t30000\t25\t0\t0\t0\t0\t0\t0\t3000\n";
    
    // 生成消耗品
    file << "20\t金币\t40\t1\t1\t0\t0\t0\t0\t0\t1\t0\t0\t0\t0\t0\t0\t0\t1\n";
    file << "21\t药水(小)\t3\t1\t1\t0\t0\t0\t0\t0\t1\t0\t0\t0\t0\t0\t30\t0\t5\n";
    file << "22\t药水(中)\t3\t2\t1\t0\t0\t0\t0\t0\t5\t0\t0\t0\t0\t0\t60\t0\t20\n";
    file << "23\t药水(大)\t3\t3\t1\t0\t0\t0\t0\t0\t20\t0\t0\t0\t0\t0\t120\t0\t80\n";
    file << "24\t魔法药(小)\t3\t4\t1\t0\t0\t0\t0\t0\t2\t0\t0\t0\t0\t0\t0\t30\t8\n";
    file << "25\t魔法药(中)\t3\t5\t1\t0\t0\t0\t0\t0\t8\t0\t0\t0\t0\t0\t0\t60\t30\n";
    file << "26\t魔法药(大)\t3\t6\t1\t0\t0\t0\t0\t0\t30\t0\t0\t0\t0\t0\t0\t120\t120\n";
    
    // 生成首饰
    file << "30\t铜戒指\t26\t1\t1\t0\t0\t0\t0\t0\t500\t0\t0\t0\t1\t0\t0\t0\t50\n";
    file << "31\t银戒指\t26\t2\t1\t0\t0\t0\t0\t0\t2000\t0\t0\t0\t2\t0\t0\t0\t200\n";
    file << "32\t金戒指\t26\t3\t1\t0\t0\t0\t0\t0\t8000\t0\t0\t0\t3\t0\t0\t0\t800\n";
    file << "33\t铜项链\t19\t1\t1\t0\t0\t0\t0\t0\t800\t0\t0\t0\t0\t1\t0\t0\t80\n";
    file << "34\t银项链\t19\t2\t1\t0\t0\t0\t0\t0\t3000\t0\t0\t0\t0\t2\t0\t0\t300\n";
    file << "35\t金项链\t19\t3\t1\t0\t0\t0\t0\t0\t12000\t0\t0\t0\t0\t3\t0\t0\t1200\n";
    
    file.close();
    Logger::Info("Generated item data: " + filename);
    return true;
}

bool TestDataGenerator::GenerateMonsterData(const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        Logger::Error("Failed to create monster data file: " + filename);
        return false;
    }
    
    // 写入文件头
    file << ";怪物数据文件 - 测试数据\n";
    file << ";格式: 名称 种族 等级 生命值 经验值 攻击力 防御力 魔防 敏捷 准确 移动速度 攻击速度\n";
    file << "\n";
    
    // 生成基础怪物
    file << "鸡\t0\t1\t15\t2\t1\t0\t0\t10\t5\t1000\t1500\n";
    file << "鹿\t0\t2\t25\t5\t2\t1\t0\t15\t8\t800\t1200\n";
    file << "稻草人\t0\t3\t40\t8\t3\t1\t0\t5\t3\t1500\t2000\n";
    file << "骷髅\t1\t5\t80\t15\t5\t2\t1\t8\t6\t1200\t1800\n";
    file << "僵尸\t1\t8\t150\t30\t8\t3\t1\t3\t4\t2000\t2500\n";
    file << "骷髅战士\t1\t12\t250\t60\t12\t5\t2\t10\t8\t1000\t1500\n";
    file << "骷髅弓箭手\t1\t15\t200\t80\t15\t3\t2\t15\t12\t800\t1000\n";
    file << "石墓尸王\t1\t25\t800\t300\t25\t10\t5\t8\t10\t1500\t2000\n";
    file << "沃玛战士\t2\t35\t1500\t800\t35\t15\t8\t12\t15\t1200\t1800\n";
    file << "沃玛卫士\t2\t40\t2000\t1200\t40\t20\t10\t15\t18\t1000\t1500\n";
    file << "祖玛雕像\t3\t45\t3000\t2000\t45\t25\t15\t10\t12\t1800\t2200\n";
    file << "祖玛卫士\t3\t50\t4000\t3000\t50\t30\t20\t18\t20\t1500\t2000\n";
    file << "赤月恶魔\t4\t60\t8000\t8000\t60\t40\t30\t25\t25\t1200\t1800\n";
    
    file.close();
    Logger::Info("Generated monster data: " + filename);
    return true;
}

bool TestDataGenerator::GenerateMapData(const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        Logger::Error("Failed to create map data file: " + filename);
        return false;
    }
    
    // 写入文件头
    file << ";地图信息文件 - 测试数据\n";
    file << ";格式: 地图代码 地图名称 地图文件 小地图 音乐 是否安全区 是否可以随机 最小等级\n";
    file << "\n";
    
    // 生成地图数据
    file << "0\t比奇城\tBicheng\t0\t1\t1\t0\t1\n";
    file << "1\t比奇省\tBichengsheng\t1\t2\t0\t1\t1\n";
    file << "2\t毒蛇山谷\tDusheshangu\t2\t3\t0\t1\t5\n";
    file << "3\t矿洞一层\tKuangdong1\t3\t4\t0\t1\t8\n";
    file << "4\t矿洞二层\tKuangdong2\t4\t4\t0\t1\t12\n";
    file << "5\t矿洞三层\tKuangdong3\t5\t4\t0\t1\t15\n";
    file << "6\t石墓一层\tShimu1\t6\t5\t0\t1\t18\n";
    file << "7\t石墓二层\tShimu2\t7\t5\t0\t1\t22\n";
    file << "8\t石墓三层\tShimu3\t8\t5\t0\t1\t25\n";
    file << "9\t沃玛寺庙\tWoma\t9\t6\t0\t1\t30\n";
    file << "10\t祖玛寺庙\tZuma\t10\t7\t0\t1\t35\n";
    file << "11\t赤月峡谷\tChiyue\t11\t8\t0\t1\t40\n";
    
    file.close();
    Logger::Info("Generated map data: " + filename);
    return true;
}

bool TestDataGenerator::GenerateQuestData(const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        Logger::Error("Failed to create quest data file: " + filename);
        return false;
    }
    
    // 写入文件头
    file << ";任务信息文件 - 测试数据\n";
    file << ";格式: 任务ID 任务名称 任务描述 最小等级 最大等级 前置任务 奖励经验 奖励金币 奖励物品\n";
    file << "\n";
    
    // 生成任务数据
    file << "1\t新手训练\t击杀5只鸡来熟悉战斗\t1\t5\t0\t10\t100\t21\n";
    file << "2\t初级猎手\t击杀10只鹿证明你的实力\t3\t10\t1\t30\t300\t22\n";
    file << "3\t稻草人清理\t清理农田里的稻草人\t5\t15\t2\t80\t800\t23\n";
    file << "4\t骷髅猎人\t消灭矿洞中的骷髅\t8\t20\t3\t200\t2000\t2\n";
    file << "5\t僵尸克星\t击败强大的僵尸\t12\t25\t4\t500\t5000\t3\n";
    file << "6\t骷髅战士挑战\t挑战骷髅战士的权威\t15\t30\t5\t1000\t10000\t11\n";
    file << "7\t弓箭手狙击\t狙击骷髅弓箭手\t18\t35\t6\t2000\t20000\t12\n";
    file << "8\t尸王讨伐\t讨伐石墓尸王\t22\t40\t7\t5000\t50000\t13\n";
    file << "9\t沃玛征服\t征服沃玛战士\t30\t45\t8\t10000\t100000\t4\n";
    file << "10\t祖玛探索\t探索祖玛寺庙的秘密\t35\t50\t9\t20000\t200000\t14\n";
    
    file.close();
    Logger::Info("Generated quest data: " + filename);
    return true;
}

bool TestDataGenerator::GenerateNPCData(const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        Logger::Error("Failed to create NPC data file: " + filename);
        return false;
    }
    
    // 写入文件头
    file << ";NPC数据文件 - 测试数据\n";
    file << ";格式: NPC名称 地图 坐标X 坐标Y 外观 功能类型 脚本文件\n";
    file << "\n";
    
    // 生成NPC数据
    file << "新手导师\t0\t330\t330\t0\t1\tnewbie_guide.txt\n";
    file << "武器商人\t0\t320\t320\t1\t2\tweapon_shop.txt\n";
    file << "防具商人\t0\t340\t320\t2\t2\tarmor_shop.txt\n";
    file << "药店老板\t0\t330\t310\t3\t2\tpotion_shop.txt\n";
    file << "仓库管理员\t0\t350\t330\t4\t3\tstorage_npc.txt\n";
    file << "修理师傅\t0\t310\t330\t5\t4\trepair_npc.txt\n";
    file << "任务发布员\t0\t330\t350\t6\t1\tquest_giver.txt\n";
    file << "传送员\t0\t360\t340\t7\t5\tteleport_npc.txt\n";
    file << "首饰商人\t0\t320\t340\t8\t2\tjewelry_shop.txt\n";
    file << "技能导师\t0\t340\t340\t9\t6\tskill_trainer.txt\n";
    
    file.close();
    Logger::Info("Generated NPC data: " + filename);
    return true;
}

bool TestDataGenerator::GenerateShopData(const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        Logger::Error("Failed to create shop data file: " + filename);
        return false;
    }
    
    // 写入文件头
    file << ";商店数据文件 - 测试数据\n";
    file << ";格式: 商店名称 物品ID 数量 价格\n";
    file << "\n";
    
    // 武器商店
    file << "武器商店\t1\t10\t2000\n";
    file << "武器商店\t2\t8\t3000\n";
    file << "武器商店\t3\t5\t5000\n";
    file << "武器商店\t4\t3\t8000\n";
    file << "武器商店\t5\t1\t15000\n";
    
    // 防具商店
    file << "防具商店\t10\t10\t2000\n";
    file << "防具商店\t11\t8\t4000\n";
    file << "防具商店\t12\t5\t8000\n";
    file << "防具商店\t13\t3\t15000\n";
    file << "防具商店\t14\t1\t30000\n";
    
    // 药店
    file << "药店\t21\t100\t5\n";
    file << "药店\t22\t50\t20\n";
    file << "药店\t23\t20\t80\n";
    file << "药店\t24\t80\t8\n";
    file << "药店\t25\t40\t30\n";
    file << "药店\t26\t15\t120\n";
    
    // 首饰商店
    file << "首饰商店\t30\t20\t500\n";
    file << "首饰商店\t31\t15\t2000\n";
    file << "首饰商店\t32\t8\t8000\n";
    file << "首饰商店\t33\t18\t800\n";
    file << "首饰商店\t34\t12\t3000\n";
    file << "首饰商店\t35\t6\t12000\n";
    
    file.close();
    Logger::Info("Generated shop data: " + filename);
    return true;
}

bool TestDataGenerator::GenerateMonsterDropData(const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        Logger::Error("Failed to create monster drop data file: " + filename);
        return false;
    }
    
    // 写入文件头
    file << ";怪物掉落数据文件 - 测试数据\n";
    file << ";格式: 怪物名称 物品ID 掉落概率 最小数量 最大数量\n";
    file << "\n";
    
    // 生成掉落数据
    file << "鸡\t20\t1000\t1\t3\n";
    file << "鸡\t21\t500\t1\t1\n";
    file << "鹿\t20\t2000\t2\t5\n";
    file << "鹿\t21\t800\t1\t2\n";
    file << "稻草人\t20\t3000\t3\t8\n";
    file << "稻草人\t22\t300\t1\t1\n";
    file << "骷髅\t20\t5000\t5\t15\n";
    file << "骷髅\t22\t500\t1\t2\n";
    file << "骷髅\t1\t50\t1\t1\n";
    file << "僵尸\t20\t8000\t8\t25\n";
    file << "僵尸\t23\t200\t1\t1\n";
    file << "僵尸\t2\t30\t1\t1\n";
    file << "骷髅战士\t20\t15000\t15\t50\n";
    file << "骷髅战士\t3\t20\t1\t1\n";
    file << "骷髅战士\t10\t15\t1\t1\n";
    file << "石墓尸王\t20\t50000\t50\t200\n";
    file << "石墓尸王\t4\t10\t1\t1\n";
    file << "石墓尸王\t11\t8\t1\t1\n";
    file << "石墓尸王\t30\t5\t1\t1\n";
    
    file.close();
    Logger::Info("Generated monster drop data: " + filename);
    return true;
}

void TestDataGenerator::CreateDirectories(const std::string& path) {
    // 这里应该创建必要的目录，简化实现
    Logger::Info("Creating directories for: " + path);
}

} // namespace MirServer

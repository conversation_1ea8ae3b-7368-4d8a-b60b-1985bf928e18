#include "GuildManager.h"
#include "../BaseObject/PlayObject.h"
#include "../Common/Logger.h"
#include <iostream>
#include <memory>

using namespace MirServer;

int main() {
    std::cout << "=== Simple Guild System Test ===" << std::endl;
    
    try {
        // 初始化日志系统
        std::cout << "1. Initializing logger..." << std::endl;
        Logger::SetLogFile("simple_guild_test.log");
        Logger::SetLogLevel(LogLevel::LOG_DEBUG);
        std::cout << "   Logger initialized." << std::endl;

        // 初始化行会管理器
        std::cout << "2. Initializing guild manager..." << std::endl;
        auto& guildManager = GuildManager::GetInstance();
        if (!guildManager.Initialize()) {
            std::cout << "   ERROR: Guild manager initialization failed" << std::endl;
            return 1;
        }
        std::cout << "   Guild manager initialized successfully" << std::endl;

        // 创建测试玩家
        std::cout << "3. Creating test players..." << std::endl;
        auto player1 = std::make_unique<PlayObject>();
        auto player2 = std::make_unique<PlayObject>();
        
        // 设置玩家基本信息
        auto& humData1 = const_cast<HumDataInfo&>(player1->GetHumDataInfo());
        humData1.charName = "TestPlayer1";
        humData1.level = 50;
        humData1.job = JobType::WARRIOR;
        
        auto& humData2 = const_cast<HumDataInfo&>(player2->GetHumDataInfo());
        humData2.charName = "TestPlayer2";
        humData2.level = 45;
        humData2.job = JobType::WIZARD;
        
        std::cout << "   Test players created: " << player1->GetCharName() 
                  << ", " << player2->GetCharName() << std::endl;

        // 测试1: 创建行会
        std::cout << "4. Testing guild creation..." << std::endl;
        bool result = guildManager.CreateGuild("TestGuild", player1.get());
        if (result) {
            std::cout << "   ✓ Guild created successfully: TestGuild" << std::endl;
            std::cout << "   ✓ Chief: " << player1->GetCharName() << std::endl;
            std::cout << "   ✓ Player guild: " << player1->GetGuildName() << std::endl;
        } else {
            std::cout << "   ✗ Guild creation failed" << std::endl;
            return 1;
        }

        // 获取行会对象
        Guild* guild = guildManager.FindGuild("TestGuild");
        if (!guild) {
            std::cout << "   ✗ Cannot find created guild" << std::endl;
            return 1;
        }
        std::cout << "   ✓ Guild object found" << std::endl;

        // 测试2: 添加成员
        std::cout << "5. Testing member addition..." << std::endl;
        bool addResult = guild->AddMember(player2.get(), GuildRank::MEMBER);
        if (addResult) {
            std::cout << "   ✓ Member added successfully" << std::endl;
            std::cout << "   ✓ Guild member count: " << guild->GetMemberCount() << std::endl;
            std::cout << "   ✓ Player2 guild: " << player2->GetGuildName() << std::endl;
        } else {
            std::cout << "   ✗ Member addition failed" << std::endl;
        }

        // 测试3: 职位管理
        std::cout << "6. Testing rank management..." << std::endl;
        bool rankResult = guild->UpdateMemberRank("TestPlayer2", GuildRank::VICE_CHIEF, "ViceChief");
        if (rankResult) {
            std::cout << "   ✓ Rank updated successfully" << std::endl;
            std::cout << "   ✓ Player2 new rank: " << guild->GetRankName("TestPlayer2") << std::endl;
        } else {
            std::cout << "   ✗ Rank update failed" << std::endl;
        }

        // 测试4: 行会公告
        std::cout << "7. Testing guild notices..." << std::endl;
        guild->AddNotice("Welcome to our guild!");
        guild->AddNotice("Guild event tonight at 8 PM");
        
        const auto& notices = guild->GetNotices();
        std::cout << "   ✓ Notice count: " << notices.size() << std::endl;
        for (size_t i = 0; i < notices.size(); ++i) {
            std::cout << "     " << (i + 1) << ". " << notices[i] << std::endl;
        }

        // 测试5: 行会属性
        std::cout << "8. Testing guild properties..." << std::endl;
        guild->SetBuildPoint(1000);
        guild->SetAurae(500);
        guild->SetStability(800);
        guild->SetFlourishing(600);
        
        std::cout << "   ✓ Build Point: " << guild->GetBuildPoint() << std::endl;
        std::cout << "   ✓ Aurae: " << guild->GetAurae() << std::endl;
        std::cout << "   ✓ Stability: " << guild->GetStability() << std::endl;
        std::cout << "   ✓ Flourishing: " << guild->GetFlourishing() << std::endl;

        // 测试6: 成员列表
        std::cout << "9. Testing member list..." << std::endl;
        const auto& members = guild->GetMembers();
        std::cout << "   ✓ Guild members:" << std::endl;
        for (const auto& member : members) {
            std::cout << "     - " << member.playerName 
                      << " (" << member.rankName << ")" 
                      << " [" << (member.isOnline ? "Online" : "Offline") << "]" << std::endl;
        }

        // 测试7: 成员删除
        std::cout << "10. Testing member removal..." << std::endl;
        bool removeResult = guild->RemoveMember("TestPlayer2");
        if (removeResult) {
            std::cout << "    ✓ Member removed successfully" << std::endl;
            std::cout << "    ✓ Guild member count: " << guild->GetMemberCount() << std::endl;
            std::cout << "    ✓ Player2 guild: " << (player2->GetGuildName().empty() ? "Cleared" : player2->GetGuildName()) << std::endl;
        } else {
            std::cout << "    ✗ Member removal failed" << std::endl;
        }

        // 测试8: 运行时处理
        std::cout << "11. Testing runtime processing..." << std::endl;
        guild->Run();
        guildManager.Run();
        std::cout << "    ✓ Runtime processing completed" << std::endl;

        // 清理
        std::cout << "12. Cleaning up..." << std::endl;
        guildManager.Finalize();
        std::cout << "    ✓ Cleanup completed" << std::endl;

        std::cout << "\n=== All Tests Passed Successfully! ===" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception occurred: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown exception occurred" << std::endl;
        return 1;
    }
}

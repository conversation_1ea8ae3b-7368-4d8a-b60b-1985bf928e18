#pragma once

#include <string>
#include <random>

namespace MirServer {

// 测试数据生成器类
class TestDataGenerator {
public:
    TestDataGenerator();
    ~TestDataGenerator() = default;

    // 生成所有测试数据
    bool GenerateTestData(const std::string& dataPath);

    // 生成特定类型的测试数据
    bool GenerateItemData(const std::string& filename);
    bool GenerateMonsterData(const std::string& filename);
    bool GenerateMapData(const std::string& filename);
    bool GenerateQuestData(const std::string& filename);
    bool GenerateNPCData(const std::string& filename);
    bool GenerateShopData(const std::string& filename);
    bool GenerateMonsterDropData(const std::string& filename);

private:
    // 创建目录
    void CreateDirectories(const std::string& path);

    // 随机数生成器
    std::mt19937 m_randomEngine;
};

} // namespace MirServer

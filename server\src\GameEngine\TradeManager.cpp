#include "TradeManager.h"
#include "../BaseObject/PlayObject.h"
#include "../Common/Logger.h"
#include "../Protocol/PacketTypes.h"
#include <algorithm>
#include <sstream>

namespace MirServer {

// 全局实例
std::unique_ptr<TradeManager> g_TradeManager = nullptr;

TradeManager::TradeManager() {
    Logger::Info("TradeManager created");
}

TradeManager::~TradeManager() {
    Finalize();
    Logger::Info("TradeManager destroyed");
}

bool TradeManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    Logger::Info("Initializing TradeManager...");

    // 初始化统计信息
    m_statistics = {};
    m_lastProcessTime = GetCurrentTime();
    m_lastTimeoutCheckTime = GetCurrentTime();

    m_initialized = true;
    Logger::Info("TradeManager initialized successfully");

    return true;
}

void TradeManager::Finalize() {
    if (!m_initialized) {
        return;
    }

    Logger::Info("Finalizing TradeManager...");

    // 取消所有进行中的交易
    {
        std::unique_lock<std::shared_mutex> lock(m_tradesMutex);
        for (auto& pair : m_trades) {
            if (pair.second->state == TradeState::TRADING ||
                pair.second->state == TradeState::LOCKED) {
                pair.second->state = TradeState::CANCELLED;
                NotifyTradeResult(pair.second.get(), false);
            }
        }
        m_trades.clear();
        m_playerToTrade.clear();
    }

    m_initialized = false;
    Logger::Info("TradeManager finalized");
}

bool TradeManager::RequestTrade(PlayObject* requester, PlayObject* target) {
    if (!requester || !target || !m_initialized) {
        return false;
    }

    // 验证玩家状态
    if (!CanStartTrade(requester, target)) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_tradesMutex);

    // 检查是否已经在交易中
    if (IsInTrade(requester) || IsInTrade(target)) {
        requester->SendMessage("你或对方正在交易中！");
        return false;
    }

    // 创建交易数据
    std::string tradeId = GenerateTradeId(requester->GetCharName(), target->GetCharName());
    auto trade = std::make_unique<TradeData>();

    trade->player1Name = requester->GetCharName();
    trade->player2Name = target->GetCharName();
    trade->player1 = requester;
    trade->player2 = target;
    trade->state = TradeState::REQUESTING;
    trade->startTime = GetCurrentTime();
    trade->lastUpdateTime = trade->startTime;

    // 添加到映射
    m_trades[tradeId] = std::move(trade);
    m_playerToTrade[requester->GetCharName()] = tradeId;
    m_playerToTrade[target->GetCharName()] = tradeId;

    // 通知双方
    requester->SendMessage("交易请求已发送给 " + target->GetCharName());
    target->SendDefMessage(Protocol::SM_DEALMENU, 0, 0, 0, 0);

    Logger::Info("Trade requested: " + requester->GetCharName() + " -> " + target->GetCharName());
    return true;
}

bool TradeManager::AcceptTrade(PlayObject* player, const std::string& requesterName) {
    if (!player || !m_initialized) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_tradesMutex);

    TradeData* trade = GetTradeDataByPlayer(player);
    if (!trade || trade->state != TradeState::REQUESTING) {
        return false;
    }

    // 验证请求者
    if (trade->player1Name != requesterName) {
        return false;
    }

    // 开始交易
    trade->state = TradeState::TRADING;
    trade->lastUpdateTime = GetCurrentTime();

    // 通知双方
    NotifyTradeUpdate(trade);

    Logger::Info("Trade accepted: " + trade->player1Name + " <-> " + trade->player2Name);
    return true;
}

bool TradeManager::RejectTrade(PlayObject* player, const std::string& requesterName) {
    if (!player || !m_initialized) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_tradesMutex);

    TradeData* trade = GetTradeDataByPlayer(player);
    if (!trade || trade->state != TradeState::REQUESTING) {
        return false;
    }

    // 验证请求者
    if (trade->player1Name != requesterName) {
        return false;
    }

    // 取消交易
    trade->state = TradeState::CANCELLED;
    NotifyTradeResult(trade, false);

    std::string tradeId = m_playerToTrade[player->GetCharName()];
    CleanupTrade(tradeId);

    Logger::Info("Trade rejected: " + trade->player1Name + " -> " + trade->player2Name);
    return true;
}

bool TradeManager::CancelTrade(PlayObject* player) {
    if (!player || !m_initialized) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_tradesMutex);

    TradeData* trade = GetTradeDataByPlayer(player);
    if (!trade) {
        return false;
    }

    // 取消交易
    trade->state = TradeState::CANCELLED;
    NotifyTradeResult(trade, false);

    std::string tradeId = m_playerToTrade[player->GetCharName()];
    CleanupTrade(tradeId);

    Logger::Info("Trade cancelled by: " + player->GetCharName());
    return true;
}

bool TradeManager::AddTradeItem(PlayObject* player, const UserItem& item) {
    if (!player || !m_initialized) {
        return false;
    }

    if (!CanAddTradeItem(player, item)) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_tradesMutex);

    TradeData* trade = GetTradeDataByPlayer(player);
    if (!trade || trade->state != TradeState::TRADING) {
        return false;
    }

    // 添加物品到对应玩家的交易列表
    if (trade->player1 == player) {
        if (trade->player1Items.size() >= static_cast<size_t>(m_maxTradeItems)) {
            player->SendMessage("交易物品已满！");
            return false;
        }
        trade->player1Items.push_back(item);
        trade->player1Locked = false; // 重置锁定状态
    } else if (trade->player2 == player) {
        if (trade->player2Items.size() >= static_cast<size_t>(m_maxTradeItems)) {
            player->SendMessage("交易物品已满！");
            return false;
        }
        trade->player2Items.push_back(item);
        trade->player2Locked = false; // 重置锁定状态
    } else {
        return false;
    }

    trade->lastUpdateTime = GetCurrentTime();
    NotifyTradeUpdate(trade);

    Logger::Debug("Item added to trade by: " + player->GetCharName());
    return true;
}

bool TradeManager::RemoveTradeItem(PlayObject* player, WORD makeIndex) {
    if (!player || !m_initialized) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_tradesMutex);

    TradeData* trade = GetTradeDataByPlayer(player);
    if (!trade || trade->state != TradeState::TRADING) {
        return false;
    }

    bool removed = false;

    // 从对应玩家的交易列表中移除物品
    if (trade->player1 == player) {
        auto it = std::find_if(trade->player1Items.begin(), trade->player1Items.end(),
            [makeIndex](const UserItem& item) {
                return item.makeIndex == makeIndex;
            });
        if (it != trade->player1Items.end()) {
            trade->player1Items.erase(it);
            trade->player1Locked = false; // 重置锁定状态
            removed = true;
        }
    } else if (trade->player2 == player) {
        auto it = std::find_if(trade->player2Items.begin(), trade->player2Items.end(),
            [makeIndex](const UserItem& item) {
                return item.makeIndex == makeIndex;
            });
        if (it != trade->player2Items.end()) {
            trade->player2Items.erase(it);
            trade->player2Locked = false; // 重置锁定状态
            removed = true;
        }
    }

    if (removed) {
        trade->lastUpdateTime = GetCurrentTime();
        NotifyTradeUpdate(trade);
        Logger::Debug("Item removed from trade by: " + player->GetCharName());
    }

    return removed;
}

bool TradeManager::SetTradeGold(PlayObject* player, DWORD amount) {
    if (!player || !m_initialized) {
        return false;
    }

    // 检查玩家是否有足够的金币
    if (player->GetGold() < amount) {
        player->SendMessage("金币不足！");
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_tradesMutex);

    TradeData* trade = GetTradeDataByPlayer(player);
    if (!trade || trade->state != TradeState::TRADING) {
        return false;
    }

    // 设置金币数量
    if (trade->player1 == player) {
        trade->player1Gold = amount;
        trade->player1Locked = false; // 重置锁定状态
    } else if (trade->player2 == player) {
        trade->player2Gold = amount;
        trade->player2Locked = false; // 重置锁定状态
    } else {
        return false;
    }

    trade->lastUpdateTime = GetCurrentTime();
    NotifyTradeUpdate(trade);

    Logger::Debug("Gold set in trade by: " + player->GetCharName() +
                  ", amount: " + std::to_string(amount));
    return true;
}

bool TradeManager::LockTrade(PlayObject* player) {
    if (!player || !m_initialized) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_tradesMutex);

    TradeData* trade = GetTradeDataByPlayer(player);
    if (!trade || trade->state != TradeState::TRADING) {
        return false;
    }

    // 锁定交易
    if (trade->player1 == player) {
        trade->player1Locked = true;
    } else if (trade->player2 == player) {
        trade->player2Locked = true;
    } else {
        return false;
    }

    // 检查是否双方都已锁定
    if (trade->player1Locked && trade->player2Locked) {
        trade->state = TradeState::LOCKED;
    }

    trade->lastUpdateTime = GetCurrentTime();
    NotifyTradeUpdate(trade);

    Logger::Debug("Trade locked by: " + player->GetCharName());
    return true;
}

bool TradeManager::CompleteTrade(PlayObject* player) {
    if (!player || !m_initialized) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_tradesMutex);

    TradeData* trade = GetTradeDataByPlayer(player);
    if (!trade || trade->state != TradeState::LOCKED) {
        return false;
    }

    // 执行交易
    bool success = ExecuteTradeExchange(trade);

    trade->state = success ? TradeState::COMPLETED : TradeState::CANCELLED;
    NotifyTradeResult(trade, success);

    // 更新统计
    if (success) {
        m_statistics.completedTrades++;
        m_statistics.totalTradeValue += trade->player1Gold + trade->player2Gold;
    } else {
        m_statistics.cancelledTrades++;
    }

    std::string tradeId = m_playerToTrade[player->GetCharName()];
    CleanupTrade(tradeId);

    Logger::Info(std::string("Trade ") + (success ? "completed" : "failed") + " by: " + player->GetCharName());
    return success;
}

bool TradeManager::IsInTrade(PlayObject* player) const {
    if (!player || !m_initialized) {
        return false;
    }

    std::shared_lock<std::shared_mutex> lock(m_tradesMutex);
    return m_playerToTrade.find(player->GetCharName()) != m_playerToTrade.end();
}

TradeState TradeManager::GetTradeState(PlayObject* player) const {
    if (!player || !m_initialized) {
        return TradeState::NONE;
    }

    std::shared_lock<std::shared_mutex> lock(m_tradesMutex);

    const TradeData* trade = GetTradeDataByPlayer(player);
    return trade ? trade->state : TradeState::NONE;
}

const TradeData* TradeManager::GetTradeData(PlayObject* player) const {
    if (!player || !m_initialized) {
        return nullptr;
    }

    std::shared_lock<std::shared_mutex> lock(m_tradesMutex);
    return GetTradeDataByPlayer(player);
}

PlayObject* TradeManager::GetTradePartner(PlayObject* player) const {
    if (!player || !m_initialized) {
        return nullptr;
    }

    std::shared_lock<std::shared_mutex> lock(m_tradesMutex);

    const TradeData* trade = GetTradeDataByPlayer(player);
    if (!trade) {
        return nullptr;
    }

    return (trade->player1 == player) ? trade->player2 : trade->player1;
}

void TradeManager::Run() {
    if (!m_initialized) {
        return;
    }

    DWORD currentTime = GetCurrentTime();

    // 处理交易逻辑
    if (currentTime - m_lastProcessTime >= 1000) { // 每秒处理一次
        ProcessTrades();
        m_lastProcessTime = currentTime;
    }

    // 检查超时
    if (currentTime - m_lastTimeoutCheckTime >= 10000) { // 每10秒检查一次超时
        CheckTimeouts();
        m_lastTimeoutCheckTime = currentTime;
    }
}

void TradeManager::ProcessTrades() {
    std::shared_lock<std::shared_mutex> lock(m_tradesMutex);

    for (const auto& pair : m_trades) {
        TradeData* trade = pair.second.get();
        if (trade && (trade->state == TradeState::TRADING || trade->state == TradeState::LOCKED)) {
            // 验证玩家状态
            if (!ValidatePlayerState(trade->player1) || !ValidatePlayerState(trade->player2)) {
                trade->state = TradeState::CANCELLED;
                NotifyTradeResult(trade, false);
            }
        }
    }
}

void TradeManager::CheckTimeouts() {
    DWORD currentTime = GetCurrentTime();
    std::vector<std::string> timeoutTrades;

    {
        std::shared_lock<std::shared_mutex> lock(m_tradesMutex);

        for (const auto& pair : m_trades) {
            TradeData* trade = pair.second.get();
            if (!trade) continue;

            DWORD timeout = (trade->state == TradeState::REQUESTING) ? m_requestTimeout : m_tradeTimeout;

            if (currentTime - trade->lastUpdateTime > timeout) {
                timeoutTrades.push_back(pair.first);
            }
        }
    }

    // 处理超时的交易
    for (const std::string& tradeId : timeoutTrades) {
        std::unique_lock<std::shared_mutex> lock(m_tradesMutex);

        auto it = m_trades.find(tradeId);
        if (it != m_trades.end()) {
            it->second->state = TradeState::CANCELLED;
            NotifyTradeResult(it->second.get(), false);
            CleanupTrade(tradeId);
            m_statistics.timeoutTrades++;

            Logger::Info("Trade timeout: " + tradeId);
        }
    }
}

void TradeManager::UpdateStatistics() {
    std::shared_lock<std::shared_mutex> lock(m_tradesMutex);

    m_statistics.activeTrades = 0;
    for (const auto& pair : m_trades) {
        if (pair.second->state == TradeState::TRADING ||
            pair.second->state == TradeState::LOCKED) {
            m_statistics.activeTrades++;
        }
    }

    m_statistics.lastUpdateTime = GetCurrentTime();
}

// 私有方法实现
std::string TradeManager::GenerateTradeId(const std::string& player1, const std::string& player2) const {
    return player1 + "_" + player2 + "_" + std::to_string(GetCurrentTime());
}

TradeData* TradeManager::GetTradeDataByPlayer(PlayObject* player) const {
    if (!player) return nullptr;

    auto it = m_playerToTrade.find(player->GetCharName());
    if (it == m_playerToTrade.end()) {
        return nullptr;
    }

    auto tradeIt = m_trades.find(it->second);
    return (tradeIt != m_trades.end()) ? tradeIt->second.get() : nullptr;
}

bool TradeManager::ValidateTradeOperation(PlayObject* player) const {
    return player && m_initialized && IsInTrade(player);
}

bool TradeManager::CanAddTradeItem(PlayObject* player, const UserItem& item) const {
    if (!ValidateTradeOperation(player)) {
        return false;
    }

    // 检查物品是否有效
    if (item.itemIndex == 0 || item.makeIndex == 0) {
        return false;
    }

    // 检查玩家是否拥有该物品
    const auto& bagItems = player->GetBagItems();
    auto it = std::find_if(bagItems.begin(), bagItems.end(),
        [&item](const UserItem& bagItem) {
            return bagItem.makeIndex == item.makeIndex;
        });

    return it != bagItems.end();
}

bool TradeManager::ExecuteTradeExchange(TradeData* trade) {
    if (!trade || !trade->player1 || !trade->player2) {
        return false;
    }

    // 验证物品和金币
    if (!ValidateTradeItems(trade->player1Items) || !ValidateTradeItems(trade->player2Items)) {
        return false;
    }

    if (trade->player1->GetGold() < trade->player1Gold ||
        trade->player2->GetGold() < trade->player2Gold) {
        return false;
    }

    // 执行物品交换
    for (const auto& item : trade->player1Items) {
        if (!trade->player1->DeleteBagItem(item.makeIndex) ||
            !trade->player2->AddBagItem(item)) {
            return false; // 回滚操作
        }
    }

    for (const auto& item : trade->player2Items) {
        if (!trade->player2->DeleteBagItem(item.makeIndex) ||
            !trade->player1->AddBagItem(item)) {
            return false; // 回滚操作
        }
    }

    // 执行金币交换
    if (trade->player1Gold > 0) {
        trade->player1->DecGold(trade->player1Gold);
        trade->player2->IncGold(trade->player1Gold);
    }

    if (trade->player2Gold > 0) {
        trade->player2->DecGold(trade->player2Gold);
        trade->player1->IncGold(trade->player2Gold);
    }

    return true;
}

void TradeManager::CleanupTrade(const std::string& tradeId) {
    auto it = m_trades.find(tradeId);
    if (it != m_trades.end()) {
        // 从玩家映射中移除
        m_playerToTrade.erase(it->second->player1Name);
        m_playerToTrade.erase(it->second->player2Name);

        // 移除交易数据
        m_trades.erase(it);
    }
}

void TradeManager::NotifyTradeUpdate(TradeData* trade) {
    if (!trade) return;

    // 通知双方交易状态更新
    if (trade->player1) {
        trade->player1->SendDefMessage(Protocol::SM_DEALADDITEM, 0, 0, 0, 0);
    }
    if (trade->player2) {
        trade->player2->SendDefMessage(Protocol::SM_DEALADDITEM, 0, 0, 0, 0);
    }
}

void TradeManager::NotifyTradeResult(TradeData* trade, bool success) {
    if (!trade) return;

    WORD result = success ? Protocol::SM_DEALEND : Protocol::SM_DEALCANCEL;

    if (trade->player1) {
        trade->player1->SendDefMessage(result, 0, 0, 0, 0);
    }
    if (trade->player2) {
        trade->player2->SendDefMessage(result, 0, 0, 0, 0);
    }
}

bool TradeManager::ValidateTradeItems(const std::vector<UserItem>& items) const {
    for (const auto& item : items) {
        if (item.itemIndex == 0 || item.makeIndex == 0) {
            return false;
        }
    }
    return true;
}

bool TradeManager::ValidatePlayerState(PlayObject* player) const {
    return player && player->IsOnline() && !player->IsDead();
}

bool TradeManager::CanStartTrade(PlayObject* player1, PlayObject* player2) const {
    if (!player1 || !player2) {
        return false;
    }

    // 检查玩家状态
    if (!ValidatePlayerState(player1) || !ValidatePlayerState(player2)) {
        return false;
    }

    // 检查距离
    Point pos1 = player1->GetCurrentPos();
    Point pos2 = player2->GetCurrentPos();
    int distance = abs(pos1.x - pos2.x) + abs(pos1.y - pos2.y);

    if (distance > 3) { // 最大交易距离
        player1->SendMessage("距离太远，无法交易！");
        return false;
    }

    return true;
}

} // namespace MirServer

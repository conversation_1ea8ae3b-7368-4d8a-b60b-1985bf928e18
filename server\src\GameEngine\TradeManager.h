#pragma once

#include "../Common/Types.h"
#include "../Common/GameData.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <shared_mutex>

namespace MirServer {

class PlayObject;

// 交易状态枚举
enum class TradeState {
    NONE = 0,           // 无交易
    REQUESTING = 1,     // 请求中
    TRADING = 2,        // 交易中
    LOCKED = 3,         // 已锁定
    COMPLETED = 4,      // 已完成
    CANCELLED = 5       // 已取消
};

// 交易数据结构
struct TradeData {
    std::string player1Name;                    // 玩家1名称
    std::string player2Name;                    // 玩家2名称
    PlayObject* player1 = nullptr;             // 玩家1对象
    PlayObject* player2 = nullptr;             // 玩家2对象

    std::vector<UserItem> player1Items;        // 玩家1的物品
    std::vector<UserItem> player2Items;        // 玩家2的物品
    DWORD player1Gold = 0;                     // 玩家1的金币
    DWORD player2Gold = 0;                     // 玩家2的金币

    bool player1Locked = false;                // 玩家1是否锁定
    bool player2Locked = false;                // 玩家2是否锁定

    TradeState state = TradeState::NONE;       // 交易状态
    DWORD startTime = 0;                       // 开始时间
    DWORD lastUpdateTime = 0;                  // 最后更新时间

    TradeData() {
        player1Items.reserve(10);  // 最多10个物品
        player2Items.reserve(10);
    }
};

// 交易管理器类（对应delphi的交易系统）
class TradeManager {
public:
    TradeManager();
    ~TradeManager();

    // 初始化和清理
    bool Initialize();
    void Finalize();

    // 交易请求
    bool RequestTrade(PlayObject* requester, PlayObject* target);
    bool AcceptTrade(PlayObject* player, const std::string& requesterName);
    bool RejectTrade(PlayObject* player, const std::string& requesterName);
    bool CancelTrade(PlayObject* player);

    // 交易操作
    bool AddTradeItem(PlayObject* player, const UserItem& item);
    bool RemoveTradeItem(PlayObject* player, WORD makeIndex);
    bool SetTradeGold(PlayObject* player, DWORD amount);
    bool LockTrade(PlayObject* player);
    bool CompleteTrade(PlayObject* player);

    // 交易查询
    bool IsInTrade(PlayObject* player) const;
    TradeState GetTradeState(PlayObject* player) const;
    const TradeData* GetTradeData(PlayObject* player) const;
    PlayObject* GetTradePartner(PlayObject* player) const;

    // 运行时更新
    void Run();
    void ProcessTrades();
    void CheckTimeouts();

    // 统计信息
    struct Statistics {
        int activeTrades = 0;
        int completedTrades = 0;
        int cancelledTrades = 0;
        int timeoutTrades = 0;
        DWORD totalTradeValue = 0;
        DWORD lastUpdateTime = 0;
    };

    const Statistics& GetStatistics() const { return m_statistics; }
    void UpdateStatistics();

private:
    bool m_initialized = false;

    // 交易数据映射 (交易ID -> 交易数据)
    std::unordered_map<std::string, std::unique_ptr<TradeData>> m_trades;

    // 玩家到交易的映射 (玩家名 -> 交易ID)
    std::unordered_map<std::string, std::string> m_playerToTrade;

    mutable std::shared_mutex m_tradesMutex;

    // 配置
    DWORD m_tradeTimeout = 300000;              // 交易超时时间（5分钟）
    DWORD m_requestTimeout = 30000;             // 请求超时时间（30秒）
    int m_maxTradeItems = 10;                   // 最大交易物品数

    // 运行时状态
    DWORD m_lastProcessTime = 0;
    DWORD m_lastTimeoutCheckTime = 0;

    // 统计信息
    Statistics m_statistics;

    // 内部方法
    std::string GenerateTradeId(const std::string& player1, const std::string& player2) const;
    TradeData* GetTradeDataByPlayer(PlayObject* player) const;
    bool ValidateTradeOperation(PlayObject* player) const;
    bool CanAddTradeItem(PlayObject* player, const UserItem& item) const;
    bool ExecuteTradeExchange(TradeData* trade);
    void CleanupTrade(const std::string& tradeId);
    void NotifyTradeUpdate(TradeData* trade);
    void NotifyTradeResult(TradeData* trade, bool success);

    // 验证方法
    bool ValidateTradeItems(const std::vector<UserItem>& items) const;
    bool ValidatePlayerState(PlayObject* player) const;
    bool CanStartTrade(PlayObject* player1, PlayObject* player2) const;
};

// 全局交易管理器实例
extern std::unique_ptr<TradeManager> g_TradeManager;

} // namespace MirServer

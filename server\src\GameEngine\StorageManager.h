#pragma once

#include "../Common/Types.h"
#include "../Common/GameData.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <shared_mutex>

namespace MirServer {

class PlayObject;

// 仓库数据结构
struct StorageData {
    std::string password;                    // 仓库密码
    std::vector<UserItem> items;            // 仓库物品
    DWORD gold = 0;                         // 仓库金币
    DWORD lastAccessTime = 0;               // 最后访问时间
    bool isOpen = false;                    // 是否已打开
    int maxSlots = 40;                      // 最大格子数

    StorageData() {
        items.reserve(maxSlots);
    }
};

// 仓库管理器类（对应delphi的仓库系统）
class StorageManager {
public:
    StorageManager();
    ~StorageManager();

    // 初始化和清理
    bool Initialize();
    void Finalize();

    // 仓库操作
    bool OpenStorage(PlayObject* player, const std::string& password);
    bool CloseStorage(PlayObject* player);
    bool IsStorageOpen(PlayObject* player) const;

    // 物品操作
    bool StoreItem(PlayObject* player, const UserItem& item);
    bool TakeItem(PlayObject* player, WORD makeIndex, UserItem& outItem);
    bool StoreGold(PlayObject* player, DWORD amount);
    bool TakeGold(PlayObject* player, DWORD amount);

    // 仓库查询
    const std::vector<UserItem>& GetStorageItems(PlayObject* player) const;
    DWORD GetStorageGold(PlayObject* player) const;
    int GetStorageItemCount(PlayObject* player) const;
    bool IsStorageFull(PlayObject* player) const;

    // 密码管理
    bool SetStoragePassword(PlayObject* player, const std::string& newPassword);
    bool VerifyStoragePassword(PlayObject* player, const std::string& password) const;
    bool HasStoragePassword(PlayObject* player) const;

    // 数据持久化
    bool SaveStorageData(PlayObject* player);
    bool LoadStorageData(PlayObject* player);

    // 清理和维护
    void CleanupExpiredSessions();
    void SaveAllStorageData();

    // 统计信息
    struct Statistics {
        int totalStorages = 0;
        int activeStorages = 0;
        int totalItems = 0;
        DWORD totalGold = 0;
        DWORD lastUpdateTime = 0;
    };

    const Statistics& GetStatistics() const { return m_statistics; }
    void UpdateStatistics();

private:
    bool m_initialized = false;

    // 仓库数据映射 (玩家名 -> 仓库数据)
    std::unordered_map<std::string, std::unique_ptr<StorageData>> m_storages;
    mutable std::shared_mutex m_storagesMutex;

    // 配置
    int m_defaultMaxSlots = 40;              // 默认仓库格子数
    DWORD m_sessionTimeout = 300000;         // 会话超时时间（5分钟）
    DWORD m_autoSaveInterval = 60000;        // 自动保存间隔（1分钟）

    // 运行时状态
    DWORD m_lastCleanupTime = 0;
    DWORD m_lastSaveTime = 0;

    // 统计信息
    Statistics m_statistics;

    // 内部方法
    StorageData* GetStorageData(PlayObject* player) const;
    StorageData* CreateStorageData(PlayObject* player);
    std::string GetStorageKey(PlayObject* player) const;
    bool ValidateStorageAccess(PlayObject* player) const;

    // 数据库操作
    bool LoadStorageFromDB(const std::string& playerName, StorageData& storage);
    bool SaveStorageToDB(const std::string& playerName, const StorageData& storage);

    // 密码加密/解密
    std::string EncryptPassword(const std::string& password) const;
    bool VerifyPassword(const std::string& password, const std::string& encrypted) const;
};

// 全局仓库管理器实例
extern std::unique_ptr<StorageManager> g_StorageManager;

} // namespace MirServer

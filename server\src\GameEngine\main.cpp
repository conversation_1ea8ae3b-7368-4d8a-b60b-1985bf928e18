// main.cpp - 游戏引擎主程序
#include "UserEngine.h"
#include "MapManager.h"
#include "ItemManager.h"
#include "Environment.h"
#include "../Protocol/NetworkManager.h"
#include "../Protocol/MessageConverter.h"
#include "../Common/Logger.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <csignal>
#include <atomic>

namespace MirServer {

// 外部全局变量声明
extern std::unique_ptr<MapManager> g_MapManager;
extern std::unique_ptr<ItemManager> g_ItemManager;

std::atomic<bool> g_running{true};

// 信号处理
void SignalHandler(int signal) {
    if (signal == SIGINT || signal == SIGTERM) {
        std::cout << "\n\nShutting down server..." << std::endl;
        g_running = false;
    }
}

// 游戏引擎数据包处理器
class GameEnginePacketHandler : public Network::PacketHandler {
private:
    UserEngine* m_userEngine;
    MapManager* m_mapManager;
    ItemManager* m_itemManager;

public:
    GameEnginePacketHandler(UserEngine* userEngine, MapManager* mapManager, ItemManager* itemManager)
        : m_userEngine(userEngine), m_mapManager(mapManager), m_itemManager(itemManager) {}

    void HandlePacket(std::shared_ptr<Network::ClientConnection> client,
                     const Protocol::PacketHeader& header,
                     const uint8_t* data,
                     size_t dataSize) override {
        // 根据连接ID获取玩家
        auto player = m_userEngine->GetPlayerByConnection(client->GetId());

        switch (header.packetType) {
            case Protocol::CM_IDPASSWORD: {
                // 模拟登录
                std::cout << "[GameEngine] Player login from connection " << client->GetId() << std::endl;

                // 创建测试玩家
                HumDataInfo humData;
                humData.charName = "TestPlayer" + std::to_string(client->GetId());
                humData.account = "test";
                humData.level = 1;
                humData.mapName = "0";
                humData.currentPos = Point(300, 300);

                auto newPlayer = m_userEngine->CreatePlayer(humData);
                if (m_userEngine->PlayerLogin(newPlayer)) {
                    m_userEngine->BindPlayerToConnection(newPlayer, client->GetId());
                    std::cout << "[GameEngine] Player " << humData.charName << " logged in" << std::endl;
                }
                break;
            }

            case Protocol::CM_SAY: {
                if (player) {
                    Protocol::MessageConverter::ChatMessage chatMsg;
                    if (Protocol::MessageConverter::DecodeChatMessage(data, dataSize, chatMsg)) {
                        // GM命令处理
                        if (chatMsg.message.length() > 0 && chatMsg.message[0] == '@') {
                            m_userEngine->ProcessGMCommand(player, chatMsg.message);
                        } else {
                            // 广播消息
                            std::string broadcastMsg = player->GetCharName() + ": " + chatMsg.message;
                            m_userEngine->BroadcastMessage(broadcastMsg);
                            std::cout << "[Chat] " << broadcastMsg << std::endl;
                        }
                    }
                }
                break;
            }

            case Protocol::CM_WALK:
            case Protocol::CM_RUN: {
                if (player) {
                    Protocol::MessageConverter::MoveRequest moveReq;
                    if (Protocol::MessageConverter::DecodeMoveRequest(data, dataSize, moveReq)) {
                        if (player->Walk(moveReq.direction)) {
                            std::cout << "[Movement] " << player->GetCharName()
                                     << " moved to (" << player->GetCurrentPos().x
                                     << ", " << player->GetCurrentPos().y << ")" << std::endl;
                        }
                    }
                }
                break;
            }

            default:
                std::cout << "[GameEngine] Unhandled packet: " << header.packetType << std::endl;
                break;
        }
    }

    void OnClientConnected(std::shared_ptr<Network::ClientConnection> client) override {
        std::cout << "[GameEngine] New connection: " << client->GetId() << std::endl;
    }

    void OnClientDisconnected(std::shared_ptr<Network::ClientConnection> client) override {
        std::cout << "[GameEngine] Connection lost: " << client->GetId() << std::endl;
        m_userEngine->PlayerDisconnect(client->GetId());
    }
};

// 主游戏循环
void GameLoop(UserEngine* userEngine, MapManager* mapManager, EnvironmentManager* envManager) {
    const int TICK_INTERVAL = 50; // 50ms per tick

    while (g_running) {
        auto startTime = std::chrono::steady_clock::now();

        // 运行用户引擎
        userEngine->Run();

        // 运行所有地图环境
        envManager->RunAll();

        // 显示统计信息
        static int statCounter = 0;
        if (++statCounter >= 100) { // 每5秒显示一次
            statCounter = 0;

            auto stats = userEngine->GetStatistics();
            std::cout << "\n=== Server Statistics ===" << std::endl;
            std::cout << "Active Players: " << stats.activePlayers << std::endl;
            std::cout << "Total Logins: " << stats.totalLoginCount << std::endl;
            std::cout << "Max Concurrent: " << stats.maxConcurrentPlayers << std::endl;
            std::cout << "========================\n" << std::endl;
        }

        // 控制帧率
        auto endTime = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        if (elapsed.count() < TICK_INTERVAL) {
            std::this_thread::sleep_for(std::chrono::milliseconds(TICK_INTERVAL - elapsed.count()));
        }
    }
}

} // namespace MirServer

int main(int argc, char* argv[]) {
    using namespace MirServer;

    // 设置信号处理
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);

    // 初始化日志系统
    Logger::SetLogLevel(LogLevel::LOG_INFO);
    Logger::EnableConsoleOutput(true);
    Logger::SetLogFile("GameEngine.log");

    std::cout << "==================================" << std::endl;
    std::cout << "  MirServer Game Engine v1.0      " << std::endl;
    std::cout << "  Phase 3 Components Demo         " << std::endl;
    std::cout << "==================================" << std::endl;

    try {
        // 创建管理器实例
        g_UserEngine = std::make_unique<UserEngine>();
        auto mapManager = std::make_shared<MapManager>();
        auto itemManager = std::make_shared<ItemManager>();

        // 初始化地图管理器
        if (!mapManager->Initialize("./maps")) {
            Logger::Error("Failed to initialize MapManager");
            return 1;
        }

        // 加载测试地图
        MapInfo testMap;
        testMap.mapName = "0";
        testMap.mapFile = "0.map";
        testMap.width = 1000;
        testMap.height = 1000;
        testMap.title = "Beach";
        // 创建测试地图环境
        auto env = EnvironmentManager::Instance().CreateEnvironment("0", 1000, 1000);

        // 初始化物品管理器
        if (!itemManager->Initialize("./data")) {
            Logger::Error("Failed to initialize ItemManager");
            return 1;
        }

        // 模拟加载物品数据
        StdItem testItem;
        testItem.idx = 1;
        testItem.name = "WoodenSword";
        testItem.stdMode = static_cast<uint8_t>(ItemType::WEAPON);
        testItem.weight = 10;
        testItem.price = 100;
        testItem.dc = 3;
        testItem.dc2 = 5;
        // 这里可以添加更多测试物品

        // 初始化用户引擎
        if (!g_UserEngine->Initialize(mapManager, itemManager)) {
            Logger::Error("Failed to initialize UserEngine");
            return 1;
        }

        // 创建网络管理器
        auto networkManager = std::make_unique<Network::NetworkManager>();

        // 初始化网络
        if (!networkManager->Initialize()) {
            Logger::Error("Failed to initialize network manager");
            return 1;
        }

        // 创建游戏引擎数据包处理器
        auto packetHandler = std::make_shared<GameEnginePacketHandler>(
            g_UserEngine.get(), mapManager.get(), itemManager.get()
        );
        networkManager->SetPacketHandler(packetHandler);

        // 启动网络服务
        if (!networkManager->StartServer(7000)) {
            Logger::Error("Failed to start server on port 7000");
            return 1;
        }

        std::cout << "\nGame Engine started successfully!" << std::endl;
        std::cout << "Listening on port 7000" << std::endl;
        std::cout << "\nAvailable GM Commands:" << std::endl;
        std::cout << "  @level <level>    - Set player level" << std::endl;
        std::cout << "  @gold <amount>    - Set player gold" << std::endl;
        std::cout << "  @item <name>      - Give item (not implemented)" << std::endl;
        std::cout << "  @move <map> <x> <y> - Move to location" << std::endl;
        std::cout << "  @kick <player>    - Kick player" << std::endl;
        std::cout << "  @shutdown [delay] - Shutdown server" << std::endl;
        std::cout << "\nPress Ctrl+C to shutdown..." << std::endl;

        // 运行游戏主循环
        GameLoop(g_UserEngine.get(), mapManager.get(), &EnvironmentManager::Instance());

        // 清理
        std::cout << "\nShutting down services..." << std::endl;
        networkManager->StopServer();
        networkManager->Shutdown();

        g_UserEngine->Finalize();
        mapManager->Finalize();
        itemManager->Finalize();

        // 清理全局指针
        g_UserEngine.reset();
        g_MapManager.reset();
        g_ItemManager.reset();

        std::cout << "Server shutdown complete." << std::endl;

    } catch (const std::exception& e) {
        Logger::Fatal("Fatal error: " + std::string(e.what()));
        return 1;
    }

    return 0;
}
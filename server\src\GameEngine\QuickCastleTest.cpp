// QuickCastleTest.cpp - 快速城堡系统测试
#include "CastleManager.h"
#include "GuildManager.h"
#include "../Common/Logger.h"
#include <iostream>

using namespace MirServer;

int main() {
    std::cout << "Quick Castle System Test" << std::endl;
    std::cout << "========================" << std::endl;
    
    try {
        // 设置日志
        Logger::SetLogFile("quick_castle_test.log");
        Logger::SetLogLevel(LogLevel::LOG_INFO);
        
        std::cout << "1. Initializing GuildManager..." << std::endl;
        GuildManager::GetInstance().Initialize();
        std::cout << "   Done" << std::endl;
        
        std::cout << "2. Initializing CastleManager..." << std::endl;
        CastleManager::GetInstance().Initialize();
        std::cout << "   Done" << std::endl;
        
        std::cout << "3. Getting castle info..." << std::endl;
        CastleManager& manager = CastleManager::GetInstance();
        std::cout << "   Castle count: " << manager.GetCastleCount() << std::endl;
        
        Castle* castle = manager.FindCastle("Sabuk");
        if (castle) {
            std::cout << "   Found castle: " << castle->GetCastleName() << std::endl;
            std::cout << "   Map: " << castle->GetMapName() << std::endl;
            std::cout << "   Home: (" << castle->GetHomeX() << ", " << castle->GetHomeY() << ")" << std::endl;
            
            // 测试基本功能（不调用可能有问题的方法）
            std::cout << "4. Testing basic functions..." << std::endl;
            
            // 测试收入
            std::cout << "   Adding 1000 gold..." << std::endl;
            castle->IncomeGold(1000);
            std::cout << "   Done" << std::endl;
            
            // 测试技术等级
            std::cout << "   Setting tech level to 3..." << std::endl;
            castle->SetTechLevel(3);
            std::cout << "   Done" << std::endl;
            
            // 测试力量值
            std::cout << "   Setting power to 200..." << std::endl;
            castle->SetPower(200);
            std::cout << "   Done" << std::endl;
            
            // 测试攻击者管理（使用固定时间戳）
            std::cout << "   Adding attacker..." << std::endl;
            bool result = castle->AddAttacker("TestGuild", 12345);
            std::cout << "   Result: " << (result ? "Success" : "Failed") << std::endl;
            
            // 测试防御单元修复
            std::cout << "   Testing defense repair..." << std::endl;
            result = castle->RepairDoor();
            std::cout << "   Repair door result: " << (result ? "Success" : "Failed") << std::endl;
            
            // 测试保存
            std::cout << "   Saving castle data..." << std::endl;
            castle->Save();
            std::cout << "   Done" << std::endl;
            
        } else {
            std::cout << "   Castle not found!" << std::endl;
        }
        
        std::cout << "5. Testing CastleManager functions..." << std::endl;
        
        std::vector<std::string> nameList;
        manager.GetCastleNameList(nameList);
        std::cout << "   Castle names:" << std::endl;
        for (const auto& name : nameList) {
            std::cout << "     - " << name << std::endl;
        }
        
        std::cout << "6. Cleanup..." << std::endl;
        CastleManager::GetInstance().Finalize();
        GuildManager::GetInstance().Finalize();
        std::cout << "   Done" << std::endl;
        
        std::cout << "\n=== Test Completed ===" << std::endl;
        std::cout << "All castle system functions tested successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception occurred: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}

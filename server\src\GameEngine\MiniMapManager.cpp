#include "MiniMapManager.h"
#include "../BaseObject/PlayObject.h"
#include "../Common/Logger.h"
#include "../Protocol/PacketTypes.h"
#include "MapManager.h"
#include <algorithm>
#include <fstream>

namespace MirServer {

// 全局实例
std::unique_ptr<MiniMapManager> g_MiniMapManager = nullptr;

MiniMapManager::MiniMapManager() {
    Logger::Info("MiniMapManager created");
}

MiniMapManager::~MiniMapManager() {
    Finalize();
    Logger::Info("MiniMapManager destroyed");
}

bool MiniMapManager::Initialize() {
    if (m_initialized) {
        return true;
    }

    Logger::Info("Initializing MiniMapManager...");

    // 初始化统计信息
    m_statistics = {};
    m_lastProcessTime = GetCurrentTime();
    m_lastCleanupTime = GetCurrentTime();

    m_initialized = true;
    Logger::Info("MiniMapManager initialized successfully");

    return true;
}

void MiniMapManager::Finalize() {
    if (!m_initialized) {
        return;
    }

    Logger::Info("Finalizing MiniMapManager...");

    // 保存所有小地图数据
    {
        std::shared_lock<std::shared_mutex> lock(m_miniMapsMutex);
        for (const auto& pair : m_miniMaps) {
            if (pair.second && pair.second->generated) {
                SaveMiniMapToFile(pair.first, *pair.second);
            }
        }
    }

    // 清理数据
    {
        std::unique_lock<std::shared_mutex> mapLock(m_miniMapsMutex);
        std::unique_lock<std::shared_mutex> markLock(m_marksMutex);

        m_miniMaps.clear();
        m_mapMarks.clear();
    }

    m_initialized = false;
    Logger::Info("MiniMapManager finalized");
}

bool MiniMapManager::LoadMiniMapData(const std::string& mapName) {
    if (!m_initialized || !m_enabled) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_miniMapsMutex);

    // 检查是否已经加载
    auto it = m_miniMaps.find(mapName);
    if (it != m_miniMaps.end() && it->second->generated) {
        return true;
    }

    // 创建新的小地图数据
    auto miniMap = std::make_unique<MiniMapData>();
    miniMap->mapName = mapName;

    // 尝试从文件加载
    if (LoadMiniMapFromFile(mapName, *miniMap)) {
        miniMap->generated = true;
        m_miniMaps[mapName] = std::move(miniMap);
        Logger::Info("Loaded minimap from file: " + mapName);
        return true;
    }

    // 文件不存在，生成新的小地图
    if (GenerateMiniMapFromMapData(mapName, *miniMap)) {
        miniMap->generated = true;
        miniMap->lastUpdateTime = GetCurrentTime();
        m_miniMaps[mapName] = std::move(miniMap);

        // 保存到文件
        SaveMiniMapToFile(mapName, *m_miniMaps[mapName]);

        Logger::Info("Generated new minimap: " + mapName);
        return true;
    }

    Logger::Error("Failed to load/generate minimap: " + mapName);
    return false;
}

bool MiniMapManager::GenerateMiniMapData(const std::string& mapName) {
    if (!m_initialized || !m_enabled) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_miniMapsMutex);

    auto miniMap = std::make_unique<MiniMapData>();
    miniMap->mapName = mapName;

    if (GenerateMiniMapFromMapData(mapName, *miniMap)) {
        miniMap->generated = true;
        miniMap->lastUpdateTime = GetCurrentTime();
        m_miniMaps[mapName] = std::move(miniMap);

        Logger::Info("Generated minimap data: " + mapName);
        return true;
    }

    return false;
}

const MiniMapData* MiniMapManager::GetMiniMapData(const std::string& mapName) const {
    if (!m_initialized || !m_enabled) {
        return nullptr;
    }

    std::shared_lock<std::shared_mutex> lock(m_miniMapsMutex);

    auto it = m_miniMaps.find(mapName);
    return (it != m_miniMaps.end()) ? it->second.get() : nullptr;
}

std::vector<uint8_t> MiniMapManager::GetMiniMapImage(const std::string& mapName) const {
    const MiniMapData* miniMap = GetMiniMapData(mapName);
    if (miniMap && miniMap->generated) {
        return miniMap->imageData;
    }
    return {};
}

bool MiniMapManager::SendMiniMapToPlayer(PlayObject* player, const std::string& mapName) {
    if (!player || !m_initialized || !m_enabled) {
        return false;
    }

    // 确保小地图数据已加载
    if (!LoadMiniMapData(mapName)) {
        return false;
    }

    const MiniMapData* miniMap = GetMiniMapData(mapName);
    if (!miniMap) {
        return false;
    }

    // 发送小地图数据包
    SendMiniMapPacket(player, *miniMap);

    // 发送地图标记
    SendMapMarksPacket(player, mapName);

    Logger::Debug("Sent minimap to player: " + player->GetCharName() + ", map: " + mapName);
    return true;
}

bool MiniMapManager::UpdatePlayerMiniMap(PlayObject* player) {
    if (!player || !m_initialized || !m_enabled) {
        return false;
    }

    std::string mapName = player->GetMapName();
    if (mapName.empty()) {
        return false;
    }

    // 更新玩家位置标记
    UpdatePlayerPosition(player, player->GetCurrentPos());

    // 更新队友位置
    UpdatePartyMemberPositions(player);

    return true;
}

void MiniMapManager::AddMapMark(const std::string& mapName, const MapMark& mark) {
    if (!m_initialized || !m_enabled) {
        return;
    }

    AddMarkInternal(mapName, mark);
}

void MiniMapManager::RemoveMapMark(const std::string& mapName, MapMarkType type, const Point& position) {
    if (!m_initialized || !m_enabled) {
        return;
    }

    std::unique_lock<std::shared_mutex> lock(m_marksMutex);

    auto it = m_mapMarks.find(mapName);
    if (it != m_mapMarks.end()) {
        auto& marks = it->second;
        marks.erase(
            std::remove_if(marks.begin(), marks.end(),
                [type, &position](const MapMark& mark) {
                    return mark.type == type &&
                           mark.position.x == position.x &&
                           mark.position.y == position.y;
                }),
            marks.end()
        );
    }
}

void MiniMapManager::ClearMapMarks(const std::string& mapName, MapMarkType type) {
    if (!m_initialized || !m_enabled) {
        return;
    }

    std::unique_lock<std::shared_mutex> lock(m_marksMutex);

    auto it = m_mapMarks.find(mapName);
    if (it != m_mapMarks.end()) {
        auto& marks = it->second;
        marks.erase(
            std::remove_if(marks.begin(), marks.end(),
                [type](const MapMark& mark) {
                    return mark.type == type;
                }),
            marks.end()
        );
    }
}

std::vector<MapMark> MiniMapManager::GetMapMarks(const std::string& mapName, MapMarkType type) const {
    std::vector<MapMark> result;

    if (!m_initialized || !m_enabled) {
        return result;
    }

    std::shared_lock<std::shared_mutex> lock(m_marksMutex);

    auto it = m_mapMarks.find(mapName);
    if (it != m_mapMarks.end()) {
        for (const auto& mark : it->second) {
            if (mark.type == type && mark.visible) {
                result.push_back(mark);
            }
        }
    }

    return result;
}

void MiniMapManager::UpdatePlayerPosition(PlayObject* player, const Point& position) {
    if (!player || !m_initialized || !m_enabled) {
        return;
    }

    std::string mapName = player->GetMapName();
    if (mapName.empty()) {
        return;
    }

    // 移除旧的玩家位置标记
    RemoveMapMark(mapName, MapMarkType::PLAYER, player->GetCurrentPos());

    // 添加新的玩家位置标记
    MapMark playerMark;
    playerMark.type = MapMarkType::PLAYER;
    playerMark.position = position;
    playerMark.name = player->GetCharName();
    playerMark.description = "玩家位置";
    playerMark.color = 0xFF00FF00; // 绿色
    playerMark.visible = true;

    AddMapMark(mapName, playerMark);
}

void MiniMapManager::UpdatePartyMemberPositions(PlayObject* player) {
    if (!player || !m_initialized || !m_enabled) {
        return;
    }

    // TODO: 实现队友位置更新
    // 这里需要获取玩家的队伍信息，然后更新队友位置标记
}

void MiniMapManager::AddNPCMark(const std::string& mapName, const Point& position, const std::string& npcName) {
    MapMark npcMark;
    npcMark.type = MapMarkType::NPC;
    npcMark.position = position;
    npcMark.name = npcName;
    npcMark.description = "NPC: " + npcName;
    npcMark.color = 0xFF0000FF; // 蓝色
    npcMark.visible = true;

    AddMapMark(mapName, npcMark);
}

void MiniMapManager::AddPortalMark(const std::string& mapName, const Point& position, const std::string& targetMap) {
    MapMark portalMark;
    portalMark.type = MapMarkType::PORTAL;
    portalMark.position = position;
    portalMark.name = "传送门";
    portalMark.description = "传送到: " + targetMap;
    portalMark.color = 0xFFFF00FF; // 紫色
    portalMark.visible = true;

    AddMapMark(mapName, portalMark);
}

void MiniMapManager::AddLandmark(const std::string& mapName, const Point& position, const std::string& name) {
    MapMark landmarkMark;
    landmarkMark.type = MapMarkType::LANDMARK;
    landmarkMark.position = position;
    landmarkMark.name = name;
    landmarkMark.description = "地标: " + name;
    landmarkMark.color = 0xFFFFFF00; // 黄色
    landmarkMark.visible = true;

    AddMapMark(mapName, landmarkMark);
}

void MiniMapManager::AddQuestMark(const std::string& mapName, const Point& position, const std::string& questName) {
    MapMark questMark;
    questMark.type = MapMarkType::QUEST_TARGET;
    questMark.position = position;
    questMark.name = questName;
    questMark.description = "任务目标: " + questName;
    questMark.color = 0xFFFF8000; // 橙色
    questMark.visible = true;
    questMark.expireTime = GetCurrentTime() + m_markExpireTime; // 5分钟后过期

    AddMapMark(mapName, questMark);
}

void MiniMapManager::RemoveQuestMark(const std::string& mapName, const Point& position) {
    RemoveMapMark(mapName, MapMarkType::QUEST_TARGET, position);
}

void MiniMapManager::Run() {
    if (!m_initialized || !m_enabled) {
        return;
    }

    DWORD currentTime = GetCurrentTime();

    // 处理小地图更新
    if (currentTime - m_lastProcessTime >= m_updateInterval) {
        ProcessMiniMaps();
        m_lastProcessTime = currentTime;
    }

    // 清理过期标记
    if (currentTime - m_lastCleanupTime >= 60000) { // 每分钟清理一次
        CleanupExpiredMarks();
        m_lastCleanupTime = currentTime;
    }
}

void MiniMapManager::ProcessMiniMaps() {
    // 更新统计信息
    UpdateStatistics();

    // 限制缓存大小
    LimitCacheSize();
}

void MiniMapManager::CleanupExpiredMarks() {
    DWORD currentTime = GetCurrentTime();

    std::unique_lock<std::shared_mutex> lock(m_marksMutex);

    for (auto& mapPair : m_mapMarks) {
        auto& marks = mapPair.second;
        marks.erase(
            std::remove_if(marks.begin(), marks.end(),
                [currentTime](const MapMark& mark) {
                    return mark.expireTime > 0 && currentTime > mark.expireTime;
                }),
            marks.end()
        );
    }
}

void MiniMapManager::UpdateStatistics() {
    std::shared_lock<std::shared_mutex> mapLock(m_miniMapsMutex);
    std::shared_lock<std::shared_mutex> markLock(m_marksMutex);

    m_statistics.totalMaps = static_cast<int>(m_miniMaps.size());
    m_statistics.generatedMaps = 0;
    m_statistics.activeMarks = 0;
    m_statistics.cacheSize = 0;

    for (const auto& pair : m_miniMaps) {
        if (pair.second->generated) {
            m_statistics.generatedMaps++;
        }
        m_statistics.cacheSize += pair.second->imageData.size();
    }

    for (const auto& pair : m_mapMarks) {
        m_statistics.activeMarks += static_cast<int>(pair.second.size());
    }

    m_statistics.lastUpdateTime = GetCurrentTime();
}

// 私有方法实现
bool MiniMapManager::LoadMiniMapFromFile(const std::string& mapName, MiniMapData& miniMap) {
    std::string filename = "data/minimaps/" + mapName + ".minimap";
    std::ifstream file(filename, std::ios::binary);

    if (!file.is_open()) {
        return false;
    }

    try {
        // 读取基本信息
        file.read(reinterpret_cast<char*>(&miniMap.width), sizeof(miniMap.width));
        file.read(reinterpret_cast<char*>(&miniMap.height), sizeof(miniMap.height));

        // 读取图像数据大小
        size_t dataSize;
        file.read(reinterpret_cast<char*>(&dataSize), sizeof(dataSize));

        // 读取图像数据
        miniMap.imageData.resize(dataSize);
        file.read(reinterpret_cast<char*>(miniMap.imageData.data()), dataSize);

        // 解压缩数据
        DecompressMiniMapData(miniMap.imageData);

        file.close();
        Logger::Debug("Loaded minimap from file: " + filename);
        return true;

    } catch (const std::exception& e) {
        Logger::Error("Error loading minimap file " + filename + ": " + e.what());
        file.close();
        return false;
    }
}

bool MiniMapManager::SaveMiniMapToFile(const std::string& mapName, const MiniMapData& miniMap) {
    std::string filename = "data/minimaps/" + mapName + ".minimap";
    std::ofstream file(filename, std::ios::binary);

    if (!file.is_open()) {
        Logger::Error("Failed to create minimap file: " + filename);
        return false;
    }

    try {
        // 压缩图像数据
        std::vector<uint8_t> compressedData = miniMap.imageData;
        CompressMiniMapData(compressedData);

        // 写入基本信息
        file.write(reinterpret_cast<const char*>(&miniMap.width), sizeof(miniMap.width));
        file.write(reinterpret_cast<const char*>(&miniMap.height), sizeof(miniMap.height));

        // 写入图像数据大小
        size_t dataSize = compressedData.size();
        file.write(reinterpret_cast<const char*>(&dataSize), sizeof(dataSize));

        // 写入图像数据
        file.write(reinterpret_cast<const char*>(compressedData.data()), dataSize);

        file.close();
        Logger::Debug("Saved minimap to file: " + filename);
        return true;

    } catch (const std::exception& e) {
        Logger::Error("Error saving minimap file " + filename + ": " + e.what());
        file.close();
        return false;
    }
}

bool MiniMapManager::GenerateMiniMapFromMapData(const std::string& mapName, MiniMapData& miniMap) {
    // TODO: 从地图管理器获取地图数据
    // 这里需要与MapManager集成来获取实际的地图数据

    // 临时实现：生成一个简单的小地图
    miniMap.width = 100;
    miniMap.height = 100;
    miniMap.imageData.resize(miniMap.width * miniMap.height * 3); // RGB格式

    // 生成简单的棋盘格图案作为示例
    for (int y = 0; y < miniMap.height; y++) {
        for (int x = 0; x < miniMap.width; x++) {
            int index = (y * miniMap.width + x) * 3;
            uint8_t color = ((x / 10) + (y / 10)) % 2 ? 255 : 128;

            miniMap.imageData[index] = color;     // R
            miniMap.imageData[index + 1] = color; // G
            miniMap.imageData[index + 2] = color; // B
        }
    }

    // 生成地图标记
    GenerateMapMarks(mapName, miniMap);

    Logger::Debug("Generated minimap data for: " + mapName);
    return true;
}

void MiniMapManager::CompressMiniMapData(std::vector<uint8_t>& data) {
    // TODO: 实现数据压缩（可以使用zlib或其他压缩算法）
    // 这里暂时不压缩
}

void MiniMapManager::DecompressMiniMapData(std::vector<uint8_t>& data) {
    // TODO: 实现数据解压缩
    // 这里暂时不解压缩
}

void MiniMapManager::AddMarkInternal(const std::string& mapName, const MapMark& mark) {
    std::unique_lock<std::shared_mutex> lock(m_marksMutex);
    m_mapMarks[mapName].push_back(mark);
}

void MiniMapManager::RemoveExpiredMarks(const std::string& mapName) {
    DWORD currentTime = GetCurrentTime();

    std::unique_lock<std::shared_mutex> lock(m_marksMutex);

    auto it = m_mapMarks.find(mapName);
    if (it != m_mapMarks.end()) {
        auto& marks = it->second;
        marks.erase(
            std::remove_if(marks.begin(), marks.end(),
                [currentTime](const MapMark& mark) {
                    return mark.expireTime > 0 && currentTime > mark.expireTime;
                }),
            marks.end()
        );
    }
}

void MiniMapManager::LimitCacheSize() {
    std::unique_lock<std::shared_mutex> lock(m_miniMapsMutex);

    if (m_miniMaps.size() <= m_maxCacheSize) {
        return;
    }

    // 找到最久未使用的地图并移除
    std::vector<std::pair<std::string, DWORD>> mapTimes;
    for (const auto& pair : m_miniMaps) {
        mapTimes.emplace_back(pair.first, pair.second->lastUpdateTime);
    }

    std::sort(mapTimes.begin(), mapTimes.end(),
        [](const auto& a, const auto& b) {
            return a.second < b.second;
        });

    // 移除最旧的地图
    size_t toRemove = m_miniMaps.size() - m_maxCacheSize;
    for (size_t i = 0; i < toRemove; i++) {
        const std::string& mapName = mapTimes[i].first;

        // 保存到文件
        auto it = m_miniMaps.find(mapName);
        if (it != m_miniMaps.end() && it->second->generated) {
            SaveMiniMapToFile(mapName, *it->second);
        }

        m_miniMaps.erase(mapName);
        Logger::Debug("Removed minimap from cache: " + mapName);
    }
}

uint8_t MiniMapManager::GetPixelColor(const std::string& mapName, int x, int y) const {
    // TODO: 根据地图数据获取像素颜色
    // 这里需要与MapManager集成来获取实际的地图单元格数据
    return 128; // 临时返回灰色
}

void MiniMapManager::GenerateMapMarks(const std::string& mapName, MiniMapData& miniMap) {
    // TODO: 从地图数据生成标记
    // 这里需要扫描地图中的NPC、传送门等对象并生成相应的标记

    // 临时添加一些示例标记
    miniMap.npcs.push_back({50, 50});
    miniMap.portals.push_back({10, 10});
    miniMap.landmarks.push_back({75, 25});
}

void MiniMapManager::SendMiniMapPacket(PlayObject* player, const MiniMapData& miniMap) {
    if (!player) return;

    // TODO: 实现发送小地图数据包
    // 这里应该将小地图数据打包发送给客户端
    player->SendDefMessage(Protocol::SM_SENDMINIMAP, 0, 0, 0, 0);

    Logger::Debug("Sent minimap packet to player: " + player->GetCharName());
}

void MiniMapManager::SendMapMarksPacket(PlayObject* player, const std::string& mapName) {
    if (!player) return;

    // TODO: 实现发送地图标记数据包
    // 这里应该将地图标记数据打包发送给客户端

    Logger::Debug("Sent map marks packet to player: " + player->GetCharName());
}

} // namespace MirServer

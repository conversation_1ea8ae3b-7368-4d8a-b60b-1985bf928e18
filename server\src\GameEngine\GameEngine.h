#pragma once

#include "../Common/Types.h"
#include "../Common/Utils.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <thread>
#include <atomic>
#include <mutex>
#include <shared_mutex>

namespace MirServer {

// 前向声明
class UserEngine;
class MapManager;
class ItemManager;
class MagicManager;
class NPCManager;
class MonsterManager;
class Environment;
class EnvironmentManager;

// 游戏引擎状态
enum class GameEngineState : BYTE {
    STOPPED = 0,        // 已停止
    STARTING = 1,       // 启动中
    RUNNING = 2,        // 运行中
    STOPPING = 3,       // 停止中
    ERROR_STATE = 4     // 错误状态
};

// 游戏引擎配置
struct GameEngineConfig {
    // 服务器配置
    std::string serverName = "MirServer";
    WORD serverPort = 7000;
    int maxPlayers = 1000;

    // 游戏配置
    float expRate = 1.0f;           // 经验倍率
    float dropRate = 1.0f;          // 掉落倍率
    float goldRate = 1.0f;          // 金币倍率
    bool pkEnabled = true;          // 是否允许PK
    bool guildEnabled = true;       // 是否允许行会
    bool tradeEnabled = true;       // 是否允许交易

    // 性能配置
    int maxMonstersPerMap = 500;    // 每个地图最大怪物数
    int maxItemsPerMap = 1000;      // 每个地图最大物品数
    DWORD saveInterval = 300000;    // 保存间隔（毫秒）
    DWORD cleanupInterval = 60000;  // 清理间隔（毫秒）

    // 路径配置
    std::string dataPath = "GameData/";
    std::string mapPath = "Maps/";
    std::string logPath = "Logs/";
    std::string configPath = "Config/";
};

// 游戏引擎统计信息
struct GameEngineStats {
    // 运行时间
    DWORD startTime = 0;
    DWORD runTime = 0;

    // 玩家统计
    int onlinePlayers = 0;
    int maxOnlinePlayers = 0;
    int totalLogins = 0;

    // 对象统计
    int totalNPCs = 0;
    int totalMonsters = 0;
    int totalItems = 0;
    int totalMaps = 0;

    // 性能统计
    DWORD averageTickTime = 0;
    DWORD maxTickTime = 0;
    DWORD tickCount = 0;

    // 内存统计
    size_t memoryUsage = 0;

    DWORD lastUpdateTime = 0;
};

// 游戏引擎主类（对应delphi的TGameEngine）
class GameEngine : public Singleton<GameEngine> {
public:
    GameEngine();
    virtual ~GameEngine();

    // 初始化和清理
    bool Initialize();
    bool Initialize(const GameEngineConfig& config);
    void Finalize();

    // 启动和停止
    bool Start();
    void Stop();
    bool IsRunning() const { return m_state == GameEngineState::RUNNING; }
    GameEngineState GetState() const { return m_state; }

    // 配置管理
    bool LoadConfig(const std::string& configFile = "Config/GameEngine.ini");
    bool SaveConfig(const std::string& configFile = "Config/GameEngine.ini");
    const GameEngineConfig& GetConfig() const { return m_config; }
    void SetConfig(const GameEngineConfig& config) { m_config = config; }

    // 管理器访问
    UserEngine* GetUserEngine() const { return m_userEngine.get(); }
    MapManager* GetMapManager() const { return m_mapManager.get(); }
    ItemManager* GetItemManager() const { return m_itemManager.get(); }
    MagicManager* GetMagicManager() const { return m_magicManager.get(); }
    NPCManager* GetNPCManager() const { return m_npcManager.get(); }
    MonsterManager* GetMonsterManager() const { return m_monsterManager.get(); }
    EnvironmentManager* GetEnvironmentManager() const { return m_environmentManager.get(); }

    // 新的核心功能管理器访问
    class StorageManager* GetStorageManager() const { return m_storageManager.get(); }
    class TradeManager* GetTradeManager() const { return m_tradeManager.get(); }
    class QuestManager* GetQuestManager() const { return m_questManager.get(); }
    class MiniMapManager* GetMiniMapManager() const { return m_miniMapManager.get(); }
    class PKManager* GetPKManager() const;
    class RepairManager* GetRepairManager() const { return m_repairManager.get(); }
    class GroupManager* GetGroupManager() const;
    class GuildManager* GetGuildManager() const;
    class CastleManager* GetCastleManager() const;

    // 运行时控制
    void Run();                     // 主运行循环
    void Tick();                    // 单次tick处理
    void ProcessGameLogic();        // 处理游戏逻辑
    void ProcessNetworking();       // 处理网络
    void ProcessCleanup();          // 处理清理

    // 玩家管理
    bool AddPlayer(std::shared_ptr<class PlayObject> player);
    bool RemovePlayer(uint32_t playerId);
    std::shared_ptr<class PlayObject> FindPlayer(uint32_t playerId) const;
    std::shared_ptr<class PlayObject> FindPlayerByName(const std::string& playerName) const;
    std::vector<std::shared_ptr<class PlayObject>> GetAllPlayers() const;
    int GetOnlinePlayerCount() const;

    // 地图管理
    std::shared_ptr<Environment> GetEnvironment(const std::string& mapName) const;
    bool LoadMap(const std::string& mapName);
    void UnloadMap(const std::string& mapName);
    std::vector<std::string> GetLoadedMaps() const;

    // 游戏事件
    void OnPlayerLogin(std::shared_ptr<class PlayObject> player);
    void OnPlayerLogout(std::shared_ptr<class PlayObject> player);
    void OnPlayerDeath(std::shared_ptr<class PlayObject> player, class BaseObject* killer);
    void OnMonsterDeath(std::shared_ptr<class Monster> monster, class BaseObject* killer);
    void OnItemDrop(std::shared_ptr<class MapItem> item);

    // 系统命令
    bool ExecuteCommand(const std::string& command, const std::vector<std::string>& args);
    void BroadcastMessage(const std::string& message);
    void BroadcastSystemMessage(const std::string& message);

    // 统计信息
    const GameEngineStats& GetStatistics() const { return m_stats; }
    void UpdateStatistics();
    void ResetStatistics();

    // 保存和加载
    bool SaveAll();
    bool LoadAll();
    void SetAutoSave(bool enabled, DWORD interval = 300000);

    // 调试和监控
    void SetDebugMode(bool enabled) { m_debugMode = enabled; }
    bool IsDebugMode() const { return m_debugMode; }
    void DumpStatus() const;
    void DumpMemoryUsage() const;

private:
    // 内部初始化
    bool InitializeManagers();
    bool InitializeData();
    bool InitializeNetwork();

    // 内部清理
    void FinalizeManagers();
    void FinalizeData();
    void FinalizeNetwork();

    // 运行时处理
    void ProcessTick();
    void ProcessAutoSave();
    void ProcessStatistics();

    // 配置解析
    bool ParseConfigFile(const std::string& filename);
    bool WriteConfigFile(const std::string& filename);

    // 错误处理
    void HandleError(const std::string& error);
    void SetState(GameEngineState state);

private:
    // 状态
    std::atomic<GameEngineState> m_state;
    bool m_initialized;
    bool m_debugMode;

    // 配置
    GameEngineConfig m_config;

    // 管理器
    std::unique_ptr<UserEngine> m_userEngine;
    std::unique_ptr<MapManager> m_mapManager;
    std::unique_ptr<ItemManager> m_itemManager;
    std::unique_ptr<MagicManager> m_magicManager;
    std::unique_ptr<NPCManager> m_npcManager;
    std::unique_ptr<MonsterManager> m_monsterManager;
    std::unique_ptr<EnvironmentManager> m_environmentManager;

    // 新增核心功能管理器
    std::unique_ptr<class StorageManager> m_storageManager;
    std::unique_ptr<class TradeManager> m_tradeManager;
    std::unique_ptr<class QuestManager> m_questManager;
    std::unique_ptr<class MiniMapManager> m_miniMapManager;
    std::unique_ptr<class RepairManager> m_repairManager;

    // 运行时状态
    std::thread m_gameThread;
    std::atomic<bool> m_running;
    DWORD m_lastTickTime;
    DWORD m_lastSaveTime;
    DWORD m_lastCleanupTime;
    DWORD m_lastStatsTime;

    // 自动保存
    bool m_autoSaveEnabled;
    DWORD m_autoSaveInterval;

    // 统计信息
    GameEngineStats m_stats;

    // 线程安全
    mutable std::shared_mutex m_playersMutex;
    mutable std::mutex m_statsMutex;

    // 玩家列表
    std::unordered_map<uint32_t, std::shared_ptr<class PlayObject>> m_players;
    std::unordered_map<std::string, std::shared_ptr<class PlayObject>> m_playersByName;
};

// 全局游戏引擎实例访问
#define g_GameEngine GameEngine::GetInstance()

} // namespace MirServer

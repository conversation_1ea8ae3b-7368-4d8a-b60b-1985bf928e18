#pragma once

#include "../Common/Types.h"
#include "PacketTypes.h"
#include <functional>
#include <thread>
#include <atomic>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <unordered_map>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <unistd.h>
    #include <fcntl.h>
    #include <errno.h>
#endif

namespace MirServer {
namespace Network {

// 游戏数据包结构
struct GamePacket {
    std::vector<uint8_t> data;
    
    GamePacket() = default;
    GamePacket(const uint8_t* d, size_t size) : data(d, d + size) {}
};

// 前向声明
class ClientConnection;
class PacketHandler;

// 网络事件类型
enum class NetworkEventType {
    CLIENT_CONNECTED,
    CLIENT_DISCONNECTED,
    PACKET_RECEIVED,
    ERROR_OCCURRED
};

// 网络事件结构
struct NetworkEvent {
    NetworkEventType type;
    std::shared_ptr<ClientConnection> client;
    std::vector<uint8_t> data;
    std::string errorMsg;
};

// 网络管理器类
class NetworkManager {
public:
    NetworkManager();
    ~NetworkManager();

    // 初始化和清理
    bool Initialize();
    void Shutdown();

    // 服务器操作
    bool StartServer(uint16_t port, int maxConnections = 1000);
    void StopServer();
    bool IsRunning() const { return m_isRunning; }

    // 客户端管理
    void BroadcastPacket(const void* data, size_t size);
    void SendToClient(uint32_t clientId, const void* data, size_t size);
    void DisconnectClient(uint32_t clientId);
    size_t GetClientCount() const;
    
    // 事件处理
    using EventCallback = std::function<void(const NetworkEvent&)>;
    void SetEventCallback(EventCallback callback) { m_eventCallback = callback; }
    
    // 包处理器
    void SetPacketHandler(std::shared_ptr<PacketHandler> handler) { m_packetHandler = handler; }
    
    // 统计信息
    struct Statistics {
        uint64_t totalBytesReceived = 0;
        uint64_t totalBytesSent = 0;
        uint64_t totalPacketsReceived = 0;
        uint64_t totalPacketsSent = 0;
        uint32_t currentConnections = 0;
        uint32_t totalConnections = 0;
    };
    Statistics GetStatistics() const;

private:
    // 线程函数
    void AcceptThread();
    void IOThread();
    void ProcessEvents();
    
    // 辅助函数
    bool SetSocketNonBlocking(SOCKET_TYPE socket);
    bool SetSocketOptions(SOCKET_TYPE socket);
    void CloseSocket(SOCKET_TYPE socket);
    uint32_t GenerateClientId();
    
    // 事件队列操作
    void PushEvent(const NetworkEvent& event);
    bool PopEvent(NetworkEvent& event);
    
private:
    // 服务器套接字
    SOCKET_TYPE m_serverSocket = INVALID_SOCKET;
    uint16_t m_serverPort = 0;
    int m_maxConnections = 1000;
    
    // 运行状态
    std::atomic<bool> m_isRunning{false};
    std::atomic<bool> m_shouldStop{false};
    
    // 线程
    std::thread m_acceptThread;
    std::thread m_ioThread;
    std::thread m_eventThread;
    
    // 客户端管理
    std::unordered_map<uint32_t, std::shared_ptr<ClientConnection>> m_clients;
    std::mutex m_clientsMutex;
    std::atomic<uint32_t> m_nextClientId{1};
    
    // 事件队列
    std::queue<NetworkEvent> m_eventQueue;
    std::mutex m_eventMutex;
    std::condition_variable m_eventCV;
    
    // 回调和处理器
    EventCallback m_eventCallback;
    std::shared_ptr<PacketHandler> m_packetHandler;
    
    // 统计信息
    mutable std::mutex m_statsMutex;
    Statistics m_stats;
    
#ifdef _WIN32
    WSADATA m_wsaData;
#endif
};

// 客户端连接类
class ClientConnection {
public:
    ClientConnection(uint32_t id, SOCKET_TYPE socket, const std::string& remoteIP, uint16_t remotePort);
    ~ClientConnection();

    // 基本信息
    uint32_t GetId() const { return m_id; }
    SOCKET_TYPE GetSocket() const { return m_socket; }
    std::string GetRemoteIP() const { return m_remoteIP; }
    uint16_t GetRemotePort() const { return m_remotePort; }
    bool IsConnected() const { return m_connected; }
    
    // 数据操作
    bool Send(const void* data, size_t size);
    bool Receive();
    bool HasCompletePacket() const;
    bool GetPacket(std::vector<uint8_t>& packet);
    
    // 连接管理
    void Disconnect();
    void SetUserData(void* data) { m_userData = data; }
    void* GetUserData() const { return m_userData; }
    
    // 会话信息
    SessionInfo& GetSessionInfo() { return m_sessionInfo; }
    const SessionInfo& GetSessionInfo() const { return m_sessionInfo; }
    
    // 统计
    uint64_t GetBytesReceived() const { return m_bytesReceived; }
    uint64_t GetBytesSent() const { return m_bytesSent; }
    uint32_t GetLastActiveTime() const { return m_lastActiveTime; }

private:
    // 数据处理
    void ProcessReceivedData();
    bool ParsePacket();
    
private:
    uint32_t m_id;
    SOCKET_TYPE m_socket;
    std::string m_remoteIP;
    uint16_t m_remotePort;
    std::atomic<bool> m_connected{true};
    
    // 接收缓冲区
    static constexpr size_t RECV_BUFFER_SIZE = 65536;
    std::vector<uint8_t> m_recvBuffer;
    size_t m_recvSize = 0;
    
    // 发送缓冲区
    std::vector<uint8_t> m_sendBuffer;
    std::mutex m_sendMutex;
    
    // 数据包队列
    std::queue<std::vector<uint8_t>> m_packetQueue;
    std::mutex m_packetMutex;
    
    // 会话信息
    SessionInfo m_sessionInfo;
    void* m_userData = nullptr;
    
    // 统计信息
    std::atomic<uint64_t> m_bytesReceived{0};
    std::atomic<uint64_t> m_bytesSent{0};
    std::atomic<uint32_t> m_lastActiveTime{0};
};

// 包处理器接口
class PacketHandler {
public:
    virtual ~PacketHandler() = default;
    
    // 处理接收到的数据包
    virtual void HandlePacket(std::shared_ptr<ClientConnection> client, 
                            const Protocol::PacketHeader& header, 
                            const uint8_t* data, 
                            size_t dataSize) = 0;
    
    // 处理网络事件
    virtual void OnClientConnected(std::shared_ptr<ClientConnection> client) {}
    virtual void OnClientDisconnected(std::shared_ptr<ClientConnection> client) {}
    virtual void OnError(const std::string& error) {}
};

} // namespace Network
} // namespace MirServer 
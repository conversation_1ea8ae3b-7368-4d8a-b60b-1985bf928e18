// test_playobject_group_guild.cpp - 测试PlayObject的组队和行会功能
#include <gtest/gtest.h>
#include <memory>
#include "../src/BaseObject/PlayObject.h"
#include "../src/GameEngine/GroupManager.h"
#include "../src/GameEngine/GuildManager.h"
#include "../src/Common/Logger.h"

using namespace MirServer;

class PlayObjectGroupGuildTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化日志系统
        Logger::Initialize("test_playobject_group_guild.log");
        
        // 初始化管理器
        GroupManager::GetInstance().Initialize();
        GuildManager::GetInstance().Initialize();
        
        // 创建测试玩家
        player1 = std::make_shared<PlayObject>();
        player2 = std::make_shared<PlayObject>();
        player3 = std::make_shared<PlayObject>();
        
        // 设置玩家基本信息
        player1->SetCharName("TestPlayer1");
        player1->SetLevel(30);
        player1->SetCurrentPos({100, 100});
        player1->SetMapName("TestMap");
        
        player2->SetCharName("TestPlayer2");
        player2->SetLevel(32);
        player2->SetCurrentPos({102, 102});
        player2->SetMapName("TestMap");
        
        player3->SetCharName("TestPlayer3");
        player3->SetLevel(28);
        player3->SetCurrentPos({105, 105});
        player3->SetMapName("TestMap");
    }
    
    void TearDown() override {
        // 清理
        GroupManager::GetInstance().Finalize();
        GuildManager::GetInstance().Finalize();
        Logger::Finalize();
    }
    
    std::shared_ptr<PlayObject> player1;
    std::shared_ptr<PlayObject> player2;
    std::shared_ptr<PlayObject> player3;
};

// 测试组队功能
TEST_F(PlayObjectGroupGuildTest, TestGroupFunctionality) {
    auto& groupManager = GroupManager::GetInstance();
    
    // 初始状态：没有组队
    EXPECT_FALSE(player1->IsInGroup());
    EXPECT_FALSE(player1->IsGroupLeader());
    EXPECT_FALSE(player1->IsGroupMember(player2.get()));
    
    // 创建组队
    EXPECT_TRUE(groupManager.CreateGroup(player1.get()));
    
    // 验证队长状态
    EXPECT_TRUE(player1->IsInGroup());
    EXPECT_TRUE(player1->IsGroupLeader());
    
    // 添加队员
    auto group = groupManager.GetPlayerGroup(player1.get());
    ASSERT_NE(group, nullptr);
    EXPECT_TRUE(groupManager.JoinGroup(player2.get(), group->groupId));
    
    // 验证队员关系
    EXPECT_TRUE(player1->IsGroupMember(player2.get()));
    EXPECT_TRUE(player2->IsGroupMember(player1.get()));
    EXPECT_TRUE(player2->IsInGroup());
    EXPECT_FALSE(player2->IsGroupLeader());
    
    // 验证组队成员列表
    auto members = player1->GetGroupMembers();
    EXPECT_EQ(members.size(), 2);
    
    // 测试组队经验分享
    DWORD initialExp1 = player1->GetExp();
    DWORD initialExp2 = player2->GetExp();
    
    // 玩家1获得经验，应该被分享
    player1->GainExp(1000);
    
    // 验证经验分享（平均分配）
    EXPECT_GT(player1->GetExp(), initialExp1);
    EXPECT_GT(player2->GetExp(), initialExp2);
    
    // 测试离开组队
    EXPECT_TRUE(groupManager.LeaveGroup(player2.get()));
    EXPECT_FALSE(player2->IsInGroup());
    EXPECT_FALSE(player1->IsGroupMember(player2.get()));
}

// 测试行会功能
TEST_F(PlayObjectGroupGuildTest, TestGuildFunctionality) {
    auto& guildManager = GuildManager::GetInstance();
    
    // 初始状态：没有行会
    EXPECT_TRUE(player1->GetGuildName().empty());
    EXPECT_FALSE(player1->IsGuildMember(player2.get()));
    
    // 创建行会
    EXPECT_TRUE(guildManager.CreateGuild("TestGuild", player1.get()));
    
    // 验证行会信息
    EXPECT_EQ(player1->GetGuildName(), "TestGuild");
    EXPECT_EQ(player1->GetGuildRank(), static_cast<BYTE>(GuildRank::CHIEF));
    
    // 添加行会成员
    auto* guild = guildManager.FindGuild("TestGuild");
    ASSERT_NE(guild, nullptr);
    EXPECT_TRUE(guild->AddMember(player2.get(), GuildRank::MEMBER));
    
    // 更新玩家2的行会信息
    player2->SetGuildInfo("TestGuild", static_cast<BYTE>(GuildRank::MEMBER));
    
    // 验证行会成员关系
    EXPECT_TRUE(player1->IsGuildMember(player2.get()));
    EXPECT_TRUE(player2->IsGuildMember(player1.get()));
    EXPECT_FALSE(player1->IsGuildEnemy(player2.get()));
    
    // 测试IsProperFriend（行会成员应该是朋友）
    EXPECT_TRUE(player1->IsProperFriend(player2.get()));
    
    // 清理行会信息
    player1->ClearGuildInfo();
    EXPECT_TRUE(player1->GetGuildName().empty());
    EXPECT_EQ(player1->GetGuildRank(), 0);
}

// 测试登录登出逻辑
TEST_F(PlayObjectGroupGuildTest, TestLoginLogoutIntegration) {
    auto& groupManager = GroupManager::GetInstance();
    auto& guildManager = GuildManager::GetInstance();
    
    // 创建组队和行会
    EXPECT_TRUE(groupManager.CreateGroup(player1.get()));
    EXPECT_TRUE(guildManager.CreateGuild("TestGuild", player1.get()));
    
    // 模拟登录
    player1->LoginGame();
    
    // 验证登录后状态
    EXPECT_TRUE(player1->IsInGroup());
    EXPECT_EQ(player1->GetGuildName(), "TestGuild");
    
    // 模拟登出
    player1->LogoutGame();
    
    // 验证登出处理
    // 注意：实际的清理可能在Finalize中进行
}

// 测试Finalize清理逻辑
TEST_F(PlayObjectGroupGuildTest, TestFinalizeCleanup) {
    auto& groupManager = GroupManager::GetInstance();
    
    // 创建组队
    EXPECT_TRUE(groupManager.CreateGroup(player1.get()));
    auto group = groupManager.GetPlayerGroup(player1.get());
    ASSERT_NE(group, nullptr);
    EXPECT_TRUE(groupManager.JoinGroup(player2.get(), group->groupId));
    EXPECT_TRUE(groupManager.JoinGroup(player3.get(), group->groupId));
    
    // 验证组队状态
    EXPECT_EQ(group->members.size(), 3);
    EXPECT_TRUE(player1->IsGroupLeader());
    
    // 队长下线（调用Finalize）
    player1->Finalize();
    
    // 验证队长转移
    group = groupManager.GetPlayerGroup(player2.get());
    if (group) {
        // 应该有新的队长
        bool hasLeader = false;
        for (auto& member : group->members) {
            if (member && groupManager.IsGroupLeader(member.get())) {
                hasLeader = true;
                break;
            }
        }
        EXPECT_TRUE(hasLeader);
    }
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

// Environment.cpp - 环境系统实现
#include "Environment.h"
#include "MapManager.h"
#include "../BaseObject/PlayObject.h"
#include "../BaseObject/Monster.h"
#include "../BaseObject/NPC.h"
#include "../Common/Logger.h"
#include <algorithm>
#include <queue>
#include <random>
#include <sstream>

namespace MirServer {

// 环境类实现
Environment::Environment(const std::string& mapName, int width, int height)
    : m_mapName(mapName), m_width(width), m_height(height) {

    // 初始化地图单元格
    m_cells.resize(height);
    for (int y = 0; y < height; y++) {
        m_cells[y].resize(width);
    }

    // 初始化时间
    DWORD currentTime = GetCurrentTime();
    m_lastRunTime = currentTime;
    m_lastCleanupTime = currentTime;
    m_lastSpawnCheckTime = currentTime;
    m_lastEventProcessTime = currentTime;

    // 初始化计数器
    m_humanCount = 0;
    m_monsterCount = 0;

    // 设置默认地图名称
    m_subMapName = mapName;

    Logger::Info("Environment created: " + mapName + " (" +
                 std::to_string(width) + "x" + std::to_string(height) + ")");
}

Environment::~Environment() {
    // 清理所有对象
    m_objects.clear();
    Logger::Info("Environment destroyed: " + m_mapName);
}

size_t Environment::GetObjectCount() const {
    std::shared_lock<std::shared_mutex> lock(m_objectsMutex);
    return m_objects.size();
}

bool Environment::CanWalk(int x, int y) const {
    const MapCell* cell = GetCell(x, y);
    if (!cell) return false;

    // 检查地形是否可行走
    if (!cell->canWalk) return false;

    // 检查是否有阻挡对象
    for (const auto& obj : cell->objects) {
        if (obj && obj->CanBlock()) {
            return false;
        }
    }

    return true;
}

bool Environment::CanFly(int x, int y) const {
    const MapCell* cell = GetCell(x, y);
    return cell && cell->canFly;
}

bool Environment::CanStand(int x, int y) const {
    const MapCell* cell = GetCell(x, y);
    return cell && cell->canStand;
}

void Environment::SetCellAttribute(int x, int y, bool canWalk, bool canFly, bool canStand) {
    MapCell* cell = GetCell(x, y);
    if (cell) {
        cell->canWalk = canWalk;
        cell->canFly = canFly;
        cell->canStand = canStand;
    }
}

bool Environment::AddObject(std::shared_ptr<BaseObject> obj) {
    if (!obj) return false;

    Point pos = obj->GetCurrentPos();
    if (!IsValidPosition(pos.x, pos.y)) {
        Logger::Error("Invalid position for object: " + obj->GetCharName());
        return false;
    }

    {
        std::unique_lock<std::shared_mutex> lock(m_objectsMutex);

        // 添加到对象列表
        m_objects[obj->GetObjectId()] = obj;

        // 添加到单元格
        MapCell* cell = GetCell(pos.x, pos.y);
        if (cell) {
            cell->objects.push_back(obj);
        }
    }

    // 更新对象视野
    UpdateObjectView(obj);

    return true;
}

bool Environment::RemoveObject(std::shared_ptr<BaseObject> obj) {
    if (!obj) return false;

    Point pos = obj->GetCurrentPos();

    {
        std::unique_lock<std::shared_mutex> lock(m_objectsMutex);

        // 从对象列表移除
        m_objects.erase(obj->GetObjectId());

        // 从单元格移除
        MapCell* cell = GetCell(pos.x, pos.y);
        if (cell) {
            auto it = std::find(cell->objects.begin(), cell->objects.end(), obj);
            if (it != cell->objects.end()) {
                cell->objects.erase(it);
            }
        }
    }

    // 通知其他对象该对象消失
    auto observers = GetObjectsInRange(pos, obj->GetViewRange());
    for (const auto& observer : observers) {
        if (observer && observer != obj) {
            NotifyObjectDisappear(obj, observer);
        }
    }

    return true;
}

bool Environment::MoveObject(std::shared_ptr<BaseObject> obj, const Point& newPos) {
    if (!obj) return false;

    Point oldPos = obj->GetCurrentPos();
    if (oldPos.x == newPos.x && oldPos.y == newPos.y) {
        return true; // 位置没有改变
    }

    if (!IsValidPosition(newPos.x, newPos.y)) {
        return false;
    }

    {
        std::unique_lock<std::shared_mutex> lock(m_objectsMutex);

        // 从旧位置移除
        MapCell* oldCell = GetCell(oldPos.x, oldPos.y);
        if (oldCell) {
            auto it = std::find(oldCell->objects.begin(), oldCell->objects.end(), obj);
            if (it != oldCell->objects.end()) {
                oldCell->objects.erase(it);
            }
        }

        // 添加到新位置
        MapCell* newCell = GetCell(newPos.x, newPos.y);
        if (newCell) {
            newCell->objects.push_back(obj);
        }
    }

    // 通知移动
    auto observers = GetObjectsInRange(newPos, obj->GetViewRange() * 2);
    for (const auto& observer : observers) {
        if (observer && observer != obj) {
            NotifyObjectMove(obj, oldPos, observer);
        }
    }

    return true;
}

std::vector<std::shared_ptr<BaseObject>> Environment::GetObjectsAt(const Point& pos) const {
    std::shared_lock<std::shared_mutex> lock(m_objectsMutex);

    const MapCell* cell = GetCell(pos.x, pos.y);
    if (cell) {
        return cell->objects;
    }

    return {};
}

std::vector<std::shared_ptr<BaseObject>> Environment::GetObjectsInRange(const Point& center, int range) const {
    std::shared_lock<std::shared_mutex> lock(m_objectsMutex);

    std::vector<std::shared_ptr<BaseObject>> result;

    // 计算范围
    int minX = std::max(0, center.x - range);
    int maxX = std::min(m_width - 1, center.x + range);
    int minY = std::max(0, center.y - range);
    int maxY = std::min(m_height - 1, center.y + range);

    // 遍历范围内的单元格
    for (int y = minY; y <= maxY; y++) {
        for (int x = minX; x <= maxX; x++) {
            // 检查是否在圆形范围内
            if (GetDistance(Point(x, y), center) <= range) {
                const MapCell* cell = GetCell(x, y);
                if (cell) {
                    result.insert(result.end(), cell->objects.begin(), cell->objects.end());
                }
            }
        }
    }

    // 去重
    std::sort(result.begin(), result.end());
    result.erase(std::unique(result.begin(), result.end()), result.end());

    return result;
}

std::shared_ptr<BaseObject> Environment::GetObjectAt(const Point& pos,
    std::function<bool(const std::shared_ptr<BaseObject>&)> filter) const {

    auto objects = GetObjectsAt(pos);
    for (const auto& obj : objects) {
        if (filter(obj)) {
            return obj;
        }
    }

    return nullptr;
}

std::vector<std::shared_ptr<PlayObject>> Environment::GetPlayersInRange(const Point& center, int range) const {
    auto objects = GetObjectsInRange(center, range);
    std::vector<std::shared_ptr<PlayObject>> players;

    for (const auto& obj : objects) {
        if (obj && obj->GetObjectType() == ObjectType::HUMAN) {
            players.push_back(std::static_pointer_cast<PlayObject>(obj));
        }
    }

    return players;
}

size_t Environment::GetPlayerCount() const {
    std::shared_lock<std::shared_mutex> lock(m_objectsMutex);

    size_t count = 0;
    for (const auto& pair : m_objects) {
        if (pair.second && pair.second->GetObjectType() == ObjectType::HUMAN) {
            count++;
        }
    }

    return count;
}

void Environment::BroadcastInRange(const Point& center, int range, const std::vector<uint8_t>& packet) {
    auto players = GetPlayersInRange(center, range);
    for (const auto& player : players) {
        if (player) {
            player->SendPacket(packet);
        }
    }
}

std::vector<std::shared_ptr<Monster>> Environment::GetMonstersInRange(const Point& center, int range) const {
    auto objects = GetObjectsInRange(center, range);
    std::vector<std::shared_ptr<Monster>> monsters;

    for (const auto& obj : objects) {
        if (obj && obj->GetObjectType() == ObjectType::MONSTER) {
            monsters.push_back(std::static_pointer_cast<Monster>(obj));
        }
    }

    return monsters;
}

size_t Environment::GetMonsterCount() const {
    std::shared_lock<std::shared_mutex> lock(m_objectsMutex);

    size_t count = 0;
    for (const auto& pair : m_objects) {
        if (pair.second && pair.second->GetObjectType() == ObjectType::MONSTER) {
            count++;
        }
    }

    return count;
}

bool Environment::SpawnMonster(std::shared_ptr<Monster> monster, const Point& pos) {
    if (!monster || !IsValidPosition(pos.x, pos.y)) {
        return false;
    }

    // 设置位置
    monster->SetCurrentPos(pos);
    monster->SetMapName(m_mapName);

    // 添加到环境
    return AddObject(monster);
}

void Environment::ClearMonsters() {
    std::unique_lock<std::shared_mutex> lock(m_objectsMutex);

    std::vector<uint32_t> toRemove;
    for (const auto& pair : m_objects) {
        if (pair.second && pair.second->GetObjectType() == ObjectType::MONSTER) {
            toRemove.push_back(pair.first);
        }
    }

    for (uint32_t id : toRemove) {
        m_objects.erase(id);
    }
}

std::vector<std::shared_ptr<NPC>> Environment::GetNPCsInRange(const Point& center, int range) const {
    auto objects = GetObjectsInRange(center, range);
    std::vector<std::shared_ptr<NPC>> npcs;

    for (const auto& obj : objects) {
        if (obj && obj->GetObjectType() == ObjectType::NPC) {
            npcs.push_back(std::static_pointer_cast<NPC>(obj));
        }
    }

    return npcs;
}

bool Environment::AddNPC(std::shared_ptr<NPC> npc, const Point& pos) {
    if (!npc || !IsValidPosition(pos.x, pos.y)) {
        return false;
    }

    npc->SetCurrentPos(pos);
    npc->SetMapName(m_mapName);

    return AddObject(npc);
}

bool Environment::DropItem(const UserItem& item, const Point& pos, std::shared_ptr<BaseObject> owner) {
    // TODO: 实现物品掉落
    return false;
}

bool Environment::DropGold(DWORD amount, const Point& pos, std::shared_ptr<BaseObject> owner) {
    // TODO: 实现金币掉落
    return false;
}

void Environment::CleanupDroppedItems() {
    // TODO: 清理过期的掉落物品
}

void Environment::AddMapEvent(const MapEvent& event) {
    std::lock_guard<std::mutex> lock(m_eventsMutex);
    m_mapEvents.push_back(event);
}

void Environment::RemoveMapEvent(MapEventType type, const Point& pos) {
    std::lock_guard<std::mutex> lock(m_eventsMutex);

    m_mapEvents.erase(
        std::remove_if(m_mapEvents.begin(), m_mapEvents.end(),
            [type, pos](const MapEvent& event) {
                return event.type == type && event.position.x == pos.x && event.position.y == pos.y;
            }),
        m_mapEvents.end()
    );
}

bool Environment::HasMapEvent(MapEventType type, const Point& pos) const {
    std::lock_guard<std::mutex> lock(m_eventsMutex);

    for (const auto& event : m_mapEvents) {
        if (event.type == type) {
            int distance = GetDistance(event.position, pos);
            if (distance <= event.range) {
                return true;
            }
        }
    }

    return false;
}

std::vector<MapEvent> Environment::GetMapEventsAt(const Point& pos) const {
    std::lock_guard<std::mutex> lock(m_eventsMutex);

    std::vector<MapEvent> result;
    for (const auto& event : m_mapEvents) {
        int distance = GetDistance(event.position, pos);
        if (distance <= event.range) {
            result.push_back(event);
        }
    }

    return result;
}

bool Environment::FindPath(const Point& start, const Point& end, std::vector<Point>& path, int maxSteps) const {
    path.clear();

    if (!IsValidPosition(start.x, start.y) || !IsValidPosition(end.x, end.y)) {
        return false;
    }

    if (start.x == end.x && start.y == end.y) {
        path.push_back(start);
        return true;
    }

    // A*寻路算法
    auto comp = [](const PathNode* a, const PathNode* b) {
        return a->f() > b->f();
    };

    std::priority_queue<PathNode*, std::vector<PathNode*>, decltype(comp)> openList(comp);
    std::vector<std::vector<PathNode*>> nodeMap(m_height, std::vector<PathNode*>(m_width, nullptr));
    std::vector<std::unique_ptr<PathNode>> nodePool;

    // 创建起始节点
    auto startNode = std::make_unique<PathNode>();
    startNode->pos = start;
    startNode->g = 0;
    startNode->h = CalculateHeuristic(start, end);
    startNode->parent = nullptr;

    nodeMap[start.y][start.x] = startNode.get();
    openList.push(startNode.get());
    nodePool.push_back(std::move(startNode));

    // 8个方向
    const int dx[] = {0, 1, 1, 1, 0, -1, -1, -1};
    const int dy[] = {-1, -1, 0, 1, 1, 1, 0, -1};

    while (!openList.empty() && nodePool.size() < static_cast<size_t>(maxSteps)) {
        PathNode* current = openList.top();
        openList.pop();

        // 到达目标
        if (current->pos.x == end.x && current->pos.y == end.y) {
            path = ReconstructPath(current);
            return true;
        }

        // 检查相邻节点
        for (int i = 0; i < 8; i++) {
            int nx = current->pos.x + dx[i];
            int ny = current->pos.y + dy[i];

            if (!IsValidPosition(nx, ny) || !CanWalk(nx, ny)) {
                continue;
            }

            int g = current->g + ((i % 2 == 0) ? 10 : 14); // 直线10，斜线14

            PathNode* neighbor = nodeMap[ny][nx];
            if (neighbor && g >= neighbor->g) {
                continue;
            }

            if (!neighbor) {
                auto newNode = std::make_unique<PathNode>();
                newNode->pos = Point(nx, ny);
                newNode->g = g;
                newNode->h = CalculateHeuristic(newNode->pos, end);
                newNode->parent = current;

                neighbor = newNode.get();
                nodeMap[ny][nx] = neighbor;
                openList.push(neighbor);
                nodePool.push_back(std::move(newNode));
            } else {
                neighbor->g = g;
                neighbor->parent = current;
            }
        }
    }

    return false;
}

Point Environment::GetRandomWalkablePoint(const Point& center, int range) const {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> distX(-range, range);
    std::uniform_int_distribution<> distY(-range, range);

    const int maxAttempts = 100;
    for (int i = 0; i < maxAttempts; i++) {
        int x = center.x + distX(gen);
        int y = center.y + distY(gen);

        if (IsValidPosition(x, y) && CanWalk(x, y)) {
            return Point(x, y);
        }
    }

    return center;
}

void Environment::Run() {
    DWORD currentTime = GetCurrentTime();

    // 处理对象
    ProcessObjects();

    // 处理地图事件
    ProcessMapEvents();

    // 检查刷新点
    if (currentTime - m_lastSpawnCheckTime >= m_spawnCheckInterval) {
        CheckRespawns();
        m_lastSpawnCheckTime = currentTime;
    }

    // 清理掉落物品
    if (currentTime - m_lastCleanupTime >= m_cleanupInterval) {
        CleanupDroppedItems();
        m_lastCleanupTime = currentTime;
    }

    m_lastRunTime = currentTime;
}

void Environment::ProcessObjects() {
    std::shared_lock<std::shared_mutex> lock(m_objectsMutex);

    for (const auto& pair : m_objects) {
        if (pair.second) {
            pair.second->Run();
        }
    }
}

void Environment::ProcessMapEvents() {
    DWORD currentTime = GetCurrentTime();

    // 检查是否需要处理地图事件
    if (currentTime - m_lastEventProcessTime < m_eventProcessInterval) {
        return;
    }

    // 处理地图特效
    ProcessMapEffects();

    // 处理自动加减血
    ProcessAutoHP();

    // 处理自动加减金币
    ProcessAutoGameGold();

    // 处理自动加减点数
    ProcessAutoGamePoint();

    // 处理PK效果
    ProcessPKEffects();

    m_lastEventProcessTime = currentTime;
}

void Environment::CheckRespawns() {
    ProcessSpawnPoints();
}

bool Environment::IsSafeZone(const Point& pos) const {
    return HasMapEvent(MapEventType::SAFE_ZONE, pos);
}

bool Environment::IsFightZone(const Point& pos) const {
    return HasMapEvent(MapEventType::FIGHT_ZONE, pos);
}

bool Environment::IsNoReconnectZone(const Point& pos) const {
    return HasMapEvent(MapEventType::NO_RECONNECT, pos);
}

void Environment::AddPortal(const Portal& portal) {
    std::lock_guard<std::mutex> lock(m_portalsMutex);
    m_portals.push_back(portal);
}

const Environment::Portal* Environment::GetPortalAt(const Point& pos) const {
    std::lock_guard<std::mutex> lock(m_portalsMutex);

    for (const auto& portal : m_portals) {
        if (portal.sourcePos.x == pos.x && portal.sourcePos.y == pos.y) {
            return &portal;
        }
    }

    return nullptr;
}

void Environment::AddSpawnPoint(const SpawnPoint& spawn) {
    std::lock_guard<std::mutex> lock(m_spawnMutex);
    m_spawnPoints.push_back(spawn);
}

void Environment::ProcessSpawnPoints() {
    std::lock_guard<std::mutex> lock(m_spawnMutex);

    DWORD currentTime = GetCurrentTime();

    for (auto& spawn : m_spawnPoints) {
        if (currentTime - spawn.lastSpawnTime >= spawn.respawnTime) {
            // 检查当前区域的怪物数量
            auto monsters = GetMonstersInRange(spawn.position, spawn.range);
            int currentCount = 0;

            for (const auto& monster : monsters) {
                if (monster && monster->GetCharName() == spawn.monsterName) {
                    currentCount++;
                }
            }

            // 生成不足的怪物
            int toSpawn = spawn.count - currentCount;
            for (int i = 0; i < toSpawn; i++) {
                Point spawnPos = GetRandomWalkablePoint(spawn.position, spawn.range);

                // TODO: 从怪物数据库创建怪物
                // auto monster = CreateMonster(spawn.monsterName);
                // SpawnMonster(monster, spawnPos);
            }

            spawn.lastSpawnTime = currentTime;
        }
    }
}

void Environment::DumpMapInfo() const {
    Logger::Info("=== Environment Info: " + m_mapName + " ===");
    Logger::Info("Size: " + std::to_string(m_width) + "x" + std::to_string(m_height));

    auto stats = GetStatistics();
    Logger::Info("Total Objects: " + std::to_string(stats.totalObjects));
    Logger::Info("Players: " + std::to_string(stats.playerCount));
    Logger::Info("Monsters: " + std::to_string(stats.monsterCount));
    Logger::Info("NPCs: " + std::to_string(stats.npcCount));
    Logger::Info("Items: " + std::to_string(stats.itemCount));
    Logger::Info("Events: " + std::to_string(stats.eventCount));
}

Environment::Statistics Environment::GetStatistics() const {
    Statistics stats;

    {
        std::shared_lock<std::shared_mutex> lock(m_objectsMutex);
        stats.totalObjects = m_objects.size();

        for (const auto& pair : m_objects) {
            if (pair.second) {
                switch (pair.second->GetObjectType()) {
                    case ObjectType::HUMAN:
                        stats.playerCount++;
                        break;
                    case ObjectType::MONSTER:
                        stats.monsterCount++;
                        break;
                    case ObjectType::NPC:
                        stats.npcCount++;
                        break;
                    case ObjectType::ITEM:
                        stats.itemCount++;
                        break;
                    default:
                        break;
                }
            }
        }
    }

    {
        std::lock_guard<std::mutex> lock(m_eventsMutex);
        stats.eventCount = m_mapEvents.size();
    }

    return stats;
}

// 内部方法实现
bool Environment::IsValidPosition(int x, int y) const {
    return x >= 0 && x < m_width && y >= 0 && y < m_height;
}

MapCell* Environment::GetCell(int x, int y) {
    if (!IsValidPosition(x, y)) return nullptr;
    return &m_cells[y][x];
}

const MapCell* Environment::GetCell(int x, int y) const {
    if (!IsValidPosition(x, y)) return nullptr;
    return &m_cells[y][x];
}

void Environment::UpdateObjectView(std::shared_ptr<BaseObject> obj) {
    if (!obj) return;

    // 获取视野内的对象
    auto viewObjects = GetObjectsInRange(obj->GetCurrentPos(), obj->GetViewRange());

    for (const auto& viewObj : viewObjects) {
        if (viewObj && viewObj != obj) {
            // 通知对象出现
            NotifyObjectAppear(viewObj, obj);

            // 如果是玩家，也要通知对方
            if (viewObj->GetObjectType() == ObjectType::HUMAN) {
                NotifyObjectAppear(obj, viewObj);
            }
        }
    }
}

void Environment::NotifyObjectAppear(std::shared_ptr<BaseObject> obj, std::shared_ptr<BaseObject> observer) {
    if (!obj || !observer) return;

    // TODO: 根据对象类型发送不同的出现消息
    if (observer->GetObjectType() == ObjectType::HUMAN) {
        auto player = std::static_pointer_cast<PlayObject>(observer);
        // player->SendObjectAppear(obj);
    }
}

void Environment::NotifyObjectDisappear(std::shared_ptr<BaseObject> obj, std::shared_ptr<BaseObject> observer) {
    if (!obj || !observer) return;

    // TODO: 发送对象消失消息
    if (observer->GetObjectType() == ObjectType::HUMAN) {
        auto player = std::static_pointer_cast<PlayObject>(observer);
        // player->SendObjectDisappear(obj);
    }
}

void Environment::NotifyObjectMove(std::shared_ptr<BaseObject> obj, const Point& oldPos,
                                 std::shared_ptr<BaseObject> observer) {
    if (!obj || !observer) return;

    // TODO: 发送对象移动消息
    if (observer->GetObjectType() == ObjectType::HUMAN) {
        auto player = std::static_pointer_cast<PlayObject>(observer);
        // player->SendObjectMove(obj, oldPos);
    }
}

int Environment::CalculateHeuristic(const Point& from, const Point& to) const {
    int dx = std::abs(from.x - to.x);
    int dy = std::abs(from.y - to.y);
    return (dx + dy) * 10; // 曼哈顿距离
}

std::vector<Point> Environment::ReconstructPath(PathNode* node) const {
    std::vector<Point> path;

    while (node) {
        path.push_back(node->pos);
        node = node->parent;
    }

    std::reverse(path.begin(), path.end());
    return path;
}

// EnvironmentManager实现
EnvironmentManager& EnvironmentManager::Instance() {
    static EnvironmentManager instance;
    return instance;
}

std::shared_ptr<Environment> EnvironmentManager::CreateEnvironment(const std::string& mapName,
                                                                 int width, int height) {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    auto env = std::make_shared<Environment>(mapName, width, height);
    m_environments[mapName] = env;

    return env;
}

std::shared_ptr<Environment> EnvironmentManager::GetEnvironment(const std::string& mapName) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);

    auto it = m_environments.find(mapName);
    if (it != m_environments.end()) {
        return it->second;
    }

    return nullptr;
}

void EnvironmentManager::RemoveEnvironment(const std::string& mapName) {
    std::unique_lock<std::shared_mutex> lock(m_mutex);
    m_environments.erase(mapName);
}

std::vector<std::string> EnvironmentManager::GetAllMapNames() const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);

    std::vector<std::string> names;
    names.reserve(m_environments.size());

    for (const auto& pair : m_environments) {
        names.push_back(pair.first);
    }

    return names;
}

void EnvironmentManager::RunAll() {
    std::shared_lock<std::shared_mutex> lock(m_mutex);

    for (const auto& pair : m_environments) {
        if (pair.second) {
            pair.second->Run();
        }
    }
}

// 新增方法实现

// 设置地图标志
void Environment::SetMapFlags(const MapFlags& flags) {
    m_mapFlags = flags;

    // 更新禁用物品列表
    if (flags.unAllowStdItems && !flags.unAllowStdItemsText.empty()) {
        UpdateUnAllowItemsList();
    }
}

// 地图区域检查方法实现
bool Environment::IsFight3Zone(const Point& pos) const {
    return m_mapFlags.isFight3Zone || HasMapEvent(MapEventType::FIGHT3_ZONE, pos);
}

bool Environment::IsNoRandomZone(const Point& pos) const {
    return m_mapFlags.noRandomMove || HasMapEvent(MapEventType::NO_RANDOM, pos);
}

bool Environment::IsNoDrugZone(const Point& pos) const {
    return m_mapFlags.noDrug || HasMapEvent(MapEventType::NO_DRUG, pos);
}

bool Environment::IsMineZone(const Point& pos) const {
    return m_mapFlags.isMine || HasMapEvent(MapEventType::MINE_ZONE, pos);
}

bool Environment::IsNoPositionMoveZone(const Point& pos) const {
    return m_mapFlags.noPositionMove || HasMapEvent(MapEventType::NO_POSITION_MOVE, pos);
}

bool Environment::IsNoRecallZone(const Point& pos) const {
    return m_mapFlags.noRecall || HasMapEvent(MapEventType::NO_RECALL, pos);
}

bool Environment::IsNoGuildRecallZone(const Point& pos) const {
    return m_mapFlags.noGuildRecall || HasMapEvent(MapEventType::NO_GUILD_RECALL, pos);
}

bool Environment::IsNoDearRecallZone(const Point& pos) const {
    return m_mapFlags.noDearRecall || HasMapEvent(MapEventType::NO_DEAR_RECALL, pos);
}

bool Environment::IsNoMasterRecallZone(const Point& pos) const {
    return m_mapFlags.noMasterRecall || HasMapEvent(MapEventType::NO_MASTER_RECALL, pos);
}

bool Environment::IsQuizZone(const Point& pos) const {
    return m_mapFlags.isQuiz || HasMapEvent(MapEventType::QUIZ_ZONE, pos);
}

bool Environment::IsNeedHoleZone(const Point& pos) const {
    return m_mapFlags.needHole || HasMapEvent(MapEventType::NEED_HOLE, pos);
}

bool Environment::IsDarknessZone(const Point& pos) const {
    return m_mapFlags.isDark || HasMapEvent(MapEventType::DARKNESS, pos);
}

bool Environment::IsDaylightZone(const Point& pos) const {
    return m_mapFlags.isDay || HasMapEvent(MapEventType::DAYLIGHT, pos);
}

bool Environment::IsRunHumanZone(const Point& pos) const {
    return m_mapFlags.runHuman || HasMapEvent(MapEventType::RUN_HUMAN, pos);
}

bool Environment::IsRunMonsterZone(const Point& pos) const {
    return m_mapFlags.runMonster || HasMapEvent(MapEventType::RUN_MONSTER, pos);
}

bool Environment::IsNoFireMagicZone(const Point& pos) const {
    return m_mapFlags.noFireMagic || HasMapEvent(MapEventType::NO_FIRE_MAGIC, pos);
}

// 物品限制检查（对应原版AllowStdItems）
bool Environment::AllowStdItems(const std::string& itemName) const {
    if (!m_mapFlags.unAllowStdItems) {
        return true;
    }

    std::lock_guard<std::mutex> lock(m_unAllowItemsMutex);

    for (const auto& name : m_unAllowStdItemsList) {
        if (name == itemName) {
            return false;
        }
    }

    return true;
}

bool Environment::AllowStdItems(int itemIdx) const {
    if (!m_mapFlags.unAllowStdItems) {
        return true;
    }

    std::lock_guard<std::mutex> lock(m_unAllowItemsMutex);

    for (int idx : m_unAllowStdItemsIdxList) {
        if (idx == itemIdx) {
            return false;
        }
    }

    return true;
}

// 地图环境信息（对应原版GetEnvirInfo）
std::string Environment::GetEnvironmentInfo() const {
    std::ostringstream oss;

    oss << "地图名称: " << m_mapName << "\n";
    oss << "地图描述: " << m_mapDesc << "\n";
    oss << "地图大小: " << m_width << "x" << m_height << "\n";
    oss << "服务器索引: " << m_serverIndex << "\n";
    oss << "需要等级: " << m_requestLevel << "\n";
    oss << "小地图: " << m_minMap << "\n";
    oss << "主地图: " << (m_isMainMap ? "是" : "否") << "\n";

    // 地图标志信息
    oss << "\n地图标志:\n";
    if (m_mapFlags.isSafe) oss << "- 安全区\n";
    if (m_mapFlags.isFightZone) oss << "- 战斗区\n";
    if (m_mapFlags.isFight3Zone) oss << "- 行会战区\n";
    if (m_mapFlags.isDark) oss << "- 黑暗\n";
    if (m_mapFlags.isDay) oss << "- 白天\n";
    if (m_mapFlags.isQuiz) oss << "- 答题区\n";
    if (m_mapFlags.noReconnect) oss << "- 禁止重连\n";
    if (m_mapFlags.needHole) oss << "- 需要挖洞\n";
    if (m_mapFlags.noRecall) oss << "- 禁止召回\n";
    if (m_mapFlags.noGuildRecall) oss << "- 禁止行会召回\n";
    if (m_mapFlags.noDearRecall) oss << "- 禁止夫妻召回\n";
    if (m_mapFlags.noMasterRecall) oss << "- 禁止师父召回\n";
    if (m_mapFlags.noRandomMove) oss << "- 禁止随机传送\n";
    if (m_mapFlags.noDrug) oss << "- 禁止使用药品\n";
    if (m_mapFlags.isMine) oss << "- 矿区\n";
    if (m_mapFlags.noPositionMove) oss << "- 禁止使用移动符\n";
    if (m_mapFlags.runHuman) oss << "- 允许人物跑步\n";
    if (m_mapFlags.runMonster) oss << "- 允许怪物跑步\n";
    if (m_mapFlags.incHP) oss << "- 自动加血\n";
    if (m_mapFlags.decHP) oss << "- 自动减血\n";
    if (m_mapFlags.incGameGold) oss << "- 自动加金币\n";
    if (m_mapFlags.decGameGold) oss << "- 自动减金币\n";
    if (m_mapFlags.incGamePoint) oss << "- 自动加点数\n";
    if (m_mapFlags.decGamePoint) oss << "- 自动减点数\n";
    if (m_mapFlags.hasMusic) oss << "- 音乐区 (ID: " << m_mapFlags.musicID << ")\n";
    if (m_mapFlags.expRate) oss << "- 经验倍率: " << m_mapFlags.expRateValue << "%\n";
    if (m_mapFlags.noFireMagic) oss << "- 禁止火墙魔法\n";
    if (m_mapFlags.unAllowStdItems) oss << "- 禁止使用指定物品\n";

    // 对象统计
    oss << "\n对象统计:\n";
    oss << "- 玩家数量: " << m_humanCount << "\n";
    oss << "- 怪物数量: " << m_monsterCount << "\n";
    oss << "- 总对象数: " << m_objects.size() << "\n";

    return oss.str();
}

// 内部方法实现

// 处理地图特效
void Environment::ProcessMapEffects() {
    // 处理音乐区域
    if (m_mapFlags.hasMusic && m_mapFlags.musicID > 0) {
        // TODO: 发送音乐消息给区域内的玩家
        // 这里需要与音乐系统集成
    }

    // 处理黑暗和白天效果
    if (m_mapFlags.isDark || m_mapFlags.isDay) {
        // TODO: 发送光线效果消息给区域内的玩家
        // 这里需要与光线系统集成
    }
}

// 处理自动加减血
void Environment::ProcessAutoHP() {
    DWORD currentTime = GetCurrentTime();

    if (m_mapFlags.incHP && m_mapFlags.incHPTime > 0) {
        // 自动加血逻辑
        auto players = GetPlayersInRange(Point(m_width/2, m_height/2), std::max(m_width, m_height));
        for (auto& player : players) {
            if (player) {
                // TODO: 实现自动加血逻辑
                // player->IncHP(m_mapFlags.incHPPoint);
            }
        }
    }

    if (m_mapFlags.decHP && m_mapFlags.decHPTime > 0) {
        // 自动减血逻辑
        auto players = GetPlayersInRange(Point(m_width/2, m_height/2), std::max(m_width, m_height));
        for (auto& player : players) {
            if (player) {
                // TODO: 实现自动减血逻辑
                // player->DecHP(m_mapFlags.decHPPoint);
            }
        }
    }
}

// 处理自动加减金币
void Environment::ProcessAutoGameGold() {
    DWORD currentTime = GetCurrentTime();

    if (m_mapFlags.incGameGold && m_mapFlags.incGameGoldTime > 0) {
        // 自动加金币逻辑
        auto players = GetPlayersInRange(Point(m_width/2, m_height/2), std::max(m_width, m_height));
        for (auto& player : players) {
            if (player) {
                // TODO: 实现自动加金币逻辑
                // player->IncGameGold(m_mapFlags.incGameGoldValue);
            }
        }
    }

    if (m_mapFlags.decGameGold && m_mapFlags.decGameGoldTime > 0) {
        // 自动减金币逻辑
        auto players = GetPlayersInRange(Point(m_width/2, m_height/2), std::max(m_width, m_height));
        for (auto& player : players) {
            if (player) {
                // TODO: 实现自动减金币逻辑
                // player->DecGameGold(m_mapFlags.decGameGoldValue);
            }
        }
    }
}

// 处理自动加减点数
void Environment::ProcessAutoGamePoint() {
    DWORD currentTime = GetCurrentTime();

    if (m_mapFlags.incGamePoint && m_mapFlags.incGamePointTime > 0) {
        // 自动加点数逻辑
        auto players = GetPlayersInRange(Point(m_width/2, m_height/2), std::max(m_width, m_height));
        for (auto& player : players) {
            if (player) {
                // TODO: 实现自动加点数逻辑
                // player->IncGamePoint(m_mapFlags.incGamePointValue);
            }
        }
    }

    if (m_mapFlags.decGamePoint && m_mapFlags.decGamePointTime > 0) {
        // 自动减点数逻辑
        auto players = GetPlayersInRange(Point(m_width/2, m_height/2), std::max(m_width, m_height));
        for (auto& player : players) {
            if (player) {
                // TODO: 实现自动减点数逻辑
                // player->DecGamePoint(m_mapFlags.decGamePointValue);
            }
        }
    }
}

// 处理PK效果
void Environment::ProcessPKEffects() {
    // PK相关的地图效果处理
    if (m_mapFlags.pkWinLevel || m_mapFlags.pkWinExp || m_mapFlags.pkLostLevel || m_mapFlags.pkLostExp) {
        // TODO: 实现PK效果逻辑
        // 这里需要与PK系统集成，在PK事件发生时调用相应的处理
    }

    // 经验倍率处理
    if (m_mapFlags.expRate && m_mapFlags.expRateValue != 100) {
        // TODO: 实现经验倍率逻辑
        // 这里需要与经验系统集成
    }
}

// 更新禁用物品列表
void Environment::UpdateUnAllowItemsList() {
    std::lock_guard<std::mutex> lock(m_unAllowItemsMutex);

    m_unAllowStdItemsList.clear();
    m_unAllowStdItemsIdxList.clear();

    if (m_mapFlags.unAllowStdItemsText.empty()) {
        return;
    }

    // 解析禁用物品文本（格式：物品名1|物品名2|物品名3）
    std::string text = m_mapFlags.unAllowStdItemsText;
    size_t pos = 0;
    std::string delimiter = "|";

    while ((pos = text.find(delimiter)) != std::string::npos) {
        std::string itemName = text.substr(0, pos);
        if (!itemName.empty()) {
            // 去除首尾空格
            itemName.erase(0, itemName.find_first_not_of(" \t"));
            itemName.erase(itemName.find_last_not_of(" \t") + 1);

            if (!itemName.empty()) {
                m_unAllowStdItemsList.push_back(itemName);

                // TODO: 通过ItemManager获取物品索引
                // int itemIdx = ItemManager::GetStdItemIdx(itemName);
                // if (itemIdx >= 0) {
                //     m_unAllowStdItemsIdxList.push_back(itemIdx);
                // }
            }
        }
        text.erase(0, pos + delimiter.length());
    }

    // 处理最后一个物品名
    if (!text.empty()) {
        text.erase(0, text.find_first_not_of(" \t"));
        text.erase(text.find_last_not_of(" \t") + 1);

        if (!text.empty()) {
            m_unAllowStdItemsList.push_back(text);

            // TODO: 通过ItemManager获取物品索引
            // int itemIdx = ItemManager::GetStdItemIdx(text);
            // if (itemIdx >= 0) {
            //     m_unAllowStdItemsIdxList.push_back(itemIdx);
            // }
        }
    }
}

// 检查地图事件条件
bool Environment::CheckMapEventCondition(const MapEvent& event, const Point& pos) const {
    // 检查位置是否在事件范围内
    int distance = GetDistance(event.position, pos);
    if (distance > event.range) {
        return false;
    }

    // 检查时间间隔
    if (event.interval > 0) {
        DWORD currentTime = GetCurrentTime();
        if (currentTime - event.lastTriggerTime < event.interval) {
            return false;
        }
    }

    return true;
}

} // namespace MirServer
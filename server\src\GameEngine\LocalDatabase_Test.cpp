#include "LocalDatabase.h"
#include "../Common/Logger.h"
#include <iostream>
#include <memory>

using namespace MirServer;

// 简单的测试用例
void TestLocalDatabase() {
    std::cout << "=== LocalDatabase 功能测试 ===" << std::endl;

    // 创建LocalDatabase实例
    auto localDB = std::make_unique<LocalDatabase>();

    // 设置环境目录
    localDB->SetEnvironmentDir("./TestData/");

    // 测试初始化
    std::cout << "1. 测试初始化..." << std::endl;
    bool initResult = localDB->Initialize(nullptr, nullptr);
    std::cout << "   初始化结果: " << (initResult ? "成功" : "失败") << std::endl;

    // 测试加载方法
    std::cout << "\n2. 测试数据加载方法..." << std::endl;

    std::cout << "   - LoadNPCs: " << (localDB->LoadNPCs() ? "成功" : "失败") << std::endl;
    std::cout << "   - LoadMerchant: " << (localDB->LoadMerchant() ? "成功" : "失败") << std::endl;
    std::cout << "   - LoadStartPoint: " << (localDB->LoadStartPoint() ? "成功" : "失败") << std::endl;
    std::cout << "   - LoadMinMap: " << (localDB->LoadMinMap() ? "成功" : "失败") << std::endl;
    std::cout << "   - LoadMapQuest: " << (localDB->LoadMapQuest() ? "成功" : "失败") << std::endl;
    std::cout << "   - LoadQuestDiary: " << (localDB->LoadQuestDiary() ? "成功" : "失败") << std::endl;
    std::cout << "   - LoadUnbindList: " << (localDB->LoadUnbindList() ? "成功" : "失败") << std::endl;
    std::cout << "   - LoadMonGen: " << (localDB->LoadMonGen() ? "成功" : "失败") << std::endl;

    // 测试脚本加载
    std::cout << "\n3. 测试脚本加载..." << std::endl;
    bool scriptResult = localDB->LoadScriptFile("测试NPC", "./Scripts", "test_npc", true);
    std::cout << "   LoadScriptFile: " << (scriptResult ? "成功" : "失败") << std::endl;

    // 测试查询接口
    std::cout << "\n4. 测试查询接口..." << std::endl;

    auto npcInfo = localDB->GetNPCInfo("测试NPC");
    std::cout << "   GetNPCInfo: " << (npcInfo ? "找到NPC" : "未找到NPC") << std::endl;

    auto& startPoints = localDB->GetStartPoints();
    std::cout << "   GetStartPoints: 找到 " << startPoints.size() << " 个起始点" << std::endl;

    auto& minMaps = localDB->GetMinMaps();
    std::cout << "   GetMinMaps: 找到 " << minMaps.size() << " 个小地图" << std::endl;

    auto& mapQuests = localDB->GetMapQuests();
    std::cout << "   GetMapQuests: 找到 " << mapQuests.size() << " 个地图任务" << std::endl;

    auto& questDiaries = localDB->GetQuestDiaries();
    std::cout << "   GetQuestDiaries: 找到 " << questDiaries.size() << " 个任务日记" << std::endl;

    auto& unbindItems = localDB->GetUnbindItems();
    std::cout << "   GetUnbindItems: 找到 " << unbindItems.size() << " 个解绑物品" << std::endl;

    auto& monGens = localDB->GetMonGens();
    std::cout << "   GetMonGens: 找到 " << monGens.size() << " 个怪物生成点" << std::endl;

    // 测试统计信息
    std::cout << "\n5. 测试统计信息..." << std::endl;
    auto stats = localDB->GetStatistics();
    std::cout << "   NPC数量: " << stats.npcCount << std::endl;
    std::cout << "   起始点数量: " << stats.startPointCount << std::endl;
    std::cout << "   小地图数量: " << stats.minMapCount << std::endl;
    std::cout << "   地图任务数量: " << stats.mapQuestCount << std::endl;
    std::cout << "   任务日记数量: " << stats.questDiaryCount << std::endl;
    std::cout << "   解绑物品数量: " << stats.unbindItemCount << std::endl;
    std::cout << "   怪物生成点数量: " << stats.monGenCount << std::endl;

    // 测试缓存管理
    std::cout << "\n6. 测试缓存管理..." << std::endl;
    size_t cacheSize = localDB->GetCacheSize();
    std::cout << "   缓存大小: " << cacheSize << " 字节" << std::endl;

    localDB->ClearCache();
    std::cout << "   清理缓存: 完成" << std::endl;

    localDB->RefreshCache();
    std::cout << "   刷新缓存: 完成" << std::endl;

    // 测试重新加载
    std::cout << "\n7. 测试重新加载..." << std::endl;
    localDB->ReloadMerchants();
    std::cout << "   ReloadMerchants: 完成" << std::endl;

    localDB->ReloadNPC();
    std::cout << "   ReloadNPC: 完成" << std::endl;

    localDB->ReloadAll();
    std::cout << "   ReloadAll: 完成" << std::endl;

    // 清理
    localDB->Finalize();
    std::cout << "\n8. 清理完成" << std::endl;

    std::cout << "\n=== 测试完成 ===" << std::endl;
}

int main() {
    // 初始化日志系统
    Logger::SetLogFile("LocalDatabase_Test.log");
    Logger::SetLogLevel(LogLevel::LOG_INFO);

    try {
        TestLocalDatabase();
    }
    catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}

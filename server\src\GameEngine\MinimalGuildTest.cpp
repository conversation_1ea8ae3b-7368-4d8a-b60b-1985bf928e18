#include "GuildManager.h"
#include "../Common/Logger.h"
#include <iostream>
#include <memory>

using namespace MirServer;

int main() {
    std::cout << "=== Minimal Guild System Test ===" << std::endl;
    
    try {
        // 初始化日志系统
        std::cout << "1. Initializing logger..." << std::endl;
        Logger::SetLogFile("minimal_guild_test.log");
        Logger::SetLogLevel(LogLevel::LOG_DEBUG);
        std::cout << "   Logger initialized." << std::endl;

        // 测试1: 创建Guild对象
        std::cout << "2. Testing Guild object creation..." << std::endl;
        auto guild = std::make_unique<Guild>("TestGuild");
        if (!guild) {
            std::cout << "   ERROR: Guild creation failed" << std::endl;
            return 1;
        }
        std::cout << "   Guild object created successfully" << std::endl;

        // 测试2: 获取行会名称
        std::cout << "3. Testing guild name retrieval..." << std::endl;
        std::string guildName = guild->GetGuildName();
        std::cout << "   Guild name: " << guildName << std::endl;

        // 测试3: 设置行会属性
        std::cout << "4. Testing guild properties..." << std::endl;
        guild->SetBuildPoint(1000);
        guild->SetAurae(500);
        guild->SetStability(800);
        guild->SetFlourishing(600);
        
        std::cout << "   Build Point: " << guild->GetBuildPoint() << std::endl;
        std::cout << "   Aurae: " << guild->GetAurae() << std::endl;
        std::cout << "   Stability: " << guild->GetStability() << std::endl;
        std::cout << "   Flourishing: " << guild->GetFlourishing() << std::endl;

        // 测试4: 行会公告
        std::cout << "5. Testing guild notices..." << std::endl;
        guild->AddNotice("Welcome to our guild!");
        guild->AddNotice("Guild event tonight at 8 PM");
        
        const auto& notices = guild->GetNotices();
        std::cout << "   Notice count: " << notices.size() << std::endl;
        for (size_t i = 0; i < notices.size(); ++i) {
            std::cout << "     " << (i + 1) << ". " << notices[i] << std::endl;
        }

        // 测试5: 清除公告
        std::cout << "6. Testing notice clearing..." << std::endl;
        guild->ClearNotices();
        const auto& clearedNotices = guild->GetNotices();
        std::cout << "   Notices after clearing: " << clearedNotices.size() << std::endl;

        // 测试6: 团队战功能
        std::cout << "7. Testing team fight features..." << std::endl;
        guild->StartTeamFight();
        std::cout << "   Team fight started: " << (guild->IsTeamFightActive() ? "Active" : "Inactive") << std::endl;
        
        guild->TeamFightWhoWinPoint("TestPlayer1", 100);
        std::cout << "   Contest points: " << guild->GetContestPoint() << std::endl;
        
        guild->EndTeamFight();
        std::cout << "   Team fight ended: " << (guild->IsTeamFightActive() ? "Active" : "Inactive") << std::endl;

        // 测试7: 成员计数
        std::cout << "8. Testing member count..." << std::endl;
        int memberCount = guild->GetMemberCount();
        std::cout << "   Member count: " << memberCount << std::endl;
        std::cout << "   Is full: " << (guild->IsFull() ? "Yes" : "No") << std::endl;

        // 测试8: 设置行会信息
        std::cout << "9. Testing guild info setup..." << std::endl;
        bool setupResult = guild->SetGuildInfo("TestChief");
        std::cout << "   Guild info setup: " << (setupResult ? "Success" : "Failed") << std::endl;
        
        if (setupResult) {
            std::string chiefName = guild->GetChiefName();
            std::cout << "   Chief name: " << chiefName << std::endl;
            std::cout << "   Member count after setup: " << guild->GetMemberCount() << std::endl;
        }

        // 测试9: 创建第二个行会测试联盟和战争
        std::cout << "10. Testing guild alliance and war..." << std::endl;
        auto guild2 = std::make_unique<Guild>("TestGuild2");
        guild2->SetGuildInfo("TestChief2");
        
        // 测试联盟
        bool allyResult = guild->AddAlly(guild2.get());
        std::cout << "    Alliance creation: " << (allyResult ? "Success" : "Failed") << std::endl;
        std::cout << "    Is ally: " << (guild->IsAlly(guild2.get()) ? "Yes" : "No") << std::endl;
        
        // 解除联盟
        bool removeAllyResult = guild->RemoveAlly(guild2.get());
        std::cout << "    Alliance removal: " << (removeAllyResult ? "Success" : "Failed") << std::endl;
        
        // 测试战争
        bool warResult = guild->StartWar(guild2.get());
        std::cout << "    War declaration: " << (warResult ? "Success" : "Failed") << std::endl;
        std::cout << "    Is at war: " << (guild->IsWarWith(guild2.get()) ? "Yes" : "No") << std::endl;
        
        // 停战
        bool peaceResult = guild->EndWar(guild2.get());
        std::cout << "    Peace declaration: " << (peaceResult ? "Success" : "Failed") << std::endl;

        // 测试10: 运行时处理
        std::cout << "11. Testing runtime processing..." << std::endl;
        guild->Run();
        std::cout << "    Guild runtime processing completed" << std::endl;

        // 清理
        std::cout << "12. Cleaning up..." << std::endl;
        guild.reset();
        guild2.reset();
        std::cout << "    Cleanup completed" << std::endl;

        std::cout << "\n=== All Minimal Tests Passed Successfully! ===" << std::endl;
        std::cout << "Guild system core functionality is working correctly." << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception occurred: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown exception occurred" << std::endl;
        return 1;
    }
}

cmake_minimum_required(VERSION 3.16)
project(MirServer VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 添加cmake模块路径
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/cmake")

# 输出目录设置
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/build/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/build/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/build/lib)

# 编译选项
if(WIN32)
    add_definitions(-DWIN32_LEAN_AND_MEAN -DNOMINMAX)
    if(MSVC)
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /utf-8")
    endif()
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -pthread")
endif()

# Debug模式设置
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_definitions(-DDEBUG -D_DEBUG)
    if(NOT WIN32)
        set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0")
    endif()
endif()

# 查找依赖包
find_package(Threads REQUIRED)

# 包含目录
include_directories(src)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../common)

# 子目录
add_subdirectory(src)

# 添加PK系统测试
add_executable(PKSystemTest
    test_pk_system.cpp
    src/GameEngine/PKManager.cpp
    src/Common/Logger.cpp
    src/Common/Utils.cpp
)

target_include_directories(PKSystemTest PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/src
)

target_link_libraries(PKSystemTest PRIVATE
    Common
    Protocol
    BaseObject
    ${CMAKE_THREAD_LIBS_INIT}
)

if(WIN32)
    target_link_libraries(PKSystemTest PRIVATE ws2_32)
endif()

set_target_properties(PKSystemTest PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

# 添加简化PK系统测试
add_executable(SimplePKTest
    simple_pk_test.cpp
    src/GameEngine/PKManager.cpp
    src/Common/Utils.cpp
    src/Common/Logger.cpp
)

target_include_directories(SimplePKTest PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/src
)

target_link_libraries(SimplePKTest PRIVATE
    ${CMAKE_THREAD_LIBS_INIT}
)

if(WIN32)
    target_link_libraries(SimplePKTest PRIVATE ws2_32)
endif()

set_target_properties(SimplePKTest PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

# 添加最小化PK系统测试
add_executable(MinimalPKTest
    minimal_pk_test.cpp
    src/GameEngine/SimplePKManager.cpp
    src/Common/Utils.cpp
)

target_include_directories(MinimalPKTest PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/src
)

target_link_libraries(MinimalPKTest PRIVATE
    ${CMAKE_THREAD_LIBS_INIT}
)

if(WIN32)
    target_link_libraries(MinimalPKTest PRIVATE ws2_32)
endif()

set_target_properties(MinimalPKTest PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

# 添加简单测试
add_executable(PKTest
    simple_test.cpp
    src/GameEngine/SimplePKManager.cpp
)

target_include_directories(PKTest PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/src
)

target_link_libraries(PKTest PRIVATE
    ${CMAKE_THREAD_LIBS_INIT}
)

if(WIN32)
    target_link_libraries(PKTest PRIVATE ws2_32)
endif()

set_target_properties(PKTest PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

# 安装规则
if(WIN32)
    install(TARGETS GameEngine LoginServer DBServer
            RUNTIME DESTINATION bin
            LIBRARY DESTINATION lib
            ARCHIVE DESTINATION lib)
else()
    install(TARGETS GameEngine LoginServer DBServer
            RUNTIME DESTINATION bin
            LIBRARY DESTINATION lib
            ARCHIVE DESTINATION lib)
endif()

# 配置文件安装
install(DIRECTORY config/ DESTINATION config FILES_MATCHING PATTERN "*.ini" PATTERN "*.json")
install(DIRECTORY scripts/ DESTINATION scripts FILES_MATCHING PATTERN "*.txt")
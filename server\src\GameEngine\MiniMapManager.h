#pragma once

#include "../Common/Types.h"
#include "../Common/GameData.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <shared_mutex>

namespace MirServer {

class PlayObject;

// 小地图数据结构
struct MiniMapData {
    std::string mapName;                    // 地图名称
    int width;                              // 地图宽度
    int height;                             // 地图高度
    std::vector<uint8_t> imageData;         // 小地图图像数据
    std::vector<Point> landmarks;           // 地标位置
    std::vector<Point> npcs;                // NPC位置
    std::vector<Point> portals;             // 传送门位置
    DWORD lastUpdateTime = 0;               // 最后更新时间
    bool generated = false;                 // 是否已生成
};

// 地图标记类型
enum class MapMarkType {
    PLAYER = 1,         // 玩家位置
    PARTY_MEMBER = 2,   // 队友位置
    NPC = 3,            // NPC位置
    PORTAL = 4,         // 传送门
    LANDMARK = 5,       // 地标
    QUEST_TARGET = 6,   // 任务目标
    MONSTER = 7         // 怪物位置
};

// 地图标记结构
struct MapMark {
    MapMarkType type;                       // 标记类型
    Point position;                         // 位置
    std::string name;                       // 名称
    std::string description;                // 描述
    DWORD color = 0xFFFFFFFF;              // 颜色
    bool visible = true;                    // 是否可见
    DWORD expireTime = 0;                  // 过期时间（0表示永不过期）
};

// 小地图管理器类（对应delphi的小地图系统）
class MiniMapManager {
public:
    MiniMapManager();
    ~MiniMapManager();

    // 初始化和清理
    bool Initialize();
    void Finalize();

    // 小地图数据管理
    bool LoadMiniMapData(const std::string& mapName);
    bool GenerateMiniMapData(const std::string& mapName);
    const MiniMapData* GetMiniMapData(const std::string& mapName) const;
    std::vector<uint8_t> GetMiniMapImage(const std::string& mapName) const;

    // 玩家小地图请求
    bool SendMiniMapToPlayer(PlayObject* player, const std::string& mapName);
    bool UpdatePlayerMiniMap(PlayObject* player);

    // 地图标记管理
    void AddMapMark(const std::string& mapName, const MapMark& mark);
    void RemoveMapMark(const std::string& mapName, MapMarkType type, const Point& position);
    void ClearMapMarks(const std::string& mapName, MapMarkType type);
    std::vector<MapMark> GetMapMarks(const std::string& mapName, MapMarkType type) const;

    // 玩家位置更新
    void UpdatePlayerPosition(PlayObject* player, const Point& position);
    void UpdatePartyMemberPositions(PlayObject* player);

    // NPC和传送门标记
    void AddNPCMark(const std::string& mapName, const Point& position, const std::string& npcName);
    void AddPortalMark(const std::string& mapName, const Point& position, const std::string& targetMap);
    void AddLandmark(const std::string& mapName, const Point& position, const std::string& name);

    // 任务相关标记
    void AddQuestMark(const std::string& mapName, const Point& position, const std::string& questName);
    void RemoveQuestMark(const std::string& mapName, const Point& position);

    // 运行时更新
    void Run();
    void ProcessMiniMaps();
    void CleanupExpiredMarks();

    // 配置管理
    void SetMiniMapEnabled(bool enabled) { m_enabled = enabled; }
    bool IsMiniMapEnabled() const { return m_enabled; }
    void SetUpdateInterval(DWORD interval) { m_updateInterval = interval; }
    void SetMaxCacheSize(size_t maxSize) { m_maxCacheSize = maxSize; }

    // 统计信息
    struct Statistics {
        int totalMaps = 0;
        int generatedMaps = 0;
        int activeMarks = 0;
        size_t cacheSize = 0;
        DWORD lastUpdateTime = 0;
    };

    const Statistics& GetStatistics() const { return m_statistics; }
    void UpdateStatistics();

private:
    bool m_initialized = false;
    bool m_enabled = true;

    // 小地图数据缓存 (地图名 -> 小地图数据)
    std::unordered_map<std::string, std::unique_ptr<MiniMapData>> m_miniMaps;
    mutable std::shared_mutex m_miniMapsMutex;

    // 地图标记 (地图名 -> 标记列表)
    std::unordered_map<std::string, std::vector<MapMark>> m_mapMarks;
    mutable std::shared_mutex m_marksMutex;

    // 配置
    DWORD m_updateInterval = 5000;          // 更新间隔（5秒）
    size_t m_maxCacheSize = 50;             // 最大缓存地图数
    DWORD m_markExpireTime = 300000;        // 标记过期时间（5分钟）
    int m_miniMapScale = 4;                 // 小地图缩放比例

    // 运行时状态
    DWORD m_lastProcessTime = 0;
    DWORD m_lastCleanupTime = 0;

    // 统计信息
    Statistics m_statistics;

    // 内部方法
    bool LoadMiniMapFromFile(const std::string& mapName, MiniMapData& miniMap);
    bool SaveMiniMapToFile(const std::string& mapName, const MiniMapData& miniMap);
    bool GenerateMiniMapFromMapData(const std::string& mapName, MiniMapData& miniMap);
    void CompressMiniMapData(std::vector<uint8_t>& data);
    void DecompressMiniMapData(std::vector<uint8_t>& data);

    // 标记管理
    void AddMarkInternal(const std::string& mapName, const MapMark& mark);
    void RemoveExpiredMarks(const std::string& mapName);
    void LimitCacheSize();

    // 数据生成
    uint8_t GetPixelColor(const std::string& mapName, int x, int y) const;
    void GenerateMapMarks(const std::string& mapName, MiniMapData& miniMap);

    // 网络发送
    void SendMiniMapPacket(PlayObject* player, const MiniMapData& miniMap);
    void SendMapMarksPacket(PlayObject* player, const std::string& mapName);
};

// 全局小地图管理器实例
extern std::unique_ptr<MiniMapManager> g_MiniMapManager;

} // namespace MirServer
